package com.justplayapps.proxybase.paypal.dto

import com.paypal.http.annotations.Model
import com.paypal.http.annotations.SerializedName

@Model
class TransactionDetailsItem {

  @SerializedName("transaction_info")
  private var transactionInfo: TransactionInfo? = null

  fun transactionInfo(): TransactionInfo? {
    return transactionInfo
  }

  fun transactionInfo(transactionInfo: TransactionInfo?): TransactionDetailsItem {
    this.transactionInfo = transactionInfo
    return this
  }

  @SerializedName("payer_info")
  private var payerInfo: PayerInfo? = null

  fun payerInfo(): PayerInfo? {
    return payerInfo
  }

  fun payerInfo(payerInfo: PayerInfo?): TransactionDetailsItem {
    this.payerInfo = payerInfo
    return this
  }

  override fun toString(): String {
    return "TransactionDetailsItem(transactionInfo=$transactionInfo, payerInfo=$payerInfo)"
  }

  /*@SerializedName("shipping_info")
  @SerializedName("cart_info")
  @SerializedName("store_info")
  @SerializedName("auction_info")
  @SerializedName("incentive_info")
  */


}