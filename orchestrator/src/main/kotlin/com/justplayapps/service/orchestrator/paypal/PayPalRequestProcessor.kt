package com.justplayapps.service.orchestrator.paypal

import com.google.inject.Inject
import com.google.inject.Singleton
import com.moregames.base.util.logger
import io.ktor.http.*

@Singleton
class PayPalRequestProcessor @Inject constructor() {

  fun verifyWebhook(headers: Headers, payload: String): Boolean {
    logger().info("PayPal webhook headers: $headers")
    //todo not yet implemented
    //use mock for testing
    return true
  }

}