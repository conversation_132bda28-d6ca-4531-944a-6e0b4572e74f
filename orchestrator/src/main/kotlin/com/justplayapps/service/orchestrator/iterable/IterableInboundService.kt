package com.justplayapps.service.orchestrator.iterable

import com.google.inject.Inject
import com.google.inject.Singleton
import com.justplayapps.orchestrator.events.iterableAddCoinsEvent
import com.justplayapps.service.orchestrator.dto.IterableAddCoinsRequestApiDto
import com.justplayapps.service.orchestrator.dto.UserSearchResult
import com.justplayapps.service.orchestrator.tracking.TrackingService
import com.justplayapps.service.orchestrator.util.OrchestratorGenericMessagePublisher
import com.justplayapps.service.orchestrator.util.OrchestratorSecrets
import com.moregames.base.secret.SecretService

@Singleton
class IterableInboundService @Inject constructor(
  private val secretService: SecretService,
  private val messagePublisher: OrchestratorGenericMessagePublisher,
  private val trackingService: TrackingService,
  ) {

  companion object {
    const val ADD_COINS_TOPIC_NAME = "iterable-add-coins-event"
  }

  suspend fun apiKeyIsValid(apiKey: String?): Boolean {
    if (apiKey.isNullOrBlank()) return false
    val secret = secretService.secretValue(OrchestratorSecrets.JUSTPLAY_ITERABLE_API_KEY)
    return secret == apiKey
  }

  suspend fun publishAddCoinsMessage(user: UserSearchResult, request: IterableAddCoinsRequestApiDto) {
    val message = iterableAddCoinsEvent {
      userId = request.userId
      eventId = request.eventId
    }
    messagePublisher.publishCrossProject(
      message = message,
      topicName = "iterable-add-coins-event",
      projectName = trackingService.getProjectNameByMarket(user.market),
      env = trackingService.getBuildVariantByMarket(user.market)
    )
  }
}