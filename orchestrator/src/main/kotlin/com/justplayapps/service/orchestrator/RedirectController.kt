package com.justplayapps.service.orchestrator

import com.google.inject.Inject
import com.google.protobuf.util.JsonFormat
import com.justplayapps.games.common.Common.UserIds
import com.justplayapps.games.common.idfaOrNull
import com.justplayapps.games.common.idfvOrNull
import com.justplayapps.games.common.userIdOrNull
import com.justplayapps.games.status.GameStatus.*
import com.justplayapps.service.orchestrator.ApiManager.Companion.REDIRECT_BASIC_AUTH_CFG
import com.justplayapps.service.orchestrator.dto.GameExaminationDto
import com.justplayapps.service.orchestrator.dto.GameExaminationRequestApiDto
import com.justplayapps.service.orchestrator.dto.UserSearchResult
import com.justplayapps.service.orchestrator.iosgameattconsent.IosGameAttConsentService
import com.justplayapps.service.orchestrator.iosgameattconsent.IosGameAttConsentTrackApiDto
import com.justplayapps.service.orchestrator.iosgameattconsent.IosGameAttConsentTrackingData
import com.justplayapps.service.orchestrator.tracking.*
import com.justplayapps.service.orchestrator.util.OrchestratorGenericMessagePublisher
import com.justplayapps.service.orchestrator.util.RedirectUrlService
import com.justplayapps.service.orchestrator.webclient.*
import com.moregames.base.config.ServicesRegistry.GAME_PROGRESS
import com.moregames.base.config.ServicesRegistry.PLAYTIME
import com.moregames.base.dto.GameExaminationRequestRouteDto
import com.moregames.base.dto.GenericPayloadApiDto
import com.moregames.base.exceptions.ParameterRequiredException
import com.moregames.base.messaging.WebhookPublisher
import com.moregames.base.messaging.dto.WebAppUserAdditionalDataEventDto
import com.moregames.base.messaging.dto.WebAppUserJailBreakCheckEventDto
import com.moregames.base.messaging.dto.WebAppUserRegistrationEventDto
import com.moregames.base.messaging.dto.WebAppVerificationGpsLocationCheckEventDto
import com.moregames.base.user.dto.WebUserStatusDto
import com.moregames.base.util.*
import com.moregames.base.util.Constants.JP_PLATFORM
import com.moregames.base.vpn.CountryCodeMissingException
import com.moregames.base.vpn.IpMissingException
import com.moregames.base.vpn.SanityCheckRequestDto
import com.moregames.base.vpn.SanityCheckResponseApiDto
import io.ktor.application.*
import io.ktor.auth.*
import io.ktor.client.*
import io.ktor.client.request.*
import io.ktor.http.*
import io.ktor.http.HttpStatusCode.Companion.NotFound
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.request.*
import io.ktor.response.*
import io.ktor.routing.*
import io.ktor.util.pipeline.*
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.util.*


class RedirectController @Inject constructor(
  private val trackingService: TrackingService,
  private val webhookPublisher: WebhookPublisher,
  private val jsonConverter: Json,
  private val userService: UserService,
  private val redirectUrlService: RedirectUrlService,
  private val iosGameAttConsentService: IosGameAttConsentService,
  private val timeService: TimeService,
  @CrossServiceHttpClient private val httpClient: HttpClient,
  private val messagePublisher: OrchestratorGenericMessagePublisher
) {

  companion object {
    const val GAID_PARAM = "gaid"
    const val GOOGLE_AD_ID_PARAM = "googleAdId"
    const val IDFV_PARAM = "idfv"
    const val IDFA_PARAM = "idfa"
    const val PACKAGE_ID_PARAM = "packageId"
    const val VERIFICATION_SESSION_ID_PARAM = "verificationSessionId"
    const val USER_ID_PARAM = "userId"
    const val GAME_VERSION_PARAM = "gameVersion"

    private val protoJsonParser = JsonFormat.parser().ignoringUnknownFields()
    private val protoJsonPrinter = JsonFormat.printer().omittingInsignificantWhitespace()
  }

  context(Routing)
  operator fun invoke() = authenticate(REDIRECT_BASIC_AUTH_CFG) {
    //region supporting JS script endpoints
    get("/get/userStatus") {
      val userId = extractRequiredParameter(USER_ID_PARAM)
      val userEmailBase64Parameter = call.parameters["userEmailBase64"]?.let {
        // reader may think - are you kidding me? - but this is a real case
        // when ktor deserializes url - it replaces '+' with ' ' and we need to revert it back, so that it gets base64 decoded correctly in playtime
        // I know that sounds insane and pretty stupid, but that's how it works :(
        mapOf("userEmailBase64" to it.replace(" ", "+"))
      }.orEmpty()
      val user = userService.findUser(UserId(userId)) ?: run {
        call.respond(NotFound)
        return@get
      }
      redirectUrlService(PLAYTIME, user, "/ext/status", userEmailBase64Parameter) { call.redirect() }
    }
    get("/get/banStatus") {
      val userId = extractRequiredParameter(USER_ID_PARAM)
      val user = userService.findUser(UserId(userId)) ?: run {
        call.respond(NotFound)
        return@get
      }
      redirectUrlService(PLAYTIME, user, "/ext/banStatus") { call.redirect() }
    }
    //endregion

    //region games common endpoints (games.justplayapi.com)
    get("/get/sanityCheck") {
      val request = extractSanityCheckParams(call.request)
      if (request.packageId.isNullOrBlank() && request.vpnDetected) {
        call.respond(
          SanityCheckResponseApiDto(
            isValidUser = false,
            isFromAllowedCountry = true,
            isUsingVpn = true,
            fraudScore = 0
          )
        )
      } else {
        userService.findUser(UserId(request.userId), IDFA(request.googleAdId), IDFV(request.idfv))
          ?.let { user ->
            redirectUrlService(
              PLAYTIME, user, "/ext/sanityCheck", mapOf(
                "sanityCheckRequest" to Base64.getEncoder().encodeToString(jsonConverter.encodeToString(request).toByteArray())
              )
            ) { call.redirect() }
          } ?: if (request.vpnDetected && request.packageId.orEmpty().contains("gimica"))
          call.respond(
            SanityCheckResponseApiDto(
              isValidUser = false,
              isFromAllowedCountry = true,
              isUsingVpn = true,
              fraudScore = 0
            )
          ) else call.respond(
          SanityCheckResponseApiDto(
            isValidUser = true,
            isFromAllowedCountry = true,
            isUsingVpn = false,
            fraudScore = 0
          )
        )
      }
    }
    get("/get/adUnitIdsByGoogleAdId") {
      val googleAdId = extractRequiredParameter(GOOGLE_AD_ID_PARAM)
      val packageId = extractRequiredParameter(PACKAGE_ID_PARAM)
      val user = userService.findUser(idfa = IDFA(googleAdId)) ?: run {
        call.respond(NotFound)
        return@get
      }
      redirectUrlService(PLAYTIME, user, "/ext/adUnitIds", mapOf("packageId" to packageId)) { call.redirect() }
    }
    get("/get/adUnitIds") {
      val packageId = extractRequiredParameter(PACKAGE_ID_PARAM)
      val user = user() ?: run {
        call.respond(NotFound)
        return@get
      }
      redirectUrlService(PLAYTIME, user, "/ext/adUnitIds", mapOf("packageId" to packageId)) { call.redirect() }
    }
    get("/get/shouldShowEarlyInterstitial") {
      val user = user() ?: run {
        call.respond(NotFound)
        return@get
      }
      redirectUrlService(PLAYTIME, user, "/ext/shouldShowEarlyInterstitial") { call.redirect() }
    }
    get("/get/gdprState") {
      val user = user() ?: run {
        call.respond(NotFound)
        return@get
      }
      redirectUrlService(PLAYTIME, user, "/ext/gdprState") { call.redirect() }
    }
    get("/post/game-key") {
      val applicationId = call.parameters["applicationId"]
      val publicKey = call.parameters["publicKey"]
      if (applicationId.isNullOrBlank() || publicKey.isNullOrBlank()) {
        throw ParameterRequiredException("applicationId and publicKey", call.request.queryString())
      }
      val user = user() ?: run {
        call.respond(NotFound)
        return@get
      }
      redirectUrlService(
        service = GAME_PROGRESS,
        user = user,
        path = "/games/keys",
        additionalParams = mapOf("applicationId" to applicationId, JP_PLATFORM to user.platform.name, "publicKey" to publicKey)
      ) { call.redirect() }
    }
    //endregion

    //region Android games endpoints (games.justplayapi.com)
    route("/android") {
      get("/status") {
        val packageId = extractRequiredParameter(PACKAGE_ID_PARAM)
        val user = user() ?: run {
          call.respond(NotFound)
          return@get
        }
        redirectUrlService(PLAYTIME, user, "/ext/games/android/status", mapOf("packageId" to packageId)) { call.redirect() }
      }
      route("/examination") {
        get {
          val packageId = extractRequiredParameter(PACKAGE_ID_PARAM)
          val user = user() ?: run {
            call.respond(NotFound)
            return@get
          }
          redirectUrlService(PLAYTIME, user, "/ext/games/android/examination", mapOf("packageId" to packageId)) { call.redirect() }
        }
        post {
          val examinationRequest = call.receive<GameExaminationRequestApiDto>()

          with(examinationRequest) {
            val user = userService.findUser(UserId(userId), IDFA(idfa), IDFV(idfv)) ?: run {
              call.respond(NotFound)
              return@post
            }

            logger().info("posted android game examination: $examinationRequest for $user")

            webhookPublisher.publish(
              GameExaminationDto(
                gameExaminationRequest = GameExaminationRequestRouteDto(
                  userId = user.userId,
                  packageId = examinationRequest.packageId,
                  attestationStatement = examinationRequest.attestationStatement,
                ),
                marketServiceAddress = trackingService.getServiceAddressByMarket(user.market, PLAYTIME),
                jsonConverter = jsonConverter,
              )
            )

            call.respond(OK)
          }
        }
      }

      get("/coins-booster-configuration") {
        val packageId = extractRequiredParameter(PACKAGE_ID_PARAM)
        val gameVersion = extractRequiredParameter(GAME_VERSION_PARAM)
        val user = user() ?: run {
          call.respond(NotFound)
          return@get
        }
        redirectUrlService(
          service = PLAYTIME,
          user = user,
          path = "/ext/games/android/coins-booster-configuration",
          additionalParams = mapOf(PACKAGE_ID_PARAM to packageId, GAME_VERSION_PARAM to gameVersion)
        ) { call.redirect() }
      }

      get("/celebration-configuration") {
        val packageId = extractRequiredParameter(PACKAGE_ID_PARAM)
        val gameVersion = extractRequiredParameter(GAME_VERSION_PARAM)
        val user = user() ?: run {
          call.respond(NotFound)
          return@get
        }
        redirectUrlService(
          service = PLAYTIME,
          user = user,
          path = "/ext/games/android/celebration-configuration",
          additionalParams = mapOf(PACKAGE_ID_PARAM to packageId, GAME_VERSION_PARAM to gameVersion)
        ) { call.redirect() }
      }

      post("/status/v2") {
        val request = call.receiveText()
        val message = GameStatusRequest.newBuilder().also { protoJsonParser.merge(request, it) }.build()

        val user = userService.findUser(message.user.userId(), message.user.idfa(), message.user.idfv()) ?: run {
          logger().warn(
            "[android/status/v2] Can't determine user and market by userId: '{}', idfa: '{}', idfv: '{}'",
            message.user.userIdOrNull?.value, message.user.idfaOrNull?.value, message.user.idfvOrNull?.value
          )
          call.respond(NotFound)
          return@post
        }

        val baseUrl = trackingService.getServiceAddressByMarket(user.market, PLAYTIME)
        val response = httpClient.post<GenericPayloadApiDto>("https://$baseUrl/service/ext/games/android/${user.userId}/status") {
          contentType(ContentType.Application.Json)
          body = GenericPayloadApiDto(message.encodeToBase64())
        }
        call.respond(response.payload.fromBase64(GameStatusResponse.parser()).let { protoJsonPrinter.print(it) })
      }
      post("/first-launch") {
        val request = call.receiveText()
        val message = FirstLaunchRequest.newBuilder().also { protoJsonParser.merge(request, it) }.build()

        val user = userService.findUser(message.user.userId(), message.user.idfa(), message.user.idfv()) ?: run {
          logger().warn(
            "[android/first-launch] Can't determine user and market by userId: '{}', idfa: '{}', idfv: '{}'",
            message.user.userIdOrNull?.value, message.user.idfaOrNull?.value, message.user.idfvOrNull?.value
          )
          call.respond(NotFound)
          return@post
        }

        val baseUrl = trackingService.getServiceAddressByMarket(user.market, PLAYTIME)
        httpClient.post<String>("https://$baseUrl/service/ext/games/android/${user.userId}/first-launch") {
          contentType(ContentType.Application.Json)
          body = GenericPayloadApiDto(message.encodeToBase64())
        }
        call.respond(OK)
      }
    }
    //endregion

    //region iOS games endpoints (games.justplayapi.com)
    route("/ios") {
      get("/status") {
        val packageId = extractRequiredParameter(PACKAGE_ID_PARAM)
        val user = user() ?: run {
          call.respond(NotFound)
          return@get
        }
        redirectUrlService(PLAYTIME, user, "/ext/games/ios/status", mapOf("packageId" to packageId)) { call.redirect() }
      }
      get("/att-consent-configuration") {
        val idfv = extractRequiredParameter(IDFV_PARAM)
        val packageId = extractRequiredParameter(PACKAGE_ID_PARAM)
        val gameVersion = extractRequiredParameter(GAME_VERSION_PARAM)
        val user = userService.findUser(idfv = IDFV(idfv)) ?: run {
          call.respond(NotFound)
          return@get
        }
        redirectUrlService(
          service = PLAYTIME,
          user = user,
          path = "/ext/games/ios/att-consent-configuration",
          additionalParams = mapOf("idfv" to idfv, "packageId" to packageId, "gameVersion" to gameVersion)
        ) { call.redirect() }
      }
      post("/track-consent") {
        val idfv = extractRequiredParameter(IDFV_PARAM)
        val packageId = extractRequiredParameter(PACKAGE_ID_PARAM)
        val gameVersion = extractRequiredParameter(GAME_VERSION_PARAM)
        val request = call.receive<IosGameAttConsentTrackApiDto>()
        val user = userService.findUser(idfv = IDFV(idfv)) ?: run {
          call.respond(NotFound)
          return@post
        }

        val consentTrackingData = IosGameAttConsentTrackingData(
          userId = user.userId,
          idfv = idfv,
          idfa = call.parameters[IDFA_PARAM],
          packageId = packageId,
          gameVersion = gameVersion,
          status = request.status,
          market = user.market,
          receivedAt = timeService.now()
        )
        iosGameAttConsentService.trackConsentStatus(consentTrackingData)
        call.respond(OK)
      }

      get("/coins-booster-configuration") {
        val idfv = extractRequiredParameter(IDFV_PARAM)
        val packageId = extractRequiredParameter(PACKAGE_ID_PARAM)
        val gameVersion = extractRequiredParameter(GAME_VERSION_PARAM)
        val user = userService.findUser(idfv = IDFV(idfv)) ?: run {
          call.respond(NotFound)
          return@get
        }
        redirectUrlService(
          service = PLAYTIME,
          user = user,
          path = "/ext/games/ios/coins-booster-configuration",
          additionalParams = mapOf(IDFV_PARAM to idfv, PACKAGE_ID_PARAM to packageId, GAME_VERSION_PARAM to gameVersion)
        ) { call.redirect() }
      }

      get("/celebration-configuration") {
        val idfv = extractRequiredParameter(IDFV_PARAM)
        val packageId = extractRequiredParameter(PACKAGE_ID_PARAM)
        val gameVersion = extractRequiredParameter(GAME_VERSION_PARAM)
        val user = userService.findUser(idfv = IDFV(idfv)) ?: run {
          call.respond(NotFound)
          return@get
        }
        redirectUrlService(
          service = PLAYTIME,
          user = user,
          path = "/ext/games/ios/celebration-configuration",
          additionalParams = mapOf(IDFV_PARAM to idfv, PACKAGE_ID_PARAM to packageId, GAME_VERSION_PARAM to gameVersion)
        ) { call.redirect() }
      }

      post("/status/v2") {
        val request = call.receiveText()
        val message = GameStatusRequest.newBuilder().also { protoJsonParser.merge(request, it) }.build()

        val user = userService.findUser(message.user.userId(), message.user.idfa(), message.user.idfv()) ?: run {
          logger().warn(
            "[ios/status/v2] Can't determine user and market by userId: '{}', idfa: '{}', idfv: '{}'",
            message.user.userIdOrNull?.value, message.user.idfaOrNull?.value, message.user.idfvOrNull?.value
          )
          call.respond(NotFound)
          return@post
        }

        val baseUrl = trackingService.getServiceAddressByMarket(user.market, PLAYTIME)
        val response = httpClient.post<GenericPayloadApiDto>("https://$baseUrl/service/ext/games/ios/${user.userId}/status") {
          contentType(ContentType.Application.Json)
          body = GenericPayloadApiDto(message.encodeToBase64())
        }
        call.respond(response.payload.fromBase64(GameStatusResponse.parser()).let { protoJsonPrinter.print(it) })
      }
      post("/first-launch") {
        val request = call.receiveText()
        val message = FirstLaunchRequest.newBuilder().also { protoJsonParser.merge(request, it) }.build()

        val user = userService.findUser(message.user.userId(), message.user.idfa(), message.user.idfv()) ?: run {
          logger().warn(
            "[android/first-launch] Can't determine user and market by userId: '{}', idfa: '{}', idfv: '{}'",
            message.user.userIdOrNull?.value, message.user.idfaOrNull?.value, message.user.idfvOrNull?.value
          )
          call.respond(NotFound)
          return@post
        }

        val baseUrl = trackingService.getServiceAddressByMarket(user.market, PLAYTIME)
        httpClient.post<String>("https://$baseUrl/service/ext/games/ios/${user.userId}/first-launch") {
          contentType(ContentType.Application.Json)
          body = GenericPayloadApiDto(message.encodeToBase64())
        }
        call.respond(OK)
      }
    }
    //endregion

    //region Unified ID
    route("/unifiedid") {
      get("/status") {
        val user = user() ?: run {
          call.respond(NotFound)
          return@get
        }
        redirectUrlService(
          service = PLAYTIME,
          user = user,
          path = "/ext/unifiedid/status"
        ) { call.redirect() }
      }
      get("/token") {
        val user = user() ?: run {
          call.respond(NotFound)
          return@get
        }
        redirectUrlService(
          service = PLAYTIME,
          user = user,
          path = "/ext/unifiedid/token"
        ) { call.redirect() }
      }
      get("/refresh") {
        val user = user() ?: run {
          call.respond(NotFound)
          return@get
        }
        redirectUrlService(
          service = PLAYTIME,
          user = user,
          path = "/ext/unifiedid/refresh"
        ) { call.redirect() }
      }
    }
    //endregion

    //region RampID
    route("/rampid") {
      get("/status") {
        val user = user() ?: run {
          call.respond(NotFound)
          return@get
        }
        redirectUrlService(
          service = PLAYTIME,
          user = user,
          path = "/ext/rampid/status"
        ) { call.redirect() }
      }
      get("emailHash") {
        val user = user() ?: run {
          call.respond(NotFound)
          return@get
        }
        redirectUrlService(
          service = PLAYTIME,
          user = user,
          path = "/ext/rampid/emailHash"
        ) { call.redirect() }
      }
    }
    //endregion

    //region Web Client
    route("/webclient") {
      get("/get-userid") {
        val user = user() ?: run {
          call.respondText("{}", contentType = ContentType.Application.Json, OK)
          return@get
        }
        call.respond(OK, user.toUserIdApiDto())
      }
      post("/register-user") {
        val user = user() ?: run {
          call.respond(NotFound)
          return@post
        }
        val request = call.receive<UserRegistrationInfoApiDto>()
        messagePublisher.publishCrossProject(
          message = WebAppUserRegistrationEventDto(
            userId = user.userId,
            idfv = request.idfv,
            deviceToken = request.firebaseDeviceToken,
            notificationEnabled = request.notificationEnabled,
            projectName = trackingService.getProjectNameByMarket(user.market)
          ),
          env = trackingService.getBuildVariantByMarket(user.market)
        )
        call.respond(OK)
      }
      post("/user-data") {
        val user = user() ?: run {
          call.respond(NotFound)
          return@post
        }
        val request = call.receive<UserAdditionalDataApiDto>()
        messagePublisher.publishCrossProject(
          message = WebAppUserAdditionalDataEventDto(
            userId = user.userId,
            projectName = trackingService.getProjectNameByMarket(user.market),
            idfv = request.idfv,
            idfa = request.idfa,
            deviceToken = request.firebaseDeviceToken,
            firebaseAppInstanceId = request.firebaseAppInstanceId,
            notificationEnabled = request.notificationEnabled,
            adjustId = request.adjustId
          ),
          env = trackingService.getBuildVariantByMarket(user.market)
        )
        call.respond(OK)
      }
      get("/user-status") {
        val user = user() ?: run {
          call.respondText("{}", contentType = ContentType.Application.Json, OK)
          return@get
        }
        val baseUrl = trackingService.getServiceAddressByMarket(user.market, PLAYTIME)
        val response = httpClient.get<WebUserStatusDto>("https://$baseUrl/service/ext/games/webclient/${user.userId}/status")
        call.respond(OK, response)
      }
      post("/jail-break") {
        val user = user() ?: run {
          call.respond(NotFound)
          return@post
        }
        val request = call.receive<UserJailBreakCheckApiDto>()
        messagePublisher.publishCrossProject(
          message = WebAppUserJailBreakCheckEventDto(
            userId = user.userId,
            projectName = trackingService.getProjectNameByMarket(user.market),
            jailBreak = request.jailBreak
          ),
          env = trackingService.getBuildVariantByMarket(user.market)
        )
        call.respond(OK)
      }
      post("/gps-location") {
        val user = user() ?: run {
          call.respond(NotFound)
          return@post
        }
        val packageId = extractRequiredParameter(PACKAGE_ID_PARAM)
        val verificationSessionId = extractRequiredParameter(VERIFICATION_SESSION_ID_PARAM)
        val request = call.receive<GpsLocationCheckApiDto>()

        val baseUrl = trackingService.getServiceAddressByMarket(user.market, PLAYTIME)
        httpClient.post<String>("https://$baseUrl/service/ext/games/webclient/${user.userId}/gps-location") {
          contentType(ContentType.Application.Json)
          body = WebAppVerificationGpsLocationCheckEventDto(
            sessionId = verificationSessionId,
            applicationId = packageId,
            provided = request.provided,
            location = request.location,
            isMocked = request.isMocked
          )
        }
        call.respond(OK)
      }
    }
    //endregion
  }

  suspend fun PipelineContext<Unit, ApplicationCall>.user(): UserSearchResult? {
    val googleAdIdParameter = call.parameters[GOOGLE_AD_ID_PARAM]
    val gaidParameter = call.parameters[GAID_PARAM]
    val idfaParameter = call.parameters[IDFA_PARAM]
    val userIdParameter = call.parameters[USER_ID_PARAM]
    val idfvParameter = call.parameters[IDFV_PARAM]

    val idfa =
      when {
        !idfaParameter.isNullOrBlank() -> idfaParameter
        !googleAdIdParameter.isNullOrBlank() -> googleAdIdParameter
        else -> gaidParameter
      }

    return userService.findUser(UserId(userIdParameter), IDFA(idfa), IDFV(idfvParameter))
  }

  private fun extractSanityCheckParams(request: ApplicationRequest): SanityCheckRequestDto {
    val headers = request.headers
    return SanityCheckRequestDto(
      ip = request.queryParameters["ip"] ?: extractIp(headers) ?: throw IpMissingException(headers),
      countryCode = request.queryParameters["country"]
        ?: headers["X-AppEngine-Country"]
        ?: throw CountryCodeMissingException(headers),
      vpnDetected = request.header("vd") == "true",
      skipCache = request.queryParameters["skipCache"] == "true",
      packageId = request.queryParameters["package"],
      googleAdId = request.queryParameters["gaid"],
      idfv = request.queryParameters["idfv"],
      userId = request.queryParameters["user_id"],
    )
  }

  private fun extractIp(headers: Headers): String? = headers["X-AppEngine-User-IP"] ?: headers["X-Forwarded-For"]?.substringBefore(",")

  private fun UserSearchResult.toUserIdApiDto(): UserIdApiDto = UserIdApiDto(
    userId = this.userId,
    market = this.market,
    platform = this.platform
  )

  private fun UserIds.userId() = UserId(this.userIdOrNull?.value)
  private fun UserIds.idfa() = IDFA(this.idfaOrNull?.value)
  private fun UserIds.idfv() = IDFV(this.idfvOrNull?.value)

}
