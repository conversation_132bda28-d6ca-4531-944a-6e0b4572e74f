package com.justplayapps.service.orchestrator.tracking

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isNull
import com.justplayapps.service.orchestrator.util.TRACKING_ID_STUB
import org.junit.jupiter.api.Test

class IdsWrappersTest {
  @Test
  fun `SHOULD perform validations WHEN user id is used`() {
    assertThat(getId(UserId("123"))).isEqualTo("123")
    assertThat(getId(UserId(""))).isNull()
    assertThat(getId(UserId(null))).isNull()
  }

  @Test
  fun `SHOULD perform validations WHEN idfa is used`() {
    assertThat(getId(IDFA("123"))).isEqualTo("123")
    assertThat(getId(IDFA(""))).isNull()
    assertThat(getId(IDFA(null))).isNull()
    assertThat(getId(IDFA(TRACKING_ID_STUB))).isNull()
  }

  @Test
  fun `SHOULD perform validations WHEN idfv is used`() {
    assertThat(getId(IDFV("123"))).isEqualTo("123")
    assertThat(getId(IDFV(""))).isNull()
    assertThat(getId(IDFV(null))).isNull()
  }

  private fun getId(userId: UserId?): String? {
    return when {
      userId.isValid() -> userId.id
      else -> null
    }
  }

  private fun getId(idfa: IDFA?): String? {
    return when {
      idfa.isValid() -> idfa.id
      else -> null
    }
  }

  private fun getId(idfv: IDFV?): String? {
    return when {
      idfv.isValid() -> idfv.id
      else -> null
    }
  }
}
