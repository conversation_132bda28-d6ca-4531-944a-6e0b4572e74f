package com.justplayapps.service.orchestrator.iosgameattconsent

import com.justplayapps.service.orchestrator.iosGameAttConsentBqDtoStub
import com.justplayapps.service.orchestrator.iosGameAttConsentTrackingDataStub
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class IosGameAttConsentBqDtoTest {

  @Test
  fun `SHOULD properly construct it self`() {
    assertEquals(
      expected = iosGameAttConsentBqDtoStub,
      actual = IosGameAttConsentBqDto.fromConsentTrackingData(iosGameAttConsentTrackingDataStub)
    )
  }
}