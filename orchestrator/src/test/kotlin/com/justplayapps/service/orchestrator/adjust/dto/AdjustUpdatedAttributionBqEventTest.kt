package com.justplayapps.service.orchestrator.adjust.dto

import assertk.assertThat
import assertk.assertions.isEqualTo
import io.ktor.util.*
import org.junit.jupiter.api.Test
import java.time.Instant

class AdjustUpdatedAttributionBqEventTest {

  @Test
  fun `SHOULD create AdjustInstallEvent ON from`() {
    val expected = AdjustUpdatedAttributionBqEvent(
      campaignName = "campaignName",
      trackerId = "ABCDE12345",
      trackerName = "ChristmasSpecialOffer",
      adNetwork = "amplifier",
      googleAdId = "8cdede78-0412-4e4f-bb68-b074a0021698",
      adjustId = "7777de7804124e4fbb68b074a0027777",
      userId = "user-id",
      ip = "*************",
      countryCode = "US",
      installedAt = Instant.ofEpochSecond(1614516453),
      packageName = "my.very.package",
      osVersion = "hercules-11",
      device = "green-dragon",
      userAgent = "Dalvik(nixos)-11.22.1963",
      limitAdTracking = false,
      isAdTrackingLimited = false,
      isOrganic = true,
      adgroupName = "someAdGroupName",
      creativeName = "someCreativeName",
      googleStoreReferrer = "utm_source=my.apps.com&utm_campaign",
      idfa = "8C6CBCOD-5F43-4765-A6E6-84DFF3D24707",
      idfv = "CCB300A0-DE1B-4D48-BC7E-599E453B8DD4",
      appSetId = "8710a8a2-f633-4ad1-ab0b-b01db7b0d275",
      outdatedTracker = "outdatedTracker1",
      outdatedTrackerName = "outdatedTracker1Name1",
      attributionUpdatedAt = Instant.ofEpochSecond(1614516454),
      activityKind = "activityKind1",
      createdAt = Instant.ofEpochSecond(1614516455),
    )

    val actual = AdjustUpdatedAttributionBqEvent.from(
      "user-id",
      valuesOf(
        "user_id" to listOf("user-id"),
        "campaign_name" to listOf("campaignName"),
        "tracker" to listOf("ABCDE12345"),
        "tracker_name" to listOf("ChristmasSpecialOffer"),
        "network_name" to listOf("amplifier"),
        "gps_adid" to listOf("8cdede78-0412-4e4f-bb68-b074a0021698"),
        "adid" to listOf("7777de7804124e4fbb68b074a0027777"),
        "ip_address" to listOf("*************"),
        "country" to listOf("US"),
        "installed_at" to listOf("1614516453"),
        "app_name" to listOf("my.very.package"),
        "os_version" to listOf("hercules-11"),
        "device_name" to listOf("green-dragon"),
        "user_agent" to listOf("Dalvik(nixos)-11.22.1963"),
        "tracking_limited" to listOf("0"),
        "is_organic" to listOf("1"),
        "adgroup_name" to listOf("someAdGroupName"),
        "creative_name" to listOf("someCreativeName"),
        "referrer" to listOf("utm_source=my.apps.com&utm_campaign"),
        "idfa" to listOf("8C6CBCOD-5F43-4765-A6E6-84DFF3D24707"),
        "idfv" to listOf("CCB300A0-DE1B-4D48-BC7E-599E453B8DD4"),
        "google_app_set_id" to listOf("8710a8a2-f633-4ad1-ab0b-b01db7b0d275"),
        "outdated_tracker" to listOf("outdatedTracker1"),
        "outdated_tracker_name" to listOf("outdatedTracker1Name1"),
        "attribution_updated_at" to listOf("1614516454"),
        "activity_kind" to listOf("activityKind1"),
        "created_at" to listOf("1614516455"),
      )
    )

    assertThat(actual).isEqualTo(expected)
  }

}