syntax = "proto3";

package com.justplayapps.orchestrator.events;

import "google/protobuf/wrappers.proto";
import "google/protobuf/timestamp.proto";
import "justplay/gameapi/game-events.proto";

enum AdjustEventType {
  UNDEFINED = 0;
  INSTALL = 1;
  UPDATE_ATTRIBUTION = 2;
}

message AdjustInstallEvent {
  string userId = 1;
  google.protobuf.StringValue campaign_name = 2;
  google.protobuf.StringValue tracker_id = 3;
  google.protobuf.StringValue tracker_name = 4;
  google.protobuf.StringValue ad_network = 5;
  google.protobuf.StringValue google_ad_id = 6;
  google.protobuf.StringValue adjust_id = 7;
  google.protobuf.StringValue ip = 8;
  google.protobuf.StringValue country_code = 9;
  google.protobuf.Timestamp installed_at = 10;
  google.protobuf.StringValue package_name = 11;
  google.protobuf.StringValue os_version = 12;
  google.protobuf.StringValue device = 13;
  google.protobuf.StringValue user_agent = 14;
  google.protobuf.BoolValue limit_ad_tracking = 15;
  google.protobuf.BoolValue is_organic = 16;
  google.protobuf.StringValue adgroup_name = 17;
  google.protobuf.StringValue creative_name = 18;
  google.protobuf.StringValue google_store_referrer = 19;
  google.protobuf.StringValue idfa = 20;
  google.protobuf.StringValue idfv = 21;
  google.protobuf.StringValue app_set_id = 22;
  google.protobuf.StringValue outdated_tracker = 23;
  google.protobuf.StringValue outdated_tracker_name = 24;
  google.protobuf.Timestamp attribution_updated_at = 25;
  google.protobuf.StringValue activity_kind = 26;
  google.protobuf.Timestamp created_at = 27;
  AdjustEventType event_type = 28;
}

message UserLocatedGameEvent {
  string user_id = 1;
  justplay.gameapi.events.GameEvent game_event = 2;
}

message IterableAddCoinsEvent {
  string user_id = 1;
  string event_id = 3;
}