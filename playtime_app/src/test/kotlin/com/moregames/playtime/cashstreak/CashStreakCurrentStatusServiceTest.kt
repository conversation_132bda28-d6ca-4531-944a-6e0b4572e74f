package com.moregames.playtime.cashstreak

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.util.TimeService
import com.moregames.playtime.cashstreak.CashStreakCurrentStatusService.StreakCurrentStatus.*
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.test.Test

class CashStreakCurrentStatusServiceTest {
  private val timeService: TimeService = mock()
  private val service = CashStreakCurrentStatusService(timeService)

  companion object {
    private val now = Instant.now()
  }

  @BeforeEach
  fun init() {
    whenever(timeService.now()).thenReturn(now)
  }

  @Test
  fun `SHOULD return COMPLETED on getStatus WHEN current step ends in some time`() {
    runBlocking {
      service.getStatus(
        deadline = now.plus(1, ChronoUnit.DAYS).plusSeconds(1),
        currentStepEnd = now.plusSeconds(1)
      )
    }.let { assertThat(it).isEqualTo(COMPLETED) }
  }

  @Test
  fun `SHOULD return FAILED on getStatus WHEN deadline passed`() {
    runBlocking {
      service.getStatus(
        deadline = now.minusSeconds(1),
        currentStepEnd = now.minus(1, ChronoUnit.DAYS).minusSeconds(1)
      )
    }.let { assertThat(it).isEqualTo(FAILED) }
  }

  @Test
  fun `SHOULD return UNCOMPLETED on getStatus WHEN current period passed but deadline is not`() {
    runBlocking {
      service.getStatus(
        deadline = now.plus(5, ChronoUnit.HOURS),
        currentStepEnd = now.minus(19, ChronoUnit.HOURS)
      )
    }.let { assertThat(it).isEqualTo(UNCOMPLETED) }
  }
}