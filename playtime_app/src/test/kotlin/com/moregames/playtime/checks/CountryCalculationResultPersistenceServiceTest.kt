package com.moregames.playtime.checks


import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isNotNull
import com.moregames.base.ipregistry.CountryCalculationResultDto
import com.moregames.base.table.DatabaseExtension
import com.moregames.playtime.checks.table.CountryCalculationResultTable
import com.moregames.playtime.user.prepareUser
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.deleteAll
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(DatabaseExtension::class)
class CountryCalculationResultPersistenceServiceTest(
  private val database: Database,
) {
  private lateinit var service: CountryCalculationResultPersistenceService

  companion object {
    val countryCode = "US"
    val countryCalculationResult = CountryCalculationResultDto(
      ipRegistryCountryCode = null,
      loadBalancerCountryCode = countryCode,
      gaeCountryCode = null,
      xForwardedFor = "forwardedIpRaw",
      xClientGeoLocation = "US,California, Mountain View",
      xAppEngineUserIp = "appEngineIP"
    )
  }

  @BeforeEach
  fun before() {
    service = CountryCalculationResultPersistenceService(database)
    transaction(database) {
      CountryCalculationResultTable.deleteAll()
    }
  }

  @Test
  fun `SHOULD save country calculation result ON saveCountryCalculationResult`() {
    val userId = database.prepareUser()
    runBlocking {
      service.saveCountryCalculationResult(userId, countryCalculationResult)
    }

    val count = transaction(database) {
      CountryCalculationResultTable
        .select { CountryCalculationResultTable.userId eq userId }
        .count()
    }

    assertThat(count).isEqualTo(1L)
    val result = transaction(database) {
      CountryCalculationResultTable
        .select { CountryCalculationResultTable.userId eq userId }
        .singleOrNull()?.let {
          CountryCalculationResultDto(
            ipRegistryCountryCode = it[CountryCalculationResultTable.ipRegistyCountry],
            loadBalancerCountryCode = it[CountryCalculationResultTable.lbCountry],
            gaeCountryCode = it[CountryCalculationResultTable.gaeCountry],
            xForwardedFor = it[CountryCalculationResultTable.xForwardedFor],
            xClientGeoLocation = it[CountryCalculationResultTable.xClientGeoLocation],
            xAppEngineUserIp = it[CountryCalculationResultTable.xAppEngineUserIp]
          )
        }
    }
    assertThat(result).isNotNull()
    assertThat(result).isEqualTo(countryCalculationResult)
  }

  @Test
  fun `SHOULD save country calculation result with ip registry country code WHEN it is populated`() {
    val userId = database.prepareUser()
    runBlocking {
      service.saveCountryCalculationResult(userId, countryCalculationResult.copy(ipRegistryCountryCode = "US"))
    }

    val count = transaction(database) {
      CountryCalculationResultTable
        .select { CountryCalculationResultTable.userId eq userId }
        .count()
    }

    assertThat(count).isEqualTo(1L)
    val result = transaction(database) {
      CountryCalculationResultTable
        .select { CountryCalculationResultTable.userId eq userId }
        .singleOrNull()?.let {
          CountryCalculationResultDto(
            ipRegistryCountryCode = it[CountryCalculationResultTable.ipRegistyCountry],
            loadBalancerCountryCode = it[CountryCalculationResultTable.lbCountry],
            gaeCountryCode = it[CountryCalculationResultTable.gaeCountry],
            xForwardedFor = it[CountryCalculationResultTable.xForwardedFor],
            xClientGeoLocation = it[CountryCalculationResultTable.xClientGeoLocation],
            xAppEngineUserIp = it[CountryCalculationResultTable.xAppEngineUserIp]
          )
        }
    }
    assertThat(result).isNotNull()
    assertThat(result).isEqualTo(countryCalculationResult.copy(ipRegistryCountryCode = "US"))
  }

  @Test
  fun `SHOULD save country calculation result with gae country code WHEN it is populated`() {
    val userId = database.prepareUser()
    runBlocking {
      service.saveCountryCalculationResult(userId, countryCalculationResult.copy(gaeCountryCode = "US"))
    }

    val count = transaction(database) {
      CountryCalculationResultTable
        .select { CountryCalculationResultTable.userId eq userId }
        .count()
    }

    assertThat(count).isEqualTo(1L)
    val result = transaction(database) {
      CountryCalculationResultTable
        .select { CountryCalculationResultTable.userId eq userId }
        .singleOrNull()?.let {
          CountryCalculationResultDto(
            ipRegistryCountryCode = it[CountryCalculationResultTable.ipRegistyCountry],
            loadBalancerCountryCode = it[CountryCalculationResultTable.lbCountry],
            gaeCountryCode = it[CountryCalculationResultTable.gaeCountry],
            xForwardedFor = it[CountryCalculationResultTable.xForwardedFor],
            xClientGeoLocation = it[CountryCalculationResultTable.xClientGeoLocation],
            xAppEngineUserIp = it[CountryCalculationResultTable.xAppEngineUserIp]
          )
        }
    }
    assertThat(result).isNotNull()
    assertThat(result).isEqualTo(countryCalculationResult.copy(gaeCountryCode = "US"))
  }
}