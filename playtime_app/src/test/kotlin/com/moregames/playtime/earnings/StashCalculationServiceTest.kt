package com.moregames.playtime.earnings

import assertk.assertThat
import assertk.assertions.isEqualByComparingTo
import assertk.assertions.isNotNull
import assertk.assertions.isNull
import com.justplayapps.service.rewarding.earnings.EmExperimentBaseService
import com.justplayapps.service.rewarding.earnings.StashCalculationService
import com.justplayapps.service.rewarding.earnings.dto.Earnings
import com.moregames.base.abtesting.Variations
import com.moregames.base.util.mock
import com.moregames.playtime.user.fraudscore.HighlyTrustedUsersService
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.mockito.kotlin.mock
import java.math.BigDecimal

class StashCalculationServiceTest {
  private val emExperimentBaseService: EmExperimentBaseService = mock()
  private val highlyTrustedUsersService: HighlyTrustedUsersService = mock()

  private val underTest = StashCalculationService(
    emExperimentBaseService = emExperimentBaseService,
    highlyTrustedUsersService = highlyTrustedUsersService,
  )

  companion object {
    private const val USER_ID = "user-id"
    private val earnings = Earnings(userId = USER_ID, earningsSum = BigDecimal("0.99"), quotasDto = null)
  }

  @BeforeEach
  fun before() {
    emExperimentBaseService.mock({ stashVariation(USER_ID) }, null)
    highlyTrustedUsersService.mock({ isHighlyTrustedUser(USER_ID) }, true)
  }

  @Test
  fun `SHOULD return null ON calculateUsdForStash WHEN user is not a stash participant`() {
    runBlocking {
      underTest.calculateUsdForStash(
        earningsFromRevenue = earnings,
        realRevenue = BigDecimal("4.24"),
        realGameRevenue = BigDecimal("3.14"),
        gamesEm2CappedRevenue = BigDecimal("2.71"),
      )
    }.let { actual ->
      assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD do nothing ON calculateUsdForStash WHEN em2_stash_5pct variation AND real revenue is less than coins`() {
    emExperimentBaseService.mock({ stashVariation(USER_ID) }, Variations.EM2_STASH_5PCT)

    runBlocking {
      underTest.calculateUsdForStash(
        earningsFromRevenue = earnings,
        realRevenue = BigDecimal("4.24"),
        realGameRevenue = BigDecimal("2.71"),
        gamesEm2CappedRevenue = BigDecimal("2.71"),
      )
    }.let { actual ->
      assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD do nothing ON calculateUsdForStash WHEN em2_stash_5pct_dq_fixed variation AND real revenue is less than coins`() {
    emExperimentBaseService.mock({ stashVariation(USER_ID) }, Variations.EM2_STASH_5PCT_DQ_FIXED)

    runBlocking {
      underTest.calculateUsdForStash(
        earningsFromRevenue = earnings,
        realRevenue = BigDecimal("4.24"),
        realGameRevenue = BigDecimal("2.71"),
        gamesEm2CappedRevenue = BigDecimal("2.71"),
      )
    }.let { actual ->
      assertThat(actual).isNull()
    }
  }

  @ParameterizedTest
  @EnumSource(value = Variations::class, names = ["EM2_STASH_5PCT", "EM2_STASH_5PCT_DQ_FIXED", "EM2_STASH_ONE_TIME_FIX", "EM2_STASH_ONE_TIME_FIX_DQ"])
  fun `SHOULD calculate stash ON calculateUsdForStash WHEN coins stash participant `(variation: Variations) {
    emExperimentBaseService.mock({ stashVariation(USER_ID) }, variation)

    runBlocking {
      underTest.calculateUsdForStash(
        earningsFromRevenue = earnings,
        realRevenue = BigDecimal("4.24"),
        realGameRevenue = BigDecimal("3.14"),
        gamesEm2CappedRevenue = BigDecimal("2.71"),
      )
    }.let { actual ->
      assertThat(actual).isNotNull().isEqualByComparingTo(BigDecimal("0.43"))
    }
  }

  @ParameterizedTest
  @EnumSource(
    value = Variations::class,
    names = ["EM2_STASH_TO_EARN_45", "EM2_STASH_TO_EARN_45_DQ", "EM2_STASH_TO_EARN_55", "EM2_STASH_TO_EARN_55_DQ", "EM2_STASH_TO_EARN_65"]
  )
  fun `SHOULD return null ON calculateUsdForStash WHEN em2_stash_to_earn_foo variation AND user is not ht`(variation: Variations) {
    emExperimentBaseService.mock({ stashVariation(USER_ID) }, variation)
    highlyTrustedUsersService.mock({ isHighlyTrustedUser(USER_ID) }, false)

    runBlocking {
      underTest.calculateUsdForStash(
        earningsFromRevenue = earnings,
        realRevenue = BigDecimal("4.24"),
        realGameRevenue = BigDecimal("2.71"),
        gamesEm2CappedRevenue = BigDecimal("2.71"),
      )
    }.let { actual ->
      assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD return null ON calculateUsdForStash WHEN em2_stash_to_earn_45 variation AND earnings more than target`() {
    emExperimentBaseService.mock({ stashVariation(USER_ID) }, Variations.EM2_STASH_TO_EARN_45)

    runBlocking {
      underTest.calculateUsdForStash(
        earningsFromRevenue = earnings.copy(
          earningsSum = BigDecimal("1.22")
        ),
        realRevenue = BigDecimal("2.71"),
        realGameRevenue = BigDecimal("2.71"),
        gamesEm2CappedRevenue = BigDecimal("2.71"),
      )
    }.let { actual ->
      assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD calculate stash ON calculateUsdForStash WHEN em2_stash_to_earn_45 variation AND earnings less than target`() {
    emExperimentBaseService.mock({ stashVariation(USER_ID) }, Variations.EM2_STASH_TO_EARN_45)

    runBlocking {
      underTest.calculateUsdForStash(
        earningsFromRevenue = earnings.copy(
          earningsSum = BigDecimal("1.01")
        ),
        realRevenue = BigDecimal("2.71"),
        realGameRevenue = BigDecimal("2.71"),
        gamesEm2CappedRevenue = BigDecimal("2.71"),
      )
    }.let { actual ->
      assertThat(actual).isNotNull().isEqualByComparingTo(BigDecimal("0.419"))
    }
  }

  @Test
  fun `SHOULD return null ON calculateUsdForStash WHEN em2_stash_to_earn_45_dq variation AND earnings more than target`() {
    emExperimentBaseService.mock({ stashVariation(USER_ID) }, Variations.EM2_STASH_TO_EARN_45_DQ)

    runBlocking {
      underTest.calculateUsdForStash(
        earningsFromRevenue = earnings.copy(
          earningsSum = BigDecimal("1.22")
        ),
        realRevenue = BigDecimal("2.71"),
        realGameRevenue = BigDecimal("2.71"),
        gamesEm2CappedRevenue = BigDecimal("2.71"),
      )
    }.let { actual ->
      assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD calculate stash ON calculateUsdForStash WHEN em2_stash_to_earn_45_dq variation AND earnings less than target`() {
    emExperimentBaseService.mock({ stashVariation(USER_ID) }, Variations.EM2_STASH_TO_EARN_45_DQ)

    runBlocking {
      underTest.calculateUsdForStash(
        earningsFromRevenue = earnings.copy(
          earningsSum = BigDecimal("1.01")
        ),
        realRevenue = BigDecimal("2.71"),
        realGameRevenue = BigDecimal("2.71"),
        gamesEm2CappedRevenue = BigDecimal("2.71"),
      )
    }.let { actual ->
      assertThat(actual).isNotNull().isEqualByComparingTo(BigDecimal("0.419"))
    }
  }

  @Test
  fun `SHOULD return null ON calculateUsdForStash WHEN em2_stash_to_earn_55 variation AND earnings more than target`() {
    emExperimentBaseService.mock({ stashVariation(USER_ID) }, Variations.EM2_STASH_TO_EARN_55)

    runBlocking {
      underTest.calculateUsdForStash(
        earningsFromRevenue = earnings.copy(
          earningsSum = BigDecimal("1.50")
        ),
        realRevenue = BigDecimal("2.71"),
        realGameRevenue = BigDecimal("2.71"),
        gamesEm2CappedRevenue = BigDecimal("2.71"),
      )
    }.let { actual ->
      assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD calculate stash ON calculateUsdForStash WHEN em2_stash_to_earn_55 variation AND earnings less than target`() {
    emExperimentBaseService.mock({ stashVariation(USER_ID) }, Variations.EM2_STASH_TO_EARN_55)

    runBlocking {
      underTest.calculateUsdForStash(
        earningsFromRevenue = earnings.copy(
          earningsSum = BigDecimal("1.01")
        ),
        realRevenue = BigDecimal("2.71"),
        realGameRevenue = BigDecimal("2.71"),
        gamesEm2CappedRevenue = BigDecimal("2.71"),
      )
    }.let { actual ->
      assertThat(actual).isNotNull().isEqualByComparingTo(BigDecimal("0.961"))
    }
  }

  @Test
  fun `SHOULD return null ON calculateUsdForStash WHEN em2_stash_to_earn_55_dq variation AND earnings more than target`() {
    emExperimentBaseService.mock({ stashVariation(USER_ID) }, Variations.EM2_STASH_TO_EARN_55_DQ)

    runBlocking {
      underTest.calculateUsdForStash(
        earningsFromRevenue = earnings.copy(
          earningsSum = BigDecimal("1.50")
        ),
        realRevenue = BigDecimal("2.71"),
        realGameRevenue = BigDecimal("2.71"),
        gamesEm2CappedRevenue = BigDecimal("2.71"),
      )
    }.let { actual ->
      assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD calculate stash ON calculateUsdForStash WHEN em2_stash_to_earn_55_dq variation AND earnings less than target`() {
    emExperimentBaseService.mock({ stashVariation(USER_ID) }, Variations.EM2_STASH_TO_EARN_55_DQ)

    runBlocking {
      underTest.calculateUsdForStash(
        earningsFromRevenue = earnings.copy(
          earningsSum = BigDecimal("1.01")
        ),
        realRevenue = BigDecimal("2.71"),
        realGameRevenue = BigDecimal("2.71"),
        gamesEm2CappedRevenue = BigDecimal("2.71"),
      )
    }.let { actual ->
      assertThat(actual).isNotNull().isEqualByComparingTo(BigDecimal("0.961"))
    }
  }

  @Test
  fun `SHOULD return null ON calculateUsdForStash WHEN em2_stash_to_earn_65 variation AND earnings more than target`() {
    emExperimentBaseService.mock({ stashVariation(USER_ID) }, Variations.EM2_STASH_TO_EARN_65)

    runBlocking {
      underTest.calculateUsdForStash(
        earningsFromRevenue = earnings.copy(
          earningsSum = BigDecimal("1.77")
        ),
        realRevenue = BigDecimal("2.71"),
        realGameRevenue = BigDecimal("2.71"),
        gamesEm2CappedRevenue = BigDecimal("2.71"),
      )
    }.let { actual ->
      assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD calculate stash ON calculateUsdForStash WHEN em2_stash_to_earn_65 variation AND earnings less than target`() {
    emExperimentBaseService.mock({ stashVariation(USER_ID) }, Variations.EM2_STASH_TO_EARN_65)

    runBlocking {
      underTest.calculateUsdForStash(
        earningsFromRevenue = earnings.copy(
          earningsSum = BigDecimal("1.01")
        ),
        realRevenue = BigDecimal("2.71"),
        realGameRevenue = BigDecimal("2.71"),
        gamesEm2CappedRevenue = BigDecimal("2.71"),
      )
    }.let { actual ->
      assertThat(actual).isNotNull().isEqualByComparingTo(BigDecimal("1.503"))
    }
  }

}