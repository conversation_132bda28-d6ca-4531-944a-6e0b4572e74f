package com.moregames.playtime.buseffects

import com.justplayapps.service.rewarding.earnings.UserCurrentCoinsBalance
import com.justplayapps.service.rewarding.earnings.dto.Earnings
import com.justplayapps.service.rewarding.earnings.dto.EarningsCalculationResult
import com.moregames.base.abtesting.AbGameCoinGoalsService
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.AbTestingService.IsExperimentParticipant
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.messaging.dto.EarningsCappedEventDto
import com.moregames.base.util.TimeService
import com.moregames.base.util.io
import com.moregames.base.util.mock
import com.moregames.playtime.earnings.dto.UsedQuota
import com.moregames.playtime.earnings.dto.UserEarningsQuotasDto
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.coingoal.GameCoinGoalsService
import com.moregames.playtime.user.fraudscore.FraudScoreService
import com.moregames.playtime.user.onboarding.progressbar.OnboardingProgressBarService
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant

@OptIn(ExperimentalCoroutinesApi::class)
class CashoutPeriodEffectsTest {
  private val now = Instant.now()

  private val abGameCoinGoalsService: AbGameCoinGoalsService = mock()
  private val userService: UserService = mock()
  private val fraudScoreService: FraudScoreService = mock()
  private val gameCoinGoalsService: GameCoinGoalsService = mock()
  private val timeService: TimeService = mock {
    on { now() } doReturn now
  }
  private val bigQueryEventPublisher: BigQueryEventPublisher = mock()
  private val scope = TestScope()
  private val abTestingService: AbTestingService = mock()
  private val onboardingProgressBarService: OnboardingProgressBarService = mock()
  private val messageBus: MessageBus = mock()

  private val underTest = CashoutPeriodEffects(
    abGameCoinGoalsService = abGameCoinGoalsService,
    userService = userService,
    fraudScoreService = fraudScoreService,
    gameCoinGoalsService = gameCoinGoalsService,
    timeService = timeService,
    bigQueryEventPublisher = bigQueryEventPublisher,
    coroutineScope = { scope.io() },
    abTestingService = abTestingService,
    onboardingProgressBarService = onboardingProgressBarService,
    messageBus = messageBus,
  )

  @BeforeEach
  fun setUp() {
    abTestingService.mock({ isUserExperimentParticipant(userId, ClientExperiment.SPECIAL_CASHOUT_OFFERS) }, false)
    abTestingService.mock({ isEm2Participant(any()) }, false)
  }

  @Test
  fun `SHOULD call reset balance WHEN game coin goal participant`() {
    userService.mock({ getUser(userId) }, userDtoStub.copy(appPlatform = AppPlatform.IOS))
    abGameCoinGoalsService.mock({ isGameCoinGoalsParticipant(userId, AppPlatform.IOS) }, IsExperimentParticipant.Yes(mock()))

    runTest { underTest.handleCashoutPeriodEndedEffect(CashoutPeriodEndedEffect(userId, em2EarningsComputationData)) }

    verifyBlocking(gameCoinGoalsService) { resetBalance(userId) }

    scope.advanceUntilIdle()

    verifyBlocking(fraudScoreService) { checkUserApplovinRevenueFrequencyAndBlockFraudsters(userId) }
    verifyBlocking(fraudScoreService) { blockUserOnGameAutomation(userId) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        listOf(
          EarningsCappedEventDto(
            metaId = 123,
            userId = userId,
            cappedAmount = BigDecimal("15.230000"),
            createdAt = timeService.now(),
            cappedEventType = EarningsCappedEventDto.CappedEventType.GAME_REAL_REVENUE_CUT,
          ),
          EarningsCappedEventDto(
            metaId = 123,
            userId = userId,
            cappedAmount = BigDecimal("3.770000"),
            createdAt = timeService.now(),
            cappedEventType = EarningsCappedEventDto.CappedEventType.QUOTA_REVENUE_CUT,
          ),
        )
      )
    }
  }

  @Test
  fun `SHOULD call cashout offer service WHEN special cashout offer participant`() {
    userService.mock({ getUser(userId) }, userDtoStub.copy(appPlatform = AppPlatform.IOS))
    abGameCoinGoalsService.mock({ isGameCoinGoalsParticipant(userId, AppPlatform.IOS) }, IsExperimentParticipant.Yes(mock()))
    abTestingService.mock({ isUserExperimentParticipant(userId, ClientExperiment.SPECIAL_CASHOUT_OFFERS) }, true)

    runTest { underTest.handleCashoutPeriodEndedEffect(CashoutPeriodEndedEffect(userId, em2EarningsComputationData)) }

    verifyBlocking(gameCoinGoalsService) { resetBalance(userId) }

    scope.advanceUntilIdle()

    verifyBlocking(fraudScoreService) { checkUserApplovinRevenueFrequencyAndBlockFraudsters(userId) }
    verifyBlocking(fraudScoreService) { blockUserOnGameAutomation(userId) }
    verifyBlocking(messageBus) { publish(CreateCashoutOfferSetEffect(userId)) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        listOf(
          EarningsCappedEventDto(
            metaId = 123,
            userId = userId,
            cappedAmount = BigDecimal("15.230000"),
            createdAt = timeService.now(),
            cappedEventType = EarningsCappedEventDto.CappedEventType.GAME_REAL_REVENUE_CUT,
          ),
          EarningsCappedEventDto(
            metaId = 123,
            userId = userId,
            cappedAmount = BigDecimal("3.770000"),
            createdAt = timeService.now(),
            cappedEventType = EarningsCappedEventDto.CappedEventType.QUOTA_REVENUE_CUT,
          ),
        )
      )
    }
  }

  @Test
  fun `SHOULD call onboarding progress bar WHEN onboarding progress bar participant`() {
    userService.mock({ getUser(userId) }, userDtoStub.copy(appPlatform = AppPlatform.IOS))
    userService.mock({ getUserLastGameCoinsDate(userId) }, Instant.now())
    abGameCoinGoalsService.mock({ isGameCoinGoalsParticipant(userId, AppPlatform.IOS) }, IsExperimentParticipant.No)
    abTestingService.mock({ isUserExperimentParticipant(userId, ClientExperiment.ANDROID_ONBOARDING_PROGRESS_BAR) }, true)

    runTest { underTest.handleCashoutPeriodEndedEffect(CashoutPeriodEndedEffect(userId, em2EarningsComputationData)) }

    scope.advanceUntilIdle()

    verifyBlocking(onboardingProgressBarService) {
      activateRouteToCashout(userId)
      completePlayFirstGame(userId)
    }
    verifyBlocking(fraudScoreService) { checkUserApplovinRevenueFrequencyAndBlockFraudsters(userId) }
    verifyBlocking(fraudScoreService) { blockUserOnGameAutomation(userId) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        listOf(
          EarningsCappedEventDto(
            metaId = 123,
            userId = userId,
            cappedAmount = BigDecimal("15.230000"),
            createdAt = timeService.now(),
            cappedEventType = EarningsCappedEventDto.CappedEventType.GAME_REAL_REVENUE_CUT,
          ),
          EarningsCappedEventDto(
            metaId = 123,
            userId = userId,
            cappedAmount = BigDecimal("3.770000"),
            createdAt = timeService.now(),
            cappedEventType = EarningsCappedEventDto.CappedEventType.QUOTA_REVENUE_CUT,
          ),
        )
      )
    }
  }

  @Test
  fun `SHOULD skip reset balance WHEN NOT game coin goal participant`() {
    userService.mock({ getUser(userId) }, userDtoStub.copy(appPlatform = AppPlatform.IOS))
    abGameCoinGoalsService.mock({ isGameCoinGoalsParticipant(userId, AppPlatform.IOS) }, IsExperimentParticipant.No)

    runTest { underTest.handleCashoutPeriodEndedEffect(CashoutPeriodEndedEffect(userId, em2EarningsComputationData)) }

    scope.advanceUntilIdle()

    verifyNoInteractions(gameCoinGoalsService)
    verifyBlocking(fraudScoreService) { checkUserApplovinRevenueFrequencyAndBlockFraudsters(userId) }
    verifyBlocking(fraudScoreService) { blockUserOnGameAutomation(userId) }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        listOf(
          EarningsCappedEventDto(
            metaId = 123,
            userId = userId,
            cappedAmount = BigDecimal("15.230000"),
            createdAt = timeService.now(),
            cappedEventType = EarningsCappedEventDto.CappedEventType.GAME_REAL_REVENUE_CUT,
          ),
          EarningsCappedEventDto(
            metaId = 123,
            userId = userId,
            cappedAmount = BigDecimal("3.770000"),
            createdAt = timeService.now(),
            cappedEventType = EarningsCappedEventDto.CappedEventType.QUOTA_REVENUE_CUT,
          ),
        )
      )
    }
  }

  companion object {
    private const val userId = "userId"

    val em2EarningsComputationData = EarningsCalculationResult.Em2(
      simpleCalculationResult = EarningsCalculationResult.Simple(
        metaId = 123,
        amount = BigDecimal.ONE,
        amountNoRounding = BigDecimal.TEN
      ),
      noEarnings = false,
      realRevenue = BigDecimal("3.0"),
      realGameRevenue = BigDecimal("4.0"),
      em2CoinsBalance = UserCurrentCoinsBalance(
        gameCoins = BigDecimal("600.0"),
        offerCoins = BigDecimal("700.0"),
        bonusCoins = BigDecimal("800.0")
      ),
      coinsForOneDollar = BigDecimal("100.0"),
      em2Revenue = BigDecimal("5.77"),
      em2GameRevenue = BigDecimal("5.0"),
      earnings =
        Earnings(
          userId = userId,
          earningsSum = BigDecimal("1.0"),
          quotasDto = UserEarningsQuotasDto(
            userId = userId,
            quotas = listOf("quotas"),
            periodEnd = Instant.now(),
            values = listOf(BigDecimal("0.5"))
          ),
          usedQuota = UsedQuota(BigDecimal("0.5"))
        ),
    )
  }
}