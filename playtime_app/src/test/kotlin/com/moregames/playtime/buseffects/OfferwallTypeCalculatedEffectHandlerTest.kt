package com.moregames.playtime.buseffects

import com.moregames.base.app.OfferWallType.ADJOE
import com.moregames.base.app.OfferWallType.TAPJOY
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.messaging.dto.UserOfferwallTypeBqEventDto
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.user.UserDataCache
import com.moregames.playtime.user.offer.AndroidOfferwallPersistenceService
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoInteractions
import java.time.Instant
import java.time.temporal.ChronoUnit

class OfferwallTypeCalculatedEffectHandlerTest {
  private val userDataCache: UserDataCache = mock()
  private val androidOfferwallPersistenceService: AndroidOfferwallPersistenceService = mock()
  private val timeService: TimeService = mock()
  private val bigQueryEventPublisher: BigQueryEventPublisher = mock()
  private val marketService: MarketService = mock()

  private val service = OfferwallTypeCalculatedEffectHandler(
    userDataCache = userDataCache,
    androidOfferwallPersistenceService = androidOfferwallPersistenceService,
    timeService = timeService,
    bigQueryEventPublisher = bigQueryEventPublisher,
    marketService = marketService
  )

  companion object {
    const val USER_ID = "userId"
    private val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    private val bqEvent = UserOfferwallTypeBqEventDto(USER_ID, TAPJOY, now, "au")
    private val ofwTypes = listOf(TAPJOY, ADJOE)
    private val effect = OfferwallTypeCalculatedEffect(USER_ID, ofwTypes)
  }

  @Test
  fun `SHOULD track offerwall type in DB send it to BQ and invalidate user cache on handleOfferwallTypeCalculatedEffect`() {
    androidOfferwallPersistenceService.mock({ trackOfferwallTypesReturnResult(USER_ID, ofwTypes) }, true)
    timeService.mock({ now() }, now)
    marketService.mock({ getJPMarket() }, "au")

    runBlocking {
      service.handleOfferwallTypeCalculatedEffect(effect)
    }

    verifyBlocking(androidOfferwallPersistenceService) { trackOfferwallTypesReturnResult(USER_ID, ofwTypes) }
    verifyBlocking(bigQueryEventPublisher) { publish(listOf(bqEvent, bqEvent.copy(offerwallType = ADJOE))) }
    verifyBlocking(userDataCache) { invalidate(USER_ID) }
  }

  @Test
  fun `SHOULD only try to track offerwall type in DB handleOfferwallTypeCalculatedEffect WHEN offerwall types were already tracked`() {
    androidOfferwallPersistenceService.mock({ trackOfferwallTypesReturnResult(USER_ID, ofwTypes) }, false)
    timeService.mock({ now() }, now)
    marketService.mock({ getJPMarket() }, "au")

    runBlocking {
      service.handleOfferwallTypeCalculatedEffect(effect)
    }

    verifyBlocking(androidOfferwallPersistenceService) { trackOfferwallTypesReturnResult(USER_ID, ofwTypes) }
    verifyNoInteractions(bigQueryEventPublisher, userDataCache)
  }
}