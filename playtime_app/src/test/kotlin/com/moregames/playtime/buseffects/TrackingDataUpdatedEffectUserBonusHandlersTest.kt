package com.moregames.playtime.buseffects

import com.moregames.base.bus.MessageBus
import com.moregames.base.messaging.dto.UserDeletedEventMessageDto
import com.moregames.playtime.user.ActiveUsersService
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking

class TrackingDataUpdatedEffectUserBonusHandlersTest {
  private val activeUsersService: ActiveUsersService = mock()
  private val messageBus: MessageBus = mock()

  private val underTest = TrackingDataUpdatedEffectHandlers(
    messageBus = messageBus,
    activeUsersService = activeUsersService,
  )

  @Test
  fun `SHOULD publish event to pubsub ON send delete event effect`() = runTest {
    underTest.handleSendUserDeletedEvent(SendUserDeletedEvent(setOf("userId", "userId2")))

    verifyBlocking(activeUsersService) { removeUsers(setOf("userId", "userId2")) }
    verifyBlocking(messageBus) {
      publish(UserDeletedEventMessageDto("userId"))
      publish(UserDeletedEventMessageDto("userId2"))
    }
  }
}