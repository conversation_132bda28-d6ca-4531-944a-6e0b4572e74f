package com.moregames.playtime.subscribers

import com.justplayapps.service.rewarding.earnings.proto.convertRevenueToEarningsMessage
import com.moregames.base.bus.MessageBus
import com.moregames.base.coins.UserCurrentCoinsGoalBalance
import com.moregames.base.messaging.dto.CashoutPeriodEndedEventDto
import com.moregames.base.util.mock
import com.moregames.base.util.toProto
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutPeriodsService
import com.moregames.playtime.utils.user
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoMoreInteractions
import java.time.Instant

@OptIn(ExperimentalCoroutinesApi::class)
class CashoutPeriodEndedMessagePushSubscriberTest {
  private val cashoutPeriodsService: CashoutPeriodsService = mock()
  private val userService: UserService = mock()
  private val messageBus: MessageBus = mock()
  private val rewardingFacade: RewardingFacade = mock()

  private val subscriber = CashoutPeriodEndedMessagePushSubscriber(
    cashoutPeriodsService = cashoutPeriodsService,
    userService = userService,
    messageBus = messageBus,
    rewardingFacade = rewardingFacade,
  )

  private companion object {
    val event = CashoutPeriodEndedEventDto(
      userId = "userId",
      periodEnd = Instant.now(),
      periodStart = Instant.now()
    )
  }

  init {
    Dispatchers.setMain(StandardTestDispatcher())
  }

  @BeforeEach
  fun before() {
    userService.mock({ userExists(any()) }, true)
    userService.mock({ loadCoinGoalUser(event.userId) }, user.copy(coinsGoal = 42))
    rewardingFacade.mock({ getUserCurrentCoinsBalance(user.userId, user.appPlatform) }, UserCurrentCoinsGoalBalance(0, 0, 0))
  }

  @Test
  fun `SHOULD do nothing ON processMessage WHEN user does not exist`() {
    userService.mock({ userExists(any()) }, false)

    runTest {
      subscriber.handle(event)
    }

    verifyBlocking(userService) { userExists(event.userId) }
    verifyNoInteractions(cashoutPeriodsService)
    verifyNoMoreInteractions(userService)
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD convertRevenueToEarnings + enable cashout + create next cashout period ON handle`() {
    runTest {
      subscriber.handle(event)
    }

    verifyBlocking(cashoutPeriodsService) { trackLastPeriodGoalReached(event.userId, false) }
    verifyBlocking(userService) { userExists(event.userId) }
    verifyBlocking(userService) { loadCoinGoalUser(event.userId) }
    verifyBlocking(messageBus) {
      publish(
        convertRevenueToEarningsMessage {
          this.userId = event.userId
          this.periodStart = event.periodStart.toProto()
          this.periodEnd = event.periodEnd.toProto()
          this.coinsGoal = 42
        }
      )
    }
    verifyNoMoreInteractions(cashoutPeriodsService)
    verifyNoMoreInteractions(userService)
    verifyNoMoreInteractions(messageBus)
  }
}