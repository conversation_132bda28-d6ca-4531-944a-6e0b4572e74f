package com.moregames.playtime.subscribers

import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.UserCurrencyEarnings
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.util.mock
import com.moregames.playtime.buseffects.EarningsAddedEffectHandler.EarningsAddedEffect
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.earnings.dto.EarningsAddedBqEventDto
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.RewardEarningsAddedNotification
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.RewardSpecialChallengesEarningsAddedNotification
import com.moregames.playtime.user.UserDto
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutService
import com.moregames.playtime.user.challenge.dto.ChallengeType
import com.moregames.playtime.user.challenge.dto.RewardEarningsAddedEventDto
import com.moregames.playtime.utils.CAD
import com.moregames.playtime.utils.FR_LOCALE
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant

class RewardEarningsAddedEventSubscriberTest {
  private val userService: UserService = mock()
  private val messageBus: MessageBus = mock()
  private val bigQueryEventPublisher: BigQueryEventPublisher = mock()
  private val cashoutService: CashoutService = mock()

  private val subscriber = RewardEarningsAddedEventSubscriber(
    userService = userService,
    cashoutService = cashoutService,
    messageBus = messageBus,
    bigQueryEventPublisher = bigQueryEventPublisher
  )

  private val userMock: UserDto = mock()

  companion object {
    private const val USER_ID = "user-id"
    private val now = Instant.now()
    private val message = RewardEarningsAddedEventDto(
      metaId = -2,
      userId = USER_ID,
      amount = BigDecimal("2.71"),
      amountUserCurrency = BigDecimal("3.14"),
      userCurrencyCode = "CAD",
      challengeEventId = "animal",
      createdAt = now,
      challengeType = ChallengeType.REGULAR,
    )
  }

  @BeforeEach
  fun before() {
    userService.mock({ userExists(any()) }, true)
    userService.mock({ getUser(USER_ID) }, userMock)
    whenever(userMock.locale).thenReturn(FR_LOCALE)
  }

  @Test
  fun `SHOULD do nothing ON handle WHEN user does not exist`() {
    userService.mock({ userExists(USER_ID) }, false)

    runBlocking { subscriber.handle(message) }

    verifyBlocking(userService) { userExists(USER_ID) }
    verifyNoMoreInteractions(userService)

    verifyNoInteractions(cashoutService)
    verifyNoInteractions(messageBus)
    verifyNoInteractions(bigQueryEventPublisher)
  }

  @Test
  fun `SHOULD process message ON handle`() {
    val addedEarnings = UserCurrencyEarnings(
      amountUsd = message.amount,
      userCurrency = CAD,
      userCurrencyAmount = message.amountUserCurrency
    )
    val totalEarnings = UserCurrencyEarnings(
      amountUsd = BigDecimal("17.13"),
      userCurrency = CAD,
      userCurrencyAmount = BigDecimal("23.17")
    )
    val notification = RewardEarningsAddedNotification(userId = USER_ID, earnings = addedEarnings, FR_LOCALE)
    val bqEvent = EarningsAddedBqEventDto(
      metaId = -2,
      userId = USER_ID,
      amount = message.amount,
      challengeEventId = message.challengeEventId,
      createdAt = now
    )

    cashoutService.mock({ getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(message.userId) }, totalEarnings)

    runBlocking { subscriber.handle(message) }

    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(notification)) }
    verifyBlocking(cashoutService) { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) }
    verifyBlocking(messageBus) { publish(EarningsAddedEffect(USER_ID, totalEarnings)) }
    verifyBlocking(bigQueryEventPublisher) { publish(bqEvent) }
  }

  @Test
  fun `SHOULD process message ON handle for special challenge`() {
    val addedEarnings = UserCurrencyEarnings(
      amountUsd = message.amount,
      userCurrency = CAD,
      userCurrencyAmount = message.amountUserCurrency
    )
    val totalEarnings = UserCurrencyEarnings(
      amountUsd = BigDecimal("17.13"),
      userCurrency = CAD,
      userCurrencyAmount = BigDecimal("23.17")
    )
    val notification = RewardSpecialChallengesEarningsAddedNotification(userId = USER_ID, earnings = addedEarnings, FR_LOCALE)
    val bqEvent = EarningsAddedBqEventDto(
      metaId = -2,
      userId = USER_ID,
      amount = message.amount,
      challengeEventId = message.challengeEventId,
      createdAt = now
    )

    cashoutService.mock({ getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(message.userId) }, totalEarnings)

    runBlocking { subscriber.handle(message.copy(challengeType = ChallengeType.SPECIAL)) }

    verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(notification)) }
    verifyBlocking(cashoutService) { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) }
    verifyBlocking(messageBus) { publish(EarningsAddedEffect(USER_ID, totalEarnings)) }
    verifyBlocking(bigQueryEventPublisher) { publish(bqEvent) }
  }

  @Test
  fun `SHOULD not notify user ON handle WHEN reward is zero`() {
    val message = message.copy(
      amount = BigDecimal.ZERO,
      amountUserCurrency = BigDecimal.ZERO,
    )
    val totalEarnings = UserCurrencyEarnings(
      amountUsd = BigDecimal.ZERO,
      userCurrency = CAD,
      userCurrencyAmount = BigDecimal.ZERO
    )

    cashoutService.mock({ getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(Companion.message.userId) }, totalEarnings)

    runBlocking { subscriber.handle(message) }

    verifyBlocking(messageBus) { publish(EarningsAddedEffect(USER_ID, totalEarnings)) }
    verifyNoMoreInteractions(messageBus)
  }

}