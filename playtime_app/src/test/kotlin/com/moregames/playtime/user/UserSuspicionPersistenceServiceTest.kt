package com.moregames.playtime.user

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.table.DatabaseExtension
import com.moregames.base.table.UserSuspicionsTable
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(DatabaseExtension::class)
class UserSuspicionPersistenceServiceTest(
  private val database: Database,
) {

  private lateinit var service: UserSuspicionPersistenceService

  @BeforeEach
  fun before() {
    service = UserSuspicionPersistenceService(database)
  }

  @Test
  fun `SHOULD create user suspicion in DB ON createUserSuspicion`() {
    val userId = database.prepareUser()

    runBlocking {
      service.createUserSuspicion(userId, UserSuspicions.CASHOUT_MORE_THAN_MAX_AMOUNT, "some message here")
    }

    val actual = transaction(database) {
      UserSuspicionsTable.select { UserSuspicionsTable.userId eq userId }
        .first().let { row ->
          UserSuspicion(
            userId = row[UserSuspicionsTable.userId],
            reason = UserSuspicions.entries.find { it.key == row[UserSuspicionsTable.reasonKey] } ?: throw IllegalArgumentException("123"),
            reasonMessage = row[UserSuspicionsTable.reasonMessage])
        }
    }

    assertThat(actual).isEqualTo(UserSuspicion(userId, UserSuspicions.CASHOUT_MORE_THAN_MAX_AMOUNT, "some message here"))
  }

}