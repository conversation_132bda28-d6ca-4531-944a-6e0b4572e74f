package com.moregames.playtime.user.cashout

import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.variations.AndroidFaceScanPreScreenVariation
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.dto.AppVersionDto
import com.moregames.base.util.mock
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.checks.IpService
import com.moregames.playtime.translations.TranslationResource
import com.moregames.playtime.translations.TranslationService
import com.moregames.playtime.user.cashout.FaceScanPreScreenService.Companion.BIPA_HTML_DEFAULT_TEXT
import com.moregames.playtime.user.cashout.dto.AndroidFaceScanPreScreenParams
import com.moregames.playtime.user.cashout.dto.AndroidFaceScanPreScreenType
import com.moregames.playtime.utils.EN_LOCALE
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import kotlin.test.assertEquals
import kotlin.test.assertNull

class FaceScanPreScreenServiceTest {
  private val imageUrlFormat = "http://example.com/%s"
  private val abTestingService: AbTestingService = mock()
  private val ipService: IpService = mock()
  private val imageService: ImageService = mock {
    whenever(it.toUrl(any())).then { invocation -> imageUrlFormat.format(invocation.getArgument(0) as String) }
  }
  private val translationService: TranslationService = mock()

  private val underTest = FaceScanPreScreenService(
    abTestingService = abTestingService,
    ipService = ipService,
    imageService = imageService,
    translationService = translationService
  )

  private companion object {
    const val userId = "user1"
    val appVersion = AppVersionDto(ANDROID, 69)
  }

  @BeforeEach
  fun setUp() {
    abTestingService.mock({ androidFaceScanPreScreenVariation(userId) }, null)
  }

  @Test
  fun `SHOULD not create face scan pre screen params ON call createApiDto WHEN user is not experiment participant`() = runTest {
    abTestingService.mock({ androidFaceScanPreScreenVariation(userId) }, null)
    ipService.mock({ isFromBipaCountry("*******") }, false)

    val result = underTest.createApiDto(userId, "*******", appVersion, EN_LOCALE)

    assertNull(actual = result)
  }

  @Test
  fun `SHOULD create face scan pre screen params ON call createApiDto WHEN user is experiment participant`() = runTest {
    abTestingService.mock({ androidFaceScanPreScreenVariation(userId) }, AndroidFaceScanPreScreenVariation.FullScreenHuman)
    ipService.mock({ isFromBipaCountry("*******") }, false)

    val expected = AndroidFaceScanPreScreenParams(
      screenType = AndroidFaceScanPreScreenType.FULLSCREEN_HUMAN,
      exampleImageUrl = "http://example.com/face_scan_pre_screen_camera_allow_example.png"
    )
    val result = underTest.createApiDto(userId, "*******", appVersion, EN_LOCALE)

    assertEquals(expected = expected, actual = result)
  }

  @Test
  fun `SHOULD create face scan pre screen params with bipa enum ON call createApiDto WHEN user is experiment participant on OLD app version and located in bipa state`(
  ) = runTest {
    abTestingService.mock({ androidFaceScanPreScreenVariation(userId) }, AndroidFaceScanPreScreenVariation.FullScreenHuman)
    ipService.mock({ isFromBipaCountry("*******") }, true)
    ipService.mock({ isFromBlockedBipaState("*******") }, true)

    val expected = AndroidFaceScanPreScreenParams(
      screenType = AndroidFaceScanPreScreenType.FULLSCREEN_BIPA,
      exampleImageUrl = "http://example.com/face_scan_pre_screen_camera_allow_example.png"
    )
    val result = underTest.createApiDto(userId, "*******", appVersion, EN_LOCALE)

    assertEquals(expected = expected, actual = result)
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD create face scan pre screen params with html bipa enum ON call createApiDto WHEN user is on NEW app version and located in bipa state`(
    abVariationParticipant: Boolean
  ) = runTest {
    abTestingService.mock(
      { androidFaceScanPreScreenVariation(userId) },
      if (abVariationParticipant) AndroidFaceScanPreScreenVariation.FullScreenHuman else null
    )
    ipService.mock({ isFromBipaCountry("*******") }, true)
    ipService.mock({ isFromBlockedBipaState("*******") }, true)
    translationService.mock({ translateOrDefault(eq(TranslationResource.CASHOUT_BIPA_PRE_SCREEN_HTML_TEXT), any()) }, BIPA_HTML_DEFAULT_TEXT)

    val expected = AndroidFaceScanPreScreenParams(
      screenType = AndroidFaceScanPreScreenType.HTML_BIPA,
      htmlText = BIPA_HTML_DEFAULT_TEXT,
      exampleImageUrl = "http://example.com/face_scan_pre_screen_camera_allow_example.png"
    )
    val result = underTest.createApiDto(userId, "*******", AppVersionDto(ANDROID, 70), EN_LOCALE)

    assertEquals(expected = expected, actual = result)
  }
}
