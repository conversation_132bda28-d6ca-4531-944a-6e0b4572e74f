package com.moregames.playtime.user.offer

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.variations.MultiOfferwallVariation
import com.moregames.base.app.OfferWallType
import com.moregames.base.app.OfferWallType.*
import com.moregames.base.bus.MessageBus
import com.moregames.base.util.mock
import com.moregames.playtime.buseffects.OfferwallTypeCalculatedEffect
import com.moregames.playtime.user.UserService
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoInteractions
import org.mockito.kotlin.verifyNoMoreInteractions

@OptIn(ExperimentalCoroutinesApi::class)
class AndroidOfferwallTypeServiceTest {

  private val abTestingService: AbTestingService = mock()
  private val userService: UserService = mock()
  private val messageBus: MessageBus = mock()
  private val androidOfferwallRandomTypeCalculationService: AndroidOfferwallRandomTypeCalculationService = mock()

  private val service = AndroidOfferwallTypeService(
    abTestingService = abTestingService,
    userService = userService,
    messageBus = messageBus,
    androidOfferwallRandomTypeCalculationService = androidOfferwallRandomTypeCalculationService
  )

  companion object {
    const val USER_ID = "userId"
    private val ofwTapjoy = TAPJOY
    private val ofwFyber = FYBER
    private val busEffectAdjoe = OfferwallTypeCalculatedEffect(USER_ID, listOf(ADJOE))
    private val busEffectTapjoy = OfferwallTypeCalculatedEffect(USER_ID, listOf(ofwTapjoy))
    private val busEffectFyber = OfferwallTypeCalculatedEffect(USER_ID, listOf(ofwFyber))
    private val busEffectTapjoyAdjoe = OfferwallTypeCalculatedEffect(USER_ID, listOf(ofwTapjoy, ADJOE))
    private val busEffectFyberAdjoe = OfferwallTypeCalculatedEffect(USER_ID, listOf(ofwFyber, ADJOE))
  }

  init {
    Dispatchers.setMain(StandardTestDispatcher())
  }

  @BeforeEach
  fun init() {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.MULTI_OFFERWALL) }, DEFAULT)
    abTestingService.mock({ isEm2Participant(USER_ID) }, true)
    userService.mock({ getUser(USER_ID) }, userDtoStub)
    androidOfferwallRandomTypeCalculationService.mock({ defineOfferwallType() }, TAPJOY)
  }

  @Test
  fun `SHOULD return ADJOE only ON getOfferwallType WHEN user is on that adjoe variation`() = runTest {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.MULTI_OFFERWALL) }, MultiOfferwallVariation.AdjoeOnly)

    val result = service.getOfferwallTypes(USER_ID)
    advanceUntilIdle()

    assertThat(result).isEqualTo(listOf(ADJOE))
    verifyBlocking(messageBus) { publishAsync(busEffectAdjoe) }
  }

  @Test
  fun `SHOULD not return ADJOE only ON getOfferwallType WHEN user is on that adjoe variation but is not em2 participant`() = runTest {
    abTestingService.mock({ isEm2Participant(USER_ID) }, false)
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.MULTI_OFFERWALL) }, MultiOfferwallVariation.AdjoeOnly)

    val result = service.getOfferwallTypes(USER_ID)
    advanceUntilIdle()

    assertThat(result).isEqualTo(listOf(ofwTapjoy))
    verifyBlocking(messageBus) { publishAsync(busEffectTapjoy) }
  }

  @ParameterizedTest
  @ValueSource(strings = ["TAPJOY", "FYBER"])
  fun `SHOULD return random ofw and ADJOE ON getOfferwallType WHEN user is on add adjoe variation`(ofwType: OfferWallType) = runTest {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.MULTI_OFFERWALL) }, MultiOfferwallVariation.AddAdjoe)
    androidOfferwallRandomTypeCalculationService.mock({ defineOfferwallType() }, ofwType)

    val result = service.getOfferwallTypes(USER_ID)
    advanceUntilIdle()

    assertThat(result).isEqualTo(listOf(ofwType, ADJOE))
    when (ofwType) {
      TAPJOY -> verifyBlocking(messageBus) { publishAsync(busEffectTapjoyAdjoe) }
      FYBER -> verifyBlocking(messageBus) { publishAsync(busEffectFyberAdjoe) }
      else -> throw AssertionError("unexpected")
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["TAPJOY", "FYBER"])
  fun `SHOULD return random offerwall type ON getOfferwallTypes WHEN user is not adjoe participant`(ofwType: OfferWallType) = runTest {
    androidOfferwallRandomTypeCalculationService.mock({ defineOfferwallType() }, ofwType)

    val result = service.getOfferwallTypes(USER_ID)
    advanceUntilIdle()

    assertThat(result).isEqualTo(listOf(ofwType))
    when (ofwType) {
      TAPJOY -> verifyBlocking(messageBus) { publishAsync(busEffectTapjoy) }
      FYBER -> verifyBlocking(messageBus) { publishAsync(busEffectFyber) }
      else -> throw AssertionError("unexpected")
    }
  }

  @Test
  fun `SHOULD return offerwall types calculations WHEN we have it tracked in DB`() = runTest {
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(offerWallTypes = listOf(ofwTapjoy)))

    val result = service.getOfferwallTypes(USER_ID)
    advanceUntilIdle()

    assertThat(result).isEqualTo(listOf(ofwTapjoy))
    verifyBlocking(userService) { getUser(USER_ID) }
    verifyNoMoreInteractions(userService)
    verifyNoInteractions(messageBus, androidOfferwallRandomTypeCalculationService)
  }

  @Test
  fun `SHOULD return TAPJOY and FYBER ON getOfferwallType WHEN user is on TapjoyDt variation`() = runTest {
    val expected = listOf(TAPJOY, FYBER)
    val busEffect = OfferwallTypeCalculatedEffect(USER_ID, expected)

    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.MULTI_OFFERWALL) }, MultiOfferwallVariation.TapjoyDt)

    val result = service.getOfferwallTypes(USER_ID)
    advanceUntilIdle()

    assertThat(result).isEqualTo(expected)
    verifyBlocking(messageBus) { publishAsync(busEffect) }
  }

  @Test
  fun `SHOULD return TAPJOY and FYBER in right order ON getOfferwallType WHEN user is on TapjoyDt variation AND offerwall types are already stored in wrong order`() =
    runTest {
      val expected = listOf(TAPJOY, FYBER)

      userService.mock({ getUser(USER_ID) }, userDtoStub.copy(offerWallTypes = listOf(FYBER, TAPJOY)))
      abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.MULTI_OFFERWALL) }, MultiOfferwallVariation.TapjoyDt)

      val result = service.getOfferwallTypes(USER_ID)
      advanceUntilIdle()

      assertThat(result).isEqualTo(expected)
    }

  @Test
  fun `SHOULD return TAPJOY and FYBER ON getOfferwallType WHEN user is on TapjoyDt variation AND NOT em2 participant`() = runTest {
    val expected = listOf(TAPJOY, FYBER)
    val busEffect = OfferwallTypeCalculatedEffect(USER_ID, expected)

    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.MULTI_OFFERWALL) }, MultiOfferwallVariation.TapjoyDt)
    abTestingService.mock({ isEm2Participant(USER_ID) }, false)

    val result = service.getOfferwallTypes(USER_ID)
    advanceUntilIdle()

    assertThat(result).isEqualTo(expected)
    verifyBlocking(messageBus) { publishAsync(busEffect) }
  }

  @Test
  fun `SHOULD return TAPJOY and ADJOE ON getOfferwallType WHEN user is on TapjoyAdjoe variation`() = runTest {
    val expected = listOf(TAPJOY, ADJOE)
    val busEffect = OfferwallTypeCalculatedEffect(USER_ID, expected)

    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.MULTI_OFFERWALL) }, MultiOfferwallVariation.TapjoyAdjoe)

    val result = service.getOfferwallTypes(USER_ID)
    advanceUntilIdle()

    assertThat(result).isEqualTo(expected)
    verifyBlocking(messageBus) { publishAsync(busEffect) }
  }

  @Test
  fun `SHOULD return TAPJOY and ADJOE in right order ON getOfferwallType WHEN user is on TapjoyAdjoe variation AND offerwall types are already stored in wrong order`() =
    runTest {
      val expected = listOf(TAPJOY, ADJOE)

      userService.mock({ getUser(USER_ID) }, userDtoStub.copy(offerWallTypes = listOf(ADJOE, TAPJOY)))
      abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.MULTI_OFFERWALL) }, MultiOfferwallVariation.TapjoyAdjoe)

      val result = service.getOfferwallTypes(USER_ID)
      advanceUntilIdle()

      assertThat(result).isEqualTo(expected)
    }

  @Test
  fun `SHOULD return rnd offerwall ON getOfferwallType WHEN user is on TapjoyDt variation AND NOT em2 participant`() = runTest {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.MULTI_OFFERWALL) }, MultiOfferwallVariation.TapjoyAdjoe)
    abTestingService.mock({ isEm2Participant(USER_ID) }, false)

    val result = service.getOfferwallTypes(USER_ID)
    advanceUntilIdle()

    assertThat(result).isEqualTo(listOf(ofwTapjoy))
    verifyBlocking(messageBus) { publishAsync(busEffectTapjoy) }
  }

  @Test
  fun `SHOULD return FYBER and ADJOE ON getOfferwallType WHEN user is on DtAdjoe variation`() = runTest {
    val expected = listOf(FYBER, ADJOE)
    val busEffect = OfferwallTypeCalculatedEffect(USER_ID, expected)

    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.MULTI_OFFERWALL) }, MultiOfferwallVariation.DtAdjoe)

    val result = service.getOfferwallTypes(USER_ID)
    advanceUntilIdle()

    assertThat(result).isEqualTo(expected)
    verifyBlocking(messageBus) { publishAsync(busEffect) }
  }

  @Test
  fun `SHOULD return FYBER and ADJOE in right order ON getOfferwallType WHEN user is on DtAdjoe variation AND offerwall types are already stored in wrong order`() =
    runTest {
      val expected = listOf(FYBER, ADJOE)

      userService.mock({ getUser(USER_ID) }, userDtoStub.copy(offerWallTypes = listOf(ADJOE, FYBER)))
      abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.MULTI_OFFERWALL) }, MultiOfferwallVariation.DtAdjoe)

      val result = service.getOfferwallTypes(USER_ID)
      advanceUntilIdle()

      assertThat(result).isEqualTo(expected)
    }

  @Test
  fun `SHOULD return rnd offerwall ON getOfferwallType WHEN user is on DtAdjoe variation AND NOT em2 participant`() = runTest {
    abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.MULTI_OFFERWALL) }, MultiOfferwallVariation.DtAdjoe)
    abTestingService.mock({ isEm2Participant(USER_ID) }, false)

    val result = service.getOfferwallTypes(USER_ID)
    advanceUntilIdle()

    assertThat(result).isEqualTo(listOf(ofwTapjoy))
    verifyBlocking(messageBus) { publishAsync(busEffectTapjoy) }
  }

  @Test
  fun `SHOULD return offerwall types in right order ON getOfferwallType WHEN user is on DtAdjoe variation AND there are unaccounted offerwall types`() =
    runTest {
      val expected = listOf(FYBER, ADJOE, TAPJOY)

      userService.mock({ getUser(USER_ID) }, userDtoStub.copy(offerWallTypes = listOf(TAPJOY, ADJOE, FYBER)))
      abTestingService.mock({ assignedVariationValue(USER_ID, ClientExperiment.MULTI_OFFERWALL) }, MultiOfferwallVariation.DtAdjoe)

      val result = service.getOfferwallTypes(USER_ID)
      advanceUntilIdle()

      assertThat(result).isEqualTo(expected)
    }
}