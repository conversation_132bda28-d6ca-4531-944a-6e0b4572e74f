package com.moregames.playtime.user

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.playtime.user.dto.NotificationReportApiDto
import com.moregames.playtime.util.installDefaultContentNegotiation
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.routing.*
import io.ktor.server.testing.*
import kotlinx.serialization.ExperimentalSerializationApi
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import kotlin.test.assertFailsWith

@ExperimentalSerializationApi
class NotificationsControllerTest {
  private val notificationsPersistenceService: NotificationsPersistenceService = mock()

  private fun controller(): Application.() -> Unit = {
    install(IgnoreTrailingSlash)
    installDefaultContentNegotiation()
    routing {
      NotificationsController(
        notificationsPersistenceService
      ).startRouting(this)
    }
  }

  private companion object {
    const val userId = "userId"
  }

  @Test
  fun `SHOULD return OK ON notificationReport endpoint call`() = withTestApplication(controller()) {
    val actual = handleRequest(
      method = HttpMethod.Post,
      uri = "/notificationReport?userId=$userId"
    ) {
      addHeader("Content-Type", "application/json")
      setBody("""{"notificationId":"notificationIdX","action":"actionX"}""")
    }
    val expectedReport = NotificationReportApiDto(
      notificationId = "notificationIdX",
      action = "actionX",
      isPopup = false
    )

    verifyBlocking(notificationsPersistenceService) { saveNotificationReport(userId, expectedReport) }
    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD return exception ON notificationReport endpoint call WHEN body is not set`() = withTestApplication(controller()) {
    assertFailsWith<IllegalArgumentException> {
      handleRequest(
        method = HttpMethod.Post,
        uri = "/notificationReport?userId=$userId"
      ) {
        addHeader("Content-Type", "application/json")
      }
    }.let {
      assertThat(it.message).isEqualTo("Notification report is empty")
    }
  }
}