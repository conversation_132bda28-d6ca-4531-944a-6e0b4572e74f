package com.moregames.playtime.user.coingoal

import assertk.all
import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.util.io
import com.moregames.playtime.games.IosGameService
import com.moregames.playtime.ios.dto.GameApiDto
import com.moregames.playtime.user.UserService
import com.moregames.playtime.util.DEFAULT_USER_LOCALE
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.stub

class GameCoinGoalsGamesProviderTest {
  private val iosGameService = mock<IosGameService>()
  private val scope = TestScope()
  private val userService = mock<UserService> {
    onBlocking { getUser("userId") } doReturn userDtoStub.copy(id = "userId", locale = DEFAULT_USER_LOCALE)
  }
  private val underTest = GameCoinGoalsGamesProvider(
    iosGameService,
    { scope.io() },
    userService,
  )

  @Test
  fun `SHOULD return ios games for user`() = scope.runTest {
    iosGameService.stub {
      onBlocking { loadNewGames("userId", DEFAULT_USER_LOCALE, null) } doReturn iosNewGames
      onBlocking { loadPlayedGames("userId", DEFAULT_USER_LOCALE) } doReturn iosPlayedGames
    }

    val result = underTest.ios("userId")
    assertThat(result).all {
      transform { it.newGames }.isEqualTo(iosNewGames.map { it.id })
      transform { it.playedGames }.isEqualTo(iosPlayedGames.map { it.id })
    }
  }

  companion object {
    val gameApiDtoStub = GameApiDto(
      id = 500002,
      applicationId = "com.gimica.solitaireverse",
      name = "Solitaire Verse",
      description = "The more games you win the more coins you get",
      iconFilename = "https://storage.googleapis.com/public-playtime/images/solitaire_verse.jpg",
      imageFilename = "https://storage.googleapis.com/public-playtime/images/solitaire_verse_preview.jpg",
      infoTextInstall = "Install and Play Solitaire Verse<br><br>The more games you win the more coins you get",
      iosApplicationId = "id1667538256",
      iosGameUrl = "solitaireverse:LaunchSolitaireVerse"
    )
    val iosNewGames = buildList {
      (1..3).forEach { add(gameApiDtoStub.copy(id = it)) }
    }
    val iosPlayedGames = buildList {
      (4..6).forEach { add(gameApiDtoStub.copy(id = it)) }
    }
  }
}