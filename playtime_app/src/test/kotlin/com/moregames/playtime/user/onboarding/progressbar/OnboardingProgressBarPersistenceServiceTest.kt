package com.moregames.playtime.user.onboarding.progressbar

import assertk.all
import assertk.assertThat
import assertk.assertions.hasSize
import assertk.assertions.index
import assertk.assertions.isEqualTo
import com.moregames.base.table.DatabaseExtension
import com.moregames.playtime.user.prepareUser
import kotlinx.coroutines.test.runTest
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.batchInsert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(DatabaseExtension::class)
class OnboardingProgressBarPersistenceServiceTest(
  private val database: Database,
) {
  private val underTest = OnboardingProgressBarPersistenceService(database)

  @Test
  fun initAllProgressBarSteps() = runTest {
    val user = database.prepareUser()

    underTest.initAllProgressBarSteps(
      user, listOf(
        OnboardingProgressBarStepConfig("text", OnboardingProgressBarStepType.SIMPLE, OnboardingProgressBarStepStatus.COMPLETED, 1),
      )
    )

    transaction(database) {
      OnboardingProgressBarStateTable.select { OnboardingProgressBarStateTable.userId eq user }.toList().let { steps ->
        assertThat(steps).hasSize(1)
        assertThat(steps).index(0).all {
          transform { it[OnboardingProgressBarStateTable.userId] }.isEqualTo(user)
          transform { it[OnboardingProgressBarStateTable.text] }.isEqualTo("text")
          transform { it[OnboardingProgressBarStateTable.type] }.isEqualTo(OnboardingProgressBarStepType.SIMPLE)
          transform { it[OnboardingProgressBarStateTable.status] }.isEqualTo(OnboardingProgressBarStepStatus.COMPLETED)
          transform { it[OnboardingProgressBarStateTable.order] }.isEqualTo(1)
        }
      }
    }
  }

  @Test
  fun getAllProgressBarSteps() = runTest {
    val user = database.prepareUser()

    transaction(database) {
      OnboardingProgressBarStateTable.batchInsert(
        listOf(
          OnboardingProgressBarStepConfig("text", OnboardingProgressBarStepType.SIMPLE, OnboardingProgressBarStepStatus.COMPLETED, 1),
          OnboardingProgressBarStepConfig("text2", OnboardingProgressBarStepType.SIMPLE, OnboardingProgressBarStepStatus.COMPLETED, 2),
        )
      ) { step ->
        this[OnboardingProgressBarStateTable.userId] = user
        this[OnboardingProgressBarStateTable.text] = step.text
        this[OnboardingProgressBarStateTable.type] = step.type
        this[OnboardingProgressBarStateTable.status] = step.status
        this[OnboardingProgressBarStateTable.order] = step.order
      }
    }

    val progressBar = underTest.getAllProgressBarSteps(user)

    assertThat(progressBar).hasSize(2)
    assertThat(progressBar).index(0).all {
      transform { it.text }.isEqualTo("text")
      transform { it.type }.isEqualTo(OnboardingProgressBarStepType.SIMPLE)
      transform { it.status }.isEqualTo(OnboardingProgressBarStepStatus.COMPLETED)
    }
    assertThat(progressBar).index(1).all {
      transform { it.text }.isEqualTo("text2")
      transform { it.type }.isEqualTo(OnboardingProgressBarStepType.SIMPLE)
      transform { it.status }.isEqualTo(OnboardingProgressBarStepStatus.COMPLETED)
    }
  }

  @Test
  fun updateProgressBarStepStatus() = runTest {
    val user = database.prepareUser()
    transaction(database) {
      OnboardingProgressBarStateTable.batchInsert(
        listOf(
          OnboardingProgressBarStepConfig("text", OnboardingProgressBarStepType.SIMPLE, OnboardingProgressBarStepStatus.INCOMPLETE, 1),
        )
      ) { step ->
        this[OnboardingProgressBarStateTable.userId] = user
        this[OnboardingProgressBarStateTable.text] = step.text
        this[OnboardingProgressBarStateTable.type] = step.type
        this[OnboardingProgressBarStateTable.status] = step.status
        this[OnboardingProgressBarStateTable.order] = step.order
      }
    }

    underTest.updateProgressBarStepStatus(user, OnboardingProgressBarStepType.SIMPLE, OnboardingProgressBarStepStatus.COMPLETED)

    transaction(database) {
      OnboardingProgressBarStateTable.select { OnboardingProgressBarStateTable.userId eq user }.toList().let { steps ->
        assertThat(steps).hasSize(1)
        assertThat(steps).index(0).all {
          transform { it[OnboardingProgressBarStateTable.userId] }.isEqualTo(user)
          transform { it[OnboardingProgressBarStateTable.text] }.isEqualTo("text")
          transform { it[OnboardingProgressBarStateTable.type] }.isEqualTo(OnboardingProgressBarStepType.SIMPLE)
          transform { it[OnboardingProgressBarStateTable.status] }.isEqualTo(OnboardingProgressBarStepStatus.COMPLETED)
          transform { it[OnboardingProgressBarStateTable.order] }.isEqualTo(1)
        }
      }
    }
  }
}