package com.moregames.playtime.notifications.android.silentcoinupdate

import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.variations.AndroidSilentCoinsUpdateNotificationVariation
import com.moregames.base.app.BuildVariant
import com.moregames.base.junit.TypedVariationSource
import com.moregames.base.util.TEST_IO_SCOPE
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.*
import java.time.Instant
import java.util.UUID
import kotlin.booleanArrayOf
import kotlin.test.assertEquals

@OptIn(ExperimentalCoroutinesApi::class)
class AndroidSilentCoinUpdateServiceTest {

  private val abTestingService: AbTestingService = mock()
  private val timeService: TimeService = mock()
  private val androidSilentCoinUpdatePersistenceService: AndroidSilentCoinUpdatePersistenceService = mock()

  private val testUserId: String = UUID.randomUUID().toString()
  private val now = Instant.now()

  private val underTest = AndroidSilentCoinUpdateService(
    buildVariant = BuildVariant.TEST,
    abTestingService = abTestingService,
    timeService = timeService,
    androidSilentCoinUpdatePersistenceService = androidSilentCoinUpdatePersistenceService,
    coroutineScope = { TEST_IO_SCOPE },
  )

  @BeforeEach
  fun init() {
    Dispatchers.setMain(StandardTestDispatcher())
    timeService.mock({ now() }, now)
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD return isFirstNotificationSent WHEN called`(isFirstNotificationSent: Boolean) = runTest {
    androidSilentCoinUpdatePersistenceService.mock({ isFirstNotificationSent(testUserId) }, isFirstNotificationSent)

    val actual = underTest.isFirstNotificationSent(userId = testUserId)
    val actual2 = underTest.isFirstNotificationSent(userId = testUserId)

    // Assert: persistence service only ever got invoked once
    verifyBlocking(androidSilentCoinUpdatePersistenceService, mode = times(1)) {
      isFirstNotificationSent(testUserId)
    }
    assertEquals(isFirstNotificationSent, actual)
    assertEquals(isFirstNotificationSent, actual2)
  }

  @Test
  fun `SHOULD store notification sent WHEN called`() = runTest {
    runBlocking { underTest.storeFirstNotificationSent(userId = testUserId) }

    verifyBlocking(androidSilentCoinUpdatePersistenceService) {
      storeFirstNotificationSent(userId = testUserId, notifiedAt = now)
    }
  }

  @Test
  fun `SHOULD reset cache after storing first notification sent WHEN called`() = runTest {
    androidSilentCoinUpdatePersistenceService.mock({ isFirstNotificationSent(testUserId) }, false)

    val actual = underTest.isFirstNotificationSent(userId = testUserId)
    underTest.storeFirstNotificationSent(userId = testUserId)
    androidSilentCoinUpdatePersistenceService.mock({ isFirstNotificationSent(testUserId) }, true)
    val actual2 = underTest.isFirstNotificationSent(userId = testUserId)

    verifyBlocking(androidSilentCoinUpdatePersistenceService, mode = times(2)) {
      isFirstNotificationSent(testUserId)
    }
    assertEquals(expected = false, actual)
    assertEquals(expected = true, actual2)
  }

  @ParameterizedTest
  @TypedVariationSource(AndroidSilentCoinsUpdateNotificationVariation::class, keys = ["silentAll", "fullFirstSilentFollowing"])
  fun `SHOULD return silent notification ON silentCoinUpdateNotificationSettings when on SilentAll variation or non first notification`(
    variation: AndroidSilentCoinsUpdateNotificationVariation
  ) = runTest {
    abTestingService.mock(
      { assignedVariationValue(testUserId,ClientExperiment.ANDROID_SILENT_COINS_UPDATE_NOTIFICATION) },
      variation
    )
    androidSilentCoinUpdatePersistenceService.mock({ isFirstNotificationSent(testUserId) }, true)

    val actual = underTest.silentCoinUpdateNotificationSettings(testUserId)

    assertEquals(expected = AndroidSilentCoinUpdateService.SilentNotificationSettings(vibrationEnabled = false, soundEnabled = false), actual)
  }

  @ParameterizedTest
  @TypedVariationSource(AndroidSilentCoinsUpdateNotificationVariation::class, keys = ["vibrateOnlyAll", "fullFirstVibrateFollowing"])
  fun `SHOULD return vibration notification ON silentCoinUpdateNotificationSettings when on VibrateOnlyAll variation or non first notification`(
    variation: AndroidSilentCoinsUpdateNotificationVariation
  ) = runTest {
    abTestingService.mock(
      { assignedVariationValue(testUserId,ClientExperiment.ANDROID_SILENT_COINS_UPDATE_NOTIFICATION) },
      variation
    )
    androidSilentCoinUpdatePersistenceService.mock({ isFirstNotificationSent(testUserId) }, true)

    val actual = underTest.silentCoinUpdateNotificationSettings(testUserId)

    assertEquals(expected = AndroidSilentCoinUpdateService.SilentNotificationSettings(vibrationEnabled = true, soundEnabled = false), actual)
  }

  @ParameterizedTest
  @TypedVariationSource(AndroidSilentCoinsUpdateNotificationVariation::class, keys = ["fullFirstVibrateFollowing", "fullFirstSilentFollowing"])
  fun `SHOULD return full notification ON silentCoinUpdateNotificationSettings when on FullFirst variations and first notification`(
    variation: AndroidSilentCoinsUpdateNotificationVariation
  ) = runTest {
    abTestingService.mock(
      { assignedVariationValue(testUserId,ClientExperiment.ANDROID_SILENT_COINS_UPDATE_NOTIFICATION) },
      variation
    )
    androidSilentCoinUpdatePersistenceService.mock({ isFirstNotificationSent(testUserId) }, false)

    val actual = underTest.silentCoinUpdateNotificationSettings(testUserId)

    assertEquals(expected = AndroidSilentCoinUpdateService.SilentNotificationSettings(), actual)
  }

}
