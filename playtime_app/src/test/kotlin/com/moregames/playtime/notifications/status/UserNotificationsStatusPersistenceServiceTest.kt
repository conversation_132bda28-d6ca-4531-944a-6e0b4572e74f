package com.moregames.playtime.notifications.status

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isNull
import com.moregames.base.table.DatabaseExtension
import com.moregames.playtime.user.prepareUser
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(DatabaseExtension::class)
class UserNotificationsStatusPersistenceServiceTest(
  private val database: Database,
) {

  private lateinit var service: UserNotificationsStatusPersistenceService

  @BeforeEach
  fun before() {
    service = UserNotificationsStatusPersistenceService(database)
  }

  @Test
  fun `SHOULD return true ON areNotificationsEnabled WHEN user notifications are enabled`() {
    val userId = database.prepareUser()
    insertNotificationStatus(userId, true)
    val actual = runBlocking {
      service.areNotificationsEnabled(userId)
    }
    assertThat(actual).isEqualTo(true)
  }

  @Test
  fun `SHOULD return false ON areNotificationsEnabled WHEN user notifications are disabled`() {
    val userId = database.prepareUser()
    insertNotificationStatus(userId, false)
    val actual = runBlocking {
      service.areNotificationsEnabled(userId)
    }
    assertThat(actual).isEqualTo(false)
  }

  @Test
  fun `SHOULD return null ON areNotificationsEnabled WHEN user doesn't exist in the table`() {
    val actual = runBlocking {
      service.areNotificationsEnabled("user1")
    }
    assertThat(actual).isNull()
  }

  @Test
  fun `SHOULD update user notifications status ON addOrUpdateNotificationsStatus`() {
    val userId = database.prepareUser()
    insertNotificationStatus(userId, false)
    runBlocking {
      service.addOrUpdateNotificationsStatus(userId, true)
    }
    val actual = runBlocking {
      service.areNotificationsEnabled(userId)
    }
    assertThat(actual).isEqualTo(true)
  }

  @Test
  fun `SHOULD add user notifications status ON addOrUpdateNotificationsStatus`() {
    val userId = database.prepareUser()
    runBlocking {
      service.addOrUpdateNotificationsStatus(userId, true)
    }
    val actual = runBlocking {
      service.areNotificationsEnabled(userId)
    }
    assertThat(actual).isEqualTo(true)
  }

  @Test
  fun `SHOULD filter out users with turned off notifications`() {
    val userIds = (1..10).map { database.prepareUser() }
    userIds.take(3).forEach {
      insertNotificationStatus(it, false)
    }
    userIds.takeLast(3).forEach {
      insertNotificationStatus(it, true)
    }

    runBlocking {
      service.whichUsersNotificationsDisabled(userIds)
    }.let { assertThat(it.toSet()).isEqualTo(userIds.take(3).toSet()) }
  }

  private fun insertNotificationStatus(userId: String, enabled: Boolean = true) =
    transaction(database) {
      UserNotificationsStatusTable.insert {
        it[UserNotificationsStatusTable.userId] = userId
        it[UserNotificationsStatusTable.enabled] = enabled
      }
    }
}