package com.moregames.playtime.carousel

import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.junit.MockExtension
import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.util.mock
import com.moregames.playtime.carousel.domain.TaskDefinition
import com.moregames.playtime.carousel.domain.UserCarouselTask
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.user.challenge.progress.ObjectiveProgressCalculator
import com.moregames.playtime.user.challenge.progress.ObjectiveProgressCalculatorType
import com.moregames.playtime.user.objectives.CalculatedObjectiveProgress
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.*
import java.util.*

@ExtendWith(MockExtension::class)
class CarouselProgressTrackingHandlerTest(
  private val carouselService: CarouselService,
  private val gamesService: GamesService,
  private val messageBus: MessageBus,
) {

  private companion object {
    const val USER_ID = "user123"
    const val APPLICATION_ID = "com.example.game"
    const val GAME_ID = 1001
    val TASK_ID = UUID.randomUUID()

    val taskDefinition = TaskDefinition(
      gameId = GAME_ID,
      titleTranslation = "Complete 5 levels",
      icon = "level_icon",
      progressMax = 100,
      goal = 5,
      order = 1,
      calculator = ObjectiveProgressCalculatorType.MILESTONE,
      enabled = true,
    )
  }

  private val mockCalculator = mock<ObjectiveProgressCalculator>()
  private val calculators = mapOf("MILESTONE" to mockCalculator)

  private val underTest = CarouselProgressTrackingHandler(
    carouselService = carouselService,
    gamesService = gamesService,
    calculators = calculators,
    messageBus = messageBus,
  )

  @Test
  fun `SHOULD return early ON handleUserCarouselProgress WHEN game id not found`() = runBlocking {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(USER_ID, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, null)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService, never()) { findActiveUserTask(any(), any()) }
  }

  @Test
  fun `SHOULD return early ON handleUserCarouselProgress WHEN no active task found`(): Unit = runBlocking {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(USER_ID, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, null)

    underTest.handleUserCarouselProgress(command)

    verify(mockCalculator, never()).calculateProgress(any(), any())
  }

  @Test
  fun `SHOULD return early ON handleUserCarouselProgress WHEN calculator not found`() = runBlocking {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(USER_ID, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 50, null)

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)

    val handlerWithoutCalculator = CarouselProgressTrackingHandler(
      carouselService = carouselService,
      gamesService = gamesService,
      calculators = emptyMap(),
      messageBus = messageBus,
    )

    handlerWithoutCalculator.handleUserCarouselProgress(command)

    verifyBlocking(carouselService, never()) { updateTaskProgress(any(), any(), any()) }
  }

  @Test
  fun `SHOULD return early ON handleUserCarouselProgress WHEN new progress not greater than current`() = runBlocking {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(USER_ID, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 50, null)
    val calculatedProgress = CalculatedObjectiveProgress(40, null) // Less than current progress

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(progressDto, task) }, calculatedProgress)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService, never()) { updateTaskProgress(any(), any(), any()) }
  }

  @Test
  fun `SHOULD update task progress ON handleUserCarouselProgress WHEN progress increases but task not completed`() = runBlocking {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(USER_ID, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 50, null)
    val calculatedProgress = CalculatedObjectiveProgress(75, "achievement_data")
    val updatedTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 75, "achievement_data")

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(progressDto, task) }, calculatedProgress)
    carouselService.mock({ updateTaskProgress(task, 75, "achievement_data") }, updatedTask)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService) { updateTaskProgress(task, 75, "achievement_data") }
    verifyBlocking(carouselService, never()) { markTaskAsFinished(any()) }
    verify(messageBus, never()).publish(any<CarouselTaskFinishedEvent>())
  }

  @Test
  fun `SHOULD complete task and publish event ON handleUserCarouselProgress WHEN progress reaches maximum`() = runBlocking {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(USER_ID, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 90, null)
    val calculatedProgress = CalculatedObjectiveProgress(100, "final_achievement")
    val completedTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 100, "final_achievement")

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(progressDto, task) }, calculatedProgress)
    carouselService.mock({ updateTaskProgress(task, 100, "final_achievement") }, completedTask)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService) { updateTaskProgress(task, 100, "final_achievement") }
    verifyBlocking(carouselService) { markTaskAsFinished(TASK_ID) }
    verify(messageBus).publish(CarouselTaskFinishedEvent(TASK_ID, USER_ID))
  }

  @Test
  fun `SHOULD cap progress at progressMax ON handleUserCarouselProgress WHEN calculated progress exceeds maximum`() = runBlocking {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(USER_ID, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 50, null)
    val calculatedProgress = CalculatedObjectiveProgress(150, "achievement_data") // Exceeds progressMax of 100
    val cappedTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 100, "achievement_data")

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(progressDto, task) }, calculatedProgress)
    carouselService.mock({ updateTaskProgress(task, 100, "achievement_data") }, cappedTask)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService) { updateTaskProgress(task, 100, "achievement_data") }
    verifyBlocking(carouselService) { markTaskAsFinished(TASK_ID) }
    verify(messageBus).publish(CarouselTaskFinishedEvent(TASK_ID, USER_ID))
  }

  @Test
  fun `SHOULD not complete task ON handleUserCarouselProgress WHEN updated task is not InProgress`() = runBlocking {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(USER_ID, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 90, null)
    val calculatedProgress = CalculatedObjectiveProgress(100, "final_achievement")
    val unclaimedTask = UserCarouselTask.Unclaimed(TASK_ID, USER_ID, taskDefinition) // Not InProgress

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(progressDto, task) }, calculatedProgress)
    carouselService.mock({ updateTaskProgress(task, 100, "final_achievement") }, unclaimedTask)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService) { updateTaskProgress(task, 100, "final_achievement") }
    verifyBlocking(carouselService, never()) { markTaskAsFinished(any()) }
    verify(messageBus, never()).publish(any<CarouselTaskFinishedEvent>())
  }

  @Test
  fun `SHOULD handle different progress DTO types ON handleUserCarouselProgress`() = runBlocking {
    val milestoneDto = UserChallengeProgressDto.MilestoneProgressDto(USER_ID, APPLICATION_ID, 5)
    val command = HandleCarouselProgressCommand(milestoneDto)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 50, null)
    val calculatedProgress = CalculatedObjectiveProgress(60, "milestone_5")
    val updatedTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 60, "milestone_5")

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(milestoneDto, task) }, calculatedProgress)
    carouselService.mock({ updateTaskProgress(task, 60, "milestone_5") }, updatedTask)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService) { updateTaskProgress(task, 60, "milestone_5") }
  }

  @Test
  fun `SHOULD handle ScoreCompletedProgressDto ON handleUserCarouselProgress`() = runBlocking {
    val scoreDto = UserChallengeProgressDto.ScoreCompletedProgressDto(USER_ID, APPLICATION_ID, 1500, true)
    val command = HandleCarouselProgressCommand(scoreDto)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 30, null)
    val calculatedProgress = CalculatedObjectiveProgress(40, null)
    val updatedTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 40, null)

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(scoreDto, task) }, calculatedProgress)
    carouselService.mock({ updateTaskProgress(task, 40, null) }, updatedTask)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService) { updateTaskProgress(task, 40, null) }
  }

  @Test
  fun `SHOULD handle LevelIdProgressDto ON handleUserCarouselProgress`() = runBlocking {
    val levelDto = UserChallengeProgressDto.LevelIdProgressDto(USER_ID, APPLICATION_ID, "level_10")
    val command = HandleCarouselProgressCommand(levelDto)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 20, null)
    val calculatedProgress = CalculatedObjectiveProgress(30, "level_10")
    val updatedTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 30, "level_10")

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(levelDto, task) }, calculatedProgress)
    carouselService.mock({ updateTaskProgress(task, 30, "level_10") }, updatedTask)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService) { updateTaskProgress(task, 30, "level_10") }
  }

  @Test
  fun `SHOULD handle progress equal to current progress ON handleUserCarouselProgress`() = runBlocking {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(USER_ID, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 50, null)
    val calculatedProgress = CalculatedObjectiveProgress(50, null) // Equal to current progress

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(progressDto, task) }, calculatedProgress)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService, never()) { updateTaskProgress(any(), any(), any()) }
  }

  @Test
  fun `SHOULD handle different calculator types ON handleUserCarouselProgress`() = runBlocking {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(USER_ID, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)
    val scoreTaskDefinition = taskDefinition.copy(calculator = ObjectiveProgressCalculatorType.SCORE_PROGRESS)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, scoreTaskDefinition, 50, null)
    val calculatedProgress = CalculatedObjectiveProgress(75, null)
    val updatedTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, scoreTaskDefinition, 75, null)

    val scoreCalculator = mock<ObjectiveProgressCalculator>()
    val calculatorsWithScore = mapOf("SCORE_PROGRESS" to scoreCalculator)
    val handlerWithScoreCalculator = CarouselProgressTrackingHandler(
      carouselService = carouselService,
      gamesService = gamesService,
      calculators = calculatorsWithScore,
      messageBus = messageBus,
    )

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    scoreCalculator.mock({ calculateProgress(progressDto, task) }, calculatedProgress)
    carouselService.mock({ updateTaskProgress(task, 75, null) }, updatedTask)

    handlerWithScoreCalculator.handleUserCarouselProgress(command)

    verifyBlocking(carouselService) { updateTaskProgress(task, 75, null) }
  }

  @Test
  fun `SHOULD handle task with null achievement ON handleUserCarouselProgress`() = runBlocking {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(USER_ID, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 50, "existing_achievement")
    val calculatedProgress = CalculatedObjectiveProgress(60, null) // Null achievement
    val updatedTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 60, null)

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(progressDto, task) }, calculatedProgress)
    carouselService.mock({ updateTaskProgress(task, 60, null) }, updatedTask)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService) { updateTaskProgress(task, 60, null) }
  }

  @Test
  fun `SHOULD handle task completion exactly at progressMax ON handleUserCarouselProgress`() = runBlocking {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(USER_ID, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 99, null)
    val calculatedProgress = CalculatedObjectiveProgress(100, "final")
    val completedTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 100, "final")

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(progressDto, task) }, calculatedProgress)
    carouselService.mock({ updateTaskProgress(task, 100, "final") }, completedTask)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService) { updateTaskProgress(task, 100, "final") }
    verifyBlocking(carouselService) { markTaskAsFinished(TASK_ID) }
    verify(messageBus).publish(CarouselTaskFinishedEvent(TASK_ID, USER_ID))
  }

  @Test
  fun `SHOULD handle AmountMilestoneProgressDto ON handleUserCarouselProgress`() = runBlocking {
    val amountDto = UserChallengeProgressDto.AmountMilestoneProgressDto(USER_ID, APPLICATION_ID, 10, 500)
    val command = HandleCarouselProgressCommand(amountDto)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 25, null)
    val calculatedProgress = CalculatedObjectiveProgress(35, "500_10")
    val updatedTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 35, "500_10")

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(amountDto, task) }, calculatedProgress)
    carouselService.mock({ updateTaskProgress(task, 35, "500_10") }, updatedTask)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService) { updateTaskProgress(task, 35, "500_10") }
  }

  @Test
  fun `SHOULD handle TmProgressDto ON handleUserCarouselProgress`() = runBlocking {
    val tmDto = UserChallengeProgressDto.TmProgressDto(USER_ID, APPLICATION_ID, 1500, true, 15)
    val command = HandleCarouselProgressCommand(tmDto)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 40, null)
    val calculatedProgress = CalculatedObjectiveProgress(50, "tm_level_15")
    val updatedTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 50, "tm_level_15")

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(tmDto, task) }, calculatedProgress)
    carouselService.mock({ updateTaskProgress(task, 50, "tm_level_15") }, updatedTask)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService) { updateTaskProgress(task, 50, "tm_level_15") }
  }

  @Test
  fun `SHOULD handle task with zero current progress ON handleUserCarouselProgress`() = runBlocking {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(USER_ID, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 0, null)
    val calculatedProgress = CalculatedObjectiveProgress(10, "first_progress")
    val updatedTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 10, "first_progress")

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(progressDto, task) }, calculatedProgress)
    carouselService.mock({ updateTaskProgress(task, 10, "first_progress") }, updatedTask)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService) { updateTaskProgress(task, 10, "first_progress") }
  }

  @Test
  fun `SHOULD handle task with maximum progress minus one ON handleUserCarouselProgress`() = runBlocking {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(USER_ID, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 99, null)
    val calculatedProgress = CalculatedObjectiveProgress(99, "almost_done") // Same progress

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(progressDto, task) }, calculatedProgress)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService, never()) { updateTaskProgress(any(), any(), any()) }
  }

  @Test
  fun `SHOULD handle task with different user IDs ON handleUserCarouselProgress`() = runBlocking {
    val differentUserId = "different_user_456"
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(differentUserId, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)
    val task = UserCarouselTask.InProgress(TASK_ID, differentUserId, taskDefinition, 30, null)
    val calculatedProgress = CalculatedObjectiveProgress(40, null)
    val updatedTask = UserCarouselTask.InProgress(TASK_ID, differentUserId, taskDefinition, 40, null)

    gamesService.mock({ getGameId(differentUserId, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(differentUserId, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(progressDto, task) }, calculatedProgress)
    carouselService.mock({ updateTaskProgress(task, 40, null) }, updatedTask)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService) { updateTaskProgress(task, 40, null) }
  }

  @Test
  fun `SHOULD handle task with different game IDs ON handleUserCarouselProgress`() = runBlocking {
    val differentGameId = 2002
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(USER_ID, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)
    val differentTaskDefinition = taskDefinition.copy(gameId = differentGameId)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, differentTaskDefinition, 30, null)
    val calculatedProgress = CalculatedObjectiveProgress(40, null)
    val updatedTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, differentTaskDefinition, 40, null)

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, differentGameId)
    carouselService.mock({ findActiveUserTask(USER_ID, differentGameId) }, task)
    mockCalculator.mock({ calculateProgress(progressDto, task) }, calculatedProgress)
    carouselService.mock({ updateTaskProgress(task, 40, null) }, updatedTask)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService) { updateTaskProgress(task, 40, null) }
  }

  @Test
  fun `SHOULD handle task with different progressMax values ON handleUserCarouselProgress`() = runBlocking {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(USER_ID, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)
    val smallProgressMaxDefinition = taskDefinition.copy(progressMax = 50)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, smallProgressMaxDefinition, 40, null)
    val calculatedProgress = CalculatedObjectiveProgress(60, null) // Exceeds progressMax of 50
    val cappedTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, smallProgressMaxDefinition, 50, null)

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(progressDto, task) }, calculatedProgress)
    carouselService.mock({ updateTaskProgress(task, 50, null) }, cappedTask)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService) { updateTaskProgress(task, 50, null) }
    verifyBlocking(carouselService) { markTaskAsFinished(TASK_ID) }
    verify(messageBus).publish(CarouselTaskFinishedEvent(TASK_ID, USER_ID))
  }

  @Test
  fun `SHOULD handle task with progressMax of 1 ON handleUserCarouselProgress`() = runBlocking {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(USER_ID, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)
    val singleProgressDefinition = taskDefinition.copy(progressMax = 1)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, singleProgressDefinition, 0, null)
    val calculatedProgress = CalculatedObjectiveProgress(1, "completed")
    val completedTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, singleProgressDefinition, 1, "completed")

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(progressDto, task) }, calculatedProgress)
    carouselService.mock({ updateTaskProgress(task, 1, "completed") }, completedTask)

    underTest.handleUserCarouselProgress(command)

    verifyBlocking(carouselService) { updateTaskProgress(task, 1, "completed") }
    verifyBlocking(carouselService) { markTaskAsFinished(TASK_ID) }
    verify(messageBus).publish(CarouselTaskFinishedEvent(TASK_ID, USER_ID))
  }

  @Test
  fun `SHOULD handle multiple calculator types in map ON handleUserCarouselProgress`() = runBlocking {
    val progressDto = UserChallengeProgressDto.ScoreProgressDto(USER_ID, APPLICATION_ID, 100)
    val command = HandleCarouselProgressCommand(progressDto)
    val task = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 30, null)
    val calculatedProgress = CalculatedObjectiveProgress(40, null)
    val updatedTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 40, null)

    val multipleCalculators = mapOf(
      "MILESTONE" to mockCalculator,
      "SCORE_PROGRESS" to mock<ObjectiveProgressCalculator>(),
      "LEVEL_ID" to mock<ObjectiveProgressCalculator>()
    )
    val handlerWithMultipleCalculators = CarouselProgressTrackingHandler(
      carouselService = carouselService,
      gamesService = gamesService,
      calculators = multipleCalculators,
      messageBus = messageBus,
    )

    gamesService.mock({ getGameId(USER_ID, AppPlatform.ANDROID) }, GAME_ID)
    carouselService.mock({ findActiveUserTask(USER_ID, GAME_ID) }, task)
    mockCalculator.mock({ calculateProgress(progressDto, task) }, calculatedProgress)
    carouselService.mock({ updateTaskProgress(task, 40, null) }, updatedTask)

    handlerWithMultipleCalculators.handleUserCarouselProgress(command)

    verifyBlocking(carouselService) { updateTaskProgress(task, 40, null) }
  }
}