package com.moregames.playtime.revenue.adjoe

import com.moregames.base.bus.MessageBus
import com.moregames.base.messaging.dto.RevenueReceivedEventDto
import com.moregames.base.offers.dto.OfferAction
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.base.util.throwException
import com.moregames.playtime.user.offer.AndroidOfferwallService
import com.moregames.playtime.utils.adjoeReportStub
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.exceptions.ExposedSQLException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.whenever
import java.math.BigDecimal
import java.sql.SQLIntegrityConstraintViolationException
import java.sql.SQLTimeoutException
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.test.assertFailsWith

class AdjoeReportingManagerTest {
  private val adjoePersistenceService: AdjoePersistenceService = mock()
  private val androidOfferwallService: AndroidOfferwallService = mock()
  private val timeService: TimeService = mock()
  private val adjoeCoinsReportValidationService: AdjoeCoinsReportValidationService = mock()
  private val adjoeReportingCalculationsService: AdjoeReportingCalculationsService = mock()
  private val messageBus: MessageBus = mock()

  private val service = AdjoeReportingManager(
    adjoePersistenceService = adjoePersistenceService,
    androidOfferwallService = androidOfferwallService,
    messageBus = messageBus,
    timeService = timeService,
    adjoeCoinsReportValidationService = adjoeCoinsReportValidationService,
    adjoeReportingCalculationsService = adjoeReportingCalculationsService
  )

  companion object {
    private val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    private const val COINS = 88000
    private val revenueEvent = RevenueReceivedEventDto(
      eventId = adjoeReportStub.id,
      userId = adjoeReportStub.userId,
      timestamp = now,
      source = RevenueReceivedEventDto.RevenueSource.ADJOE,
      amount = BigDecimal("2.000000"),
      createdAt = now,
      networkId = -1
    )
    private val calculationResult = AdjoeCoinsAndDollarsCalculationResult(
      usdAmount = BigDecimal("2.000000"),
      coins = COINS
    )
    val constraintViolationException = mock<ExposedSQLException> {
      whenever(it.cause).thenReturn(SQLIntegrityConstraintViolationException())
    }
    val someOtherExposedSQLException = mock<ExposedSQLException> {
      whenever(it.cause).thenReturn(SQLTimeoutException())
    }
  }

  @BeforeEach
  fun init() {
    adjoeCoinsReportValidationService.mock({ isReportValid(adjoeReportStub) }, true)
    timeService.mock({ now() }, now)
    adjoeReportingCalculationsService.mock({ calculateCoinsAndDollars(adjoeReportStub) }, calculationResult)
  }

  @Test
  fun `SHOULD check request and emit revenue and coins ON onTapjoyCoinsReport`() {
    runBlocking {
      service.onAdjoeCoinsReport(adjoeReportStub)
    }

    verifyBlocking(adjoePersistenceService) { saveReport(adjoeReportStub) }
    verifyBlocking(adjoeReportingCalculationsService) { calculateCoinsAndDollars(adjoeReportStub) }
    verifyBlocking(messageBus) { publish(revenueEvent) }
    verifyBlocking(androidOfferwallService) { updateCoinsBalance(adjoeReportStub.userId, OfferAction.ADJOE, COINS) }
  }

  @Test
  fun `SHOULD skip emitting revenue and coins but track adjoe report ON onAdjoeCoinsReport WHEN report is invalid`() {
    adjoeCoinsReportValidationService.mock({ isReportValid(adjoeReportStub) }, false)

    runBlocking {
      service.onAdjoeCoinsReport(adjoeReportStub)
    }

    verifyBlocking(adjoePersistenceService) { saveReport(adjoeReportStub) }
    verifyNoInteractions(messageBus, androidOfferwallService, adjoeReportingCalculationsService)
  }

  @Test
  fun `SHOULD silently not emit revenue and coins ON onTapjoyCoinsReport WHEN we already have such coins report tracked`() {
    adjoePersistenceService.throwException({ saveReport(adjoeReportStub) }, constraintViolationException)

    runBlocking {
      service.onAdjoeCoinsReport(adjoeReportStub)
    }

    verifyNoInteractions(messageBus, androidOfferwallService, adjoeReportingCalculationsService)
  }

  @Test
  fun `SHOULD just fail ON onTapjoyCoinsReport WHEN we fail to save coin report but not due to a primary key duplication attempt`() {
    adjoePersistenceService.throwException({ saveReport(adjoeReportStub) }, someOtherExposedSQLException)

    assertFailsWith(ExposedSQLException::class) {
      runBlocking {
        service.onAdjoeCoinsReport(adjoeReportStub)
      }
    }
  }
}