package com.moregames.playtime.revenue.bitlabs

import com.justplayapps.service.rewarding.earnings.proto.AddUserCoinsMessageKt.addOfferWallCoinsMessage
import com.justplayapps.service.rewarding.earnings.proto.addUserCoinsMessage
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.messaging.dto.RevenueReceivedEventDto
import com.moregames.base.util.TimeService
import com.moregames.base.util.toProto
import com.moregames.playtime.revenue.bitlabs.BitlabCalculationService.BitlabCalculationResult
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.UserDto
import com.moregames.playtime.user.UserService
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant

class BitlabsReportingManagerTest {
  private val now = Instant.now()

  private val userService: UserService = mock {
    onBlocking { getUser(USER_ID) } doReturn userDtoStub.copy(appPlatform = AppPlatform.IOS)
  }
  private val bitlabsPersistenceService: BitlabsPersistenceService = mock {
    onBlocking { saveReport(any()) } doReturn true
  }
  private val timeService: TimeService = mock {
    on { now() } doReturn now
  }
  private val bitlabCalculationService: BitlabCalculationService = mock()
  private val rewardingFacade: RewardingFacade = mock {
    onBlocking { inflatingCoinsMultiplier(USER_ID) } doReturn 100
  }
  private val abTestingService: AbTestingService = mock()
  private val messageBus: MessageBus = mock()

  private val underTest = BitlabsReportingManager(
    userService = userService,
    bitlabsPersistenceService = bitlabsPersistenceService,
    bitlabCalculationService = bitlabCalculationService,
    messageBus = messageBus,
    timeService = timeService,
    abTestingService = abTestingService,
    rewardingFacade = rewardingFacade,
  )

  @Test
  fun `SHOULD skip if user is not IOS`() = runTest {
    whenever(userService.getUser(USER_ID)).doReturn(userDtoStub.copy(appPlatform = AppPlatform.ANDROID))

    val req = BitlabReport.Survey(
      userId = USER_ID,
      transactionId = "1",
      rewardValue = 2,
      rewardPaid = 3.toBigDecimal(),
      state = BitlabReport.Survey.SurveyState.COMPLETE
    )
    underTest.processReport(req)

    verifyBlocking(bitlabsPersistenceService) { saveReport(req) }
    verifyBlocking(bitlabCalculationService, never()) { calculateUsdAndCoins(any()) }
  }

  @Test
  fun `SHOULD skip if duplicate`() = runTest {
    whenever(bitlabsPersistenceService.saveReport(any())).doReturn(false)

    val req = BitlabReport.Survey(
      userId = USER_ID,
      transactionId = "1",
      rewardValue = 2,
      rewardPaid = 3.toBigDecimal(),
      state = BitlabReport.Survey.SurveyState.COMPLETE
    )
    underTest.processReport(req)

    verifyBlocking(bitlabsPersistenceService) { saveReport(req) }
    verifyBlocking(bitlabCalculationService, never()) { calculateUsdAndCoins(any()) }
  }

  @Test
  fun `SHOULD do nothing IF no revenue generated`() = runTest {
    val req = BitlabReport.Survey(
      userId = USER_ID,
      transactionId = "1",
      rewardValue = 2,
      rewardPaid = 3.toBigDecimal(),
      state = BitlabReport.Survey.SurveyState.COMPLETE
    )
    whenever(bitlabCalculationService.calculateUsdAndCoins(req)).doReturn(BitlabCalculationResult.NoRevenue)
    underTest.processReport(req)

    verifyBlocking(bitlabsPersistenceService) { saveReport(req) }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD do nothing IF reconciliation`() = runTest {
    val req = BitlabReport.Survey(
      userId = USER_ID,
      transactionId = "1",
      rewardValue = 2,
      rewardPaid = 3.toBigDecimal(),
      state = BitlabReport.Survey.SurveyState.COMPLETE
    )
    whenever(bitlabCalculationService.calculateUsdAndCoins(req)).doReturn(BitlabCalculationResult.RevenueReconciled(BigDecimal.ZERO))
    underTest.processReport(req)

    verifyBlocking(bitlabsPersistenceService) { saveReport(req) }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD process revenue on EM1`() = runTest {
    val req = BitlabReport.Survey(
      userId = USER_ID,
      transactionId = "1",
      rewardValue = 2,
      rewardPaid = 3.toBigDecimal(),
      state = BitlabReport.Survey.SurveyState.COMPLETE
    )
    whenever(bitlabCalculationService.calculateUsdAndCoins(req)).doReturn(BitlabCalculationResult.RevenueReceived(3.toBigDecimal(), 2))
    whenever(abTestingService.isEm2Participant(USER_ID)).doReturn(false)
    val user = mock<UserDto> {
      on { id } doReturn USER_ID
      on { appPlatform } doReturn AppPlatform.IOS
    }
    whenever(userService.getUser(USER_ID)).doReturn(user)

    underTest.processReport(req)

    verifyBlocking(messageBus) {
      publish(
        RevenueReceivedEventDto(
          eventId = "1",
          userId = USER_ID,
          timestamp = now,
          source = RevenueReceivedEventDto.RevenueSource.BITLABS,
          amount = 3.toBigDecimal(),
          createdAt = now,
          networkId = -1,
          gameId = null,
        )
      )
    }
    verifyBlocking(messageBus) {
      publish(
        addUserCoinsMessage {
          this.userId = userId
          this.platform = user.appPlatform.toProto()
          this.offerWallCoins = addOfferWallCoinsMessage {
            this.coinsEarned = 200.toBigDecimal().toProto()
            this.nonInflatedCoinsEarned = 2.toProto()
          }
        }
      )
    }
  }

  @Test
  fun `SHOULD process revenue on EM2`() = runTest {
    val req = BitlabReport.Survey(
      userId = USER_ID,
      transactionId = "1",
      rewardValue = 2,
      rewardPaid = 3.toBigDecimal(),
      state = BitlabReport.Survey.SurveyState.COMPLETE
    )
    whenever(bitlabCalculationService.calculateUsdAndCoins(req)).doReturn(BitlabCalculationResult.RevenueReceived(3.toBigDecimal(), 2))
    whenever(abTestingService.isEm2Participant(USER_ID)).doReturn(true)
    val user = mock<UserDto> {
      on { id } doReturn USER_ID
      on { appPlatform } doReturn AppPlatform.IOS
    }
    whenever(userService.getUser(USER_ID)).doReturn(user)

    underTest.processReport(req)

    verifyBlocking(messageBus) {
      publish(
        RevenueReceivedEventDto(
          eventId = "1",
          userId = USER_ID,
          timestamp = now,
          source = RevenueReceivedEventDto.RevenueSource.BITLABS,
          amount = 3.toBigDecimal(),
          createdAt = now,
          networkId = -1,
          gameId = null,
        )
      )
    }
    verifyBlocking(messageBus) {
      publish(
        addUserCoinsMessage {
          this.userId = userId
          this.platform = user.appPlatform.toProto()
          this.offerWallCoins = addOfferWallCoinsMessage {
            this.coinsEarned = 2.toBigDecimal().toProto()
          }
        }
      )
    }
  }

  private companion object {
    const val USER_ID = "userId"
  }
}