package com.moregames.playtime.revenue.tapjoy

import assertk.assertThat
import assertk.assertions.isFalse
import assertk.assertions.isTrue
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.OfferWallType
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.exceptions.UserRecordNotFoundException
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.secret.SecretService
import com.moregames.base.util.mock
import com.moregames.playtime.app.PlaytimeSecrets.TAPJOY_VIRTUAL_CURRENCY_SECRET_KEY
import com.moregames.playtime.app.tapjoyCurrencySaleMultiplier
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.offer.AndroidOfferwallService
import com.moregames.playtime.user.offer.AndroidOfferwallTypeService
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.math.BigDecimal

class TapjoyCoinsReportValidationServiceTest {
  private val userService: UserService = mock()
  private val androidOfferwallService: AndroidOfferwallService = mock()
  private val secretService: SecretService = mock()
  private val builderVariant = BuildVariant.PRODUCTION
  private val androidOfferwallTypeService: AndroidOfferwallTypeService = mock()
  private val featureFlagsFacade: FeatureFlagsFacade = mock()

  private val service = TapjoyCoinsReportValidationService(
    userService = userService,
    androidOfferwallService = androidOfferwallService,
    secretService = secretService,
    buildVariant = builderVariant,
    androidOfferwallTypeService = androidOfferwallTypeService,
    featureFlagsFacade = featureFlagsFacade
  )

  companion object {
    private const val USER_ID = "userID"
    private const val COINS = 100
    private val coinsReport = TapjoyCoinsReportDto(
      id = "someId",
      userId = USER_ID,
      coins = COINS,
      signature = "6ef66ed3e72c43a54942efb041cf7131",
      macAddress = "someMacAddress",
      revenue = BigDecimal("2"),
      currencySale = BigDecimal.ONE
    )
  }

  @BeforeEach
  fun init() {
    userService.mock({ getUser(USER_ID) }, userDtoStub)
    androidOfferwallService.mock({ shouldShowOfferwalls(USER_ID) }, true)
    secretService.mock({ secretValue(TAPJOY_VIRTUAL_CURRENCY_SECRET_KEY) }, "someKeyHere")
    androidOfferwallTypeService.mock({ getOfferwallTypes(USER_ID) }, listOf(OfferWallType.TAPJOY))
    featureFlagsFacade.mock({ tapjoyCurrencySaleMultiplier() }, 1.5)
  }

  @Test
  fun `SHOULD return true ON isReportValid WHEN the tapjoy coins report is valid`() {
    runBlocking {
      service.isReportValid(coinsReport)
    }.let { assertThat(it).isTrue() }
  }

  @Test
  fun `SHOULD return true ON isReportValid WHEN it is a wrong signature but test environment`() {
    runBlocking {
      TapjoyCoinsReportValidationService(
        userService,
        androidOfferwallService,
        secretService,
        BuildVariant.TEST,
        androidOfferwallTypeService,
        featureFlagsFacade
      )
        .isReportValid(coinsReport.copy(coins = 101))//more coins
    }.let { assertThat(it).isTrue() }
  }

  @Test
  fun `SHOULD return false ON isReportValid WHEN it is a wrong signature`() {
    runBlocking {
      service.isReportValid(coinsReport.copy(coins = 101))//more coins
    }.let { assertThat(it).isFalse() }
  }

  @Test
  fun `SHOULD return false ON isReportValid WHEN we have no such user`() {
    whenever(runBlocking { userService.getUser(USER_ID) }).thenThrow(UserRecordNotFoundException(USER_ID))
    runBlocking {
      service.isReportValid(coinsReport)
    }.let { assertThat(it).isFalse() }
  }

  @Test
  fun `SHOULD return false ON isReportValid WHEN user's platform is not ANDROID`() {
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(appPlatform = IOS))
    runBlocking {
      service.isReportValid(coinsReport)
    }.let { assertThat(it).isFalse() }
  }

  @Test
  fun `SHOULD return false ON isReportValid WHEN user was banned and lost access to offerwall`() {
    androidOfferwallService.mock({ shouldShowOfferwalls(USER_ID) }, false)
    runBlocking {
      service.isReportValid(coinsReport)
    }.let { assertThat(it).isFalse() }
  }

  @Test
  fun `SHOULD return false ON isReportValid WHEN revenue is zero`() {
    runBlocking {
      service.isReportValid(coinsReport.copy(revenue = BigDecimal.ZERO))
    }.let { assertThat(it).isFalse() }
  }

  @Test
  fun `SHOULD alert us ON isReportValid WHEN currencySale param bigger than allowed value`() {
    runBlocking {
      service.isReportValid(coinsReport.copy(currencySale = BigDecimal("1.51")))
    }.let { assertThat(it).isTrue() }
  }

  @Test
  fun `SHOULD not alert us ON isReportValid WHEN currencySale param withing allowed value`() {
    runBlocking {
      service.isReportValid(coinsReport.copy(currencySale = BigDecimal("1.50")))
    }.let { assertThat(it).isTrue() }
  }
}