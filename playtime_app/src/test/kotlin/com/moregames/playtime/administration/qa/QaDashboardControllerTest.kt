package com.moregames.playtime.administration.qa

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.abtesting.AbTestingPersistenceService
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.Variations
import com.moregames.base.abtesting.dto.Experiment
import com.moregames.base.abtesting.dto.Variation
import com.moregames.base.app.BuildVariant
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.encryption.EncryptionService
import com.moregames.base.encryption.HashService
import com.moregames.base.messaging.dto.RevenueReceivedEventDto.RevenueSource
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.administration.blacklist.BlacklistController
import com.moregames.playtime.administration.dto.AssignedVariationsResponse
import com.moregames.playtime.administration.dto.ExperimentVariationsDto
import com.moregames.playtime.administration.qa.QaDashboardController.*
import com.moregames.playtime.administration.user.UserAdministrationController.ExperimentFinishDto
import com.moregames.playtime.administration.user.UserAdministrationController.ExperimentStartDto
import com.moregames.playtime.app.IMAGES_ROOT
import com.moregames.playtime.games.GamesFallbackService
import com.moregames.playtime.games.fallback.dto.FallbackMessageTextDto
import com.moregames.playtime.games.fallback.dto.android.AndroidFallbackApiDto
import com.moregames.playtime.games.fallback.dto.ios.IosFallbackApiDto
import com.moregames.playtime.ios.dto.IosFallbackGameApiDto
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.verification.SeonClient
import com.moregames.playtime.user.verification.dto.DataDto
import com.moregames.playtime.user.verification.dto.EmailCheckResponseDto
import com.moregames.playtime.util.authenticate
import com.moregames.playtime.util.installDefaultContentNegotiation
import com.moregames.playtime.utils.AuthProviderMock
import com.moregames.playtime.utils.Json.defaultJsonConverter
import com.moregames.playtime.utils.androidApiGameListStub
import com.moregames.playtime.utils.customNotificationStub
import com.papsign.ktor.openapigen.OpenAPIGen
import com.papsign.ktor.openapigen.route.apiRouting
import io.ktor.application.*
import io.ktor.client.*
import io.ktor.client.engine.mock.*
import io.ktor.client.features.json.*
import io.ktor.client.features.json.serializer.*
import io.ktor.http.*
import io.ktor.server.testing.*
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoMoreInteractions
import org.mockito.kotlin.whenever
import java.math.BigDecimal
import java.time.Instant

@ExperimentalSerializationApi
class QaDashboardControllerTest {
  private val abTestingService: AbTestingService = mock()
  private val abTestingPersistenceService: AbTestingPersistenceService = mock()
  private val httpClient = mockHttpClient()
  private val userService: UserService = mock()
  private val qaDashboardService: QaDashboardService = mock()
  private val seonClient: SeonClient = mock()
  private val gamesFallbackService: GamesFallbackService = mock()
  private val timeService: TimeService = mock()
  private val encryptionService: EncryptionService = mock()
  private val hashService: HashService = mock()
  private val applicationConfig: ApplicationConfig = mock()

  private val now = Instant.now()

  private fun controller(): Application.() -> Unit = {
    install(OpenAPIGen)
    installDefaultContentNegotiation()
    apiRouting {
      authenticate(AuthProviderMock()) {
        QaDashboardController(
          abTestingService = abTestingService,
          abTestingPersistenceService = abTestingPersistenceService,
          buildVariant = BuildVariant.TEST,
          crossServiceHttpClient = httpClient,
          qaDashboardService = qaDashboardService,
          seonClient = seonClient,
          gamesFallbackService = gamesFallbackService,
          timeService = timeService,
          playIntegrityClientProvider = mock(),
          encryptionService = encryptionService,
          hashService = hashService,
          externalIpService = mock(),
          applicationConfig = applicationConfig,
        ).startRouting(this)
      }
    }
  }

  private companion object {
    const val USER_ID = "userId"
    const val TRACKING_ID = "trackingId"
    const val EMAIL = "<EMAIL>"
    val testRequest = AddCoinsRequestDto("com.relaxingbraintraining.numbermerge", 1, 593, true)
    val userOrTrackingIdParamUserId = UserOrTrackingIdParam(userId = USER_ID, trackingId = null)
    val userOrTrackingIdParamNulls = UserOrTrackingIdParam(userId = null, trackingId = null)
    val userOrTrackingIdParamTrackingId = UserOrTrackingIdParam(userId = null, trackingId = TRACKING_ID)
    val userOrTrackingIdParamBoth = UserOrTrackingIdParam(userId = USER_ID, trackingId = TRACKING_ID)
  }

  @BeforeEach
  fun before() {
    userService.mock({ fetchUserId(TRACKING_ID) }, USER_ID)
    qaDashboardService.mock({ resolveUserId(userOrTrackingIdParamBoth) }, USER_ID)
    qaDashboardService.mock({ resolveUserId(userOrTrackingIdParamTrackingId) }, USER_ID)
    qaDashboardService.mock({ resolveUserId(userOrTrackingIdParamUserId) }, USER_ID)
    qaDashboardService.mock({ resolveUserId(userOrTrackingIdParamNulls) }, null)
  }

  @Test
  fun `SHOULD trigger user whitelisting ON whitelist-user POST call`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/whitelist-user?trackingId=$TRACKING_ID") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(WhitelistUserRequestDto(false)))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamTrackingId) }
    verifyBlocking(qaDashboardService) { whitelistUser(USER_ID, false) }
  }

  @Test
  fun `SHOULD not trigger user whitelisting ON whitelist-user POST call WHEN bad request`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/whitelist-user") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(WhitelistUserRequestDto(false)))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
    verifyBlocking(qaDashboardService) { resolveUserId(UserOrTrackingIdParam(userId = null, trackingId = null)) }
    verifyNoMoreInteractions(qaDashboardService)
  }

  @Test
  fun `SHOULD trigger tracking id whitelisting ON whitelist-trackingId POST call WHEN android user`() = withTestApplication(controller()) {

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/whitelist-trackingId?trackingId=$TRACKING_ID")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { whitelistTrackingData(TRACKING_ID) }
  }

  @Test
  fun `SHOULD trigger tracking id unWhitelisting ON remove-trackingId-from-whitelist POST call `() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/remove-trackingId-from-whitelist?trackingId=$TRACKING_ID")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { removeTrackingIdFromWhitelist(TRACKING_ID) }
  }

  @Test
  fun `SHOULD trigger tracking id blacklisting ON blacklist-trackingId POST call`() = withTestApplication(controller()) {
    qaDashboardService.mock({ blacklistTrackingId(TRACKING_ID, "some reason") }, setOf("userId1", "userId2", "userId3", "userId4"))

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/blacklist-trackingId?trackingId=$TRACKING_ID") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(BlacklistTrackingIdRequestDto("some reason")))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(defaultJsonConverter.decodeFromString<BlacklistController.AffectedUserIdsResponseDto>(testCall.response.content!!))
      .isEqualTo(BlacklistController.AffectedUserIdsResponseDto(setOf("userId1", "userId2", "userId3", "userId4")))
  }

  @Test
  fun `SHOULD trigger user creation timestamp change ON change-creation-timestamp POST call`() = withTestApplication(controller()) {
    val newCreationTimestamp = Instant.now()

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/change-creation-timestamp?trackingId=$TRACKING_ID") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(ChangeUserCreationTimestampRequestDto(newCreationTimestamp)))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamTrackingId) }
    verifyBlocking(qaDashboardService) { changeUserCreationTimestamp(USER_ID, newCreationTimestamp) }
  }

  @Test
  fun `SHOULD not trigger user creation timestamp change ON change-creation-timestamp POST call WHEN id's are not provided`() =
    withTestApplication(controller()) {
      val newCreationTimestamp = Instant.now()

      val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/change-creation-timestamp") {
        addHeader("Content-Type", "application/json")
        setBody(defaultJsonConverter.encodeToString(ChangeUserCreationTimestampRequestDto(newCreationTimestamp)))
      }

      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
      verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
      verifyNoMoreInteractions(qaDashboardService)
    }

  @Test
  fun `SHOULD trigger user locale change ON change-user-locale POST call`() = withTestApplication(controller()) {
    val newCountryCode = "de-DE"

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/change-user-locale?trackingId=$TRACKING_ID") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(ChangeUserLocaleRequestDto(newCountryCode)))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamTrackingId) }
    verifyBlocking(qaDashboardService) { changeUserLocale(USER_ID, newCountryCode) }
  }

  @Test
  fun `SHOULD not trigger user locale change ON change-user-locale POST call WHEN id's are not provided`() = withTestApplication(controller()) {
    val newCountryCode = "de-DE"

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/change-user-locale") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(ChangeUserLocaleRequestDto(newCountryCode)))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
    verifyNoMoreInteractions(qaDashboardService)
  }

  @Test
  fun `SHOULD trigger user country code change ON change-country-code POST call`() = withTestApplication(controller()) {
    val newCountryCode = "CA"

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/change-country-code?trackingId=$TRACKING_ID") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(ChangeUserCountryCodeRequestDto(newCountryCode)))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamTrackingId) }
    verifyBlocking(qaDashboardService) { changeUserCountryCode(USER_ID, newCountryCode) }
  }

  @Test
  fun `SHOULD not trigger user country code change ON change-country-code POST call WHEN id's are not provided`() = withTestApplication(controller()) {
    val newCountryCode = "CA"

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/change-country-code") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(ChangeUserCountryCodeRequestDto(newCountryCode)))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
    verifyNoMoreInteractions(qaDashboardService)
  }

  @Test
  fun `SHOULD add generic revenue and emit related event ON add-generic-revenue POST call`() = withTestApplication(controller()) {
    val revenueAmount = BigDecimal("5.43")
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/add-generic-revenue?trackingId=$TRACKING_ID") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(AddGenericRevenueRequestDto(RevenueSource.APPLOVIN, revenueAmount, 200039)))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamTrackingId) }
    verifyBlocking(qaDashboardService) { addGenericRevenue("userId", RevenueSource.APPLOVIN, BigDecimal("5.43"), 200039) }
  }

  @Test
  fun `SHOULD not add generic revenue and emit related event ON add-generic-revenue POST call WHEN id's are not provided`() =
    withTestApplication(controller()) {
      val revenueAmount = BigDecimal("5.43")
      val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/add-generic-revenue") {
        addHeader("Content-Type", "application/json")
        setBody(defaultJsonConverter.encodeToString(AddGenericRevenueRequestDto(RevenueSource.APPLOVIN, revenueAmount)))
      }

      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
      verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
      verifyNoMoreInteractions(qaDashboardService)
    }

  @Test
  fun `SHOULD add earnings and allow cashout ON add-earnings POST call for both id's`() = withTestApplication(controller()) {
    val testAmount = BigDecimal("5.43")
    val request = AddEarningsRequestDto(amountUSD = testAmount)

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/add-earnings?userId=${USER_ID}&trackingId=$TRACKING_ID") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(request))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamBoth) }
    verifyBlocking(qaDashboardService) { addEarnings(USER_ID, request) }
  }

  @Test
  fun `SHOULD not add earnings and allow cashout ON add-earnings POST call WHEN id's are not provided`() = withTestApplication(controller()) {
    val testAmount = BigDecimal("5.43")
    val request = AddEarningsRequestDto(amountUSD = testAmount)

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/add-earnings") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(request))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
    verifyNoMoreInteractions(qaDashboardService)
  }

  @Test
  fun `SHOULD initiate generic notification ON send-generic-notification POST call`() = withTestApplication(controller()) {
    val notificationText = "notificationText"

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/send-generic-notification?trackingId=$TRACKING_ID") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(NotificationTextRequestDto(notificationText)))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamTrackingId) }
    verifyBlocking(qaDashboardService) { sendGenericNotification(USER_ID, notificationText = notificationText) }
  }

  @Test
  fun `SHOULD not initiate generic notification ON send-generic-notification POST call WHEN id's are not provided`() = withTestApplication(controller()) {
    val notificationText = "notificationText"

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/send-generic-notification") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(NotificationTextRequestDto(notificationText)))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
    verifyNoMoreInteractions(qaDashboardService)
  }

  @Test
  fun `SHOULD fallback to default notificationType ON QaFreeFormCustomNotification WHEN notificationType is null`() {
    val customNotification = QaFreeFormCustomNotificationApiDto(data = mapOf("key1" to "value1", "key2" to "value2"))
    assertThat(customNotification.getMessageType()).isEqualTo("customNotification")
  }

  @Test
  fun `SHOULD respect notificationType ON QaFreeFormCustomNotification`() {
    val customNotification = QaFreeFormCustomNotificationApiDto(
      notificationType = "someNotificationType",
      data = mapOf("key1" to "value1", "key2" to "value2"),
    )
    assertThat(customNotification.getMessageType()).isEqualTo("someNotificationType")
  }

  @Test
  fun `SHOULD initiate custom notification ON send-free-form-custom-notification POST call`() = withTestApplication(controller()) {
    val request = QaFreeFormCustomNotificationApiDto(data = mapOf("key1" to "value1", "key2" to "value2"))

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/send-free-form-custom-notification?trackingId=$TRACKING_ID") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(request))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { sendQaFreeFormCustomNotification(USER_ID, request) }
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamTrackingId) }
  }

  @Test
  fun `SHOULD not initiate custom notification ON send-free-form-custom-notification POST call WHEN id's are not provided`() =
    withTestApplication(controller()) {
      val request = QaFreeFormCustomNotificationApiDto(data = mapOf("key1" to "value1", "key2" to "value2"))

      val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/send-free-form-custom-notification") {
        addHeader("Content-Type", "application/json")
        setBody(defaultJsonConverter.encodeToString(request))
      }

      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
      verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
      verifyNoMoreInteractions(qaDashboardService)
    }

  @Test
  fun `SHOULD initiate custom notification ON send-custom-notification POST call`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall =
      handleRequest(method = HttpMethod.Post, uri = "/qa/users/send-custom-notification?userId=${USER_ID}&trackingId=$TRACKING_ID") {
        addHeader("Content-Type", "application/json")
        setBody(defaultJsonConverter.encodeToString(customNotificationStub))
      }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamBoth) }
    verifyBlocking(qaDashboardService) { sendCustomNotifications(USER_ID, customNotificationStub) }
  }

  @Test
  fun `SHOULD not initiate custom notification ON send-custom-notification POST call WHEN id's are not provided`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/send-custom-notification") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(customNotificationStub))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
    verifyNoMoreInteractions(qaDashboardService)
  }

  @Test
  fun `SHOULD initiate email notification ON send-email-notification POST call`() = withTestApplication(controller()) {
    val testRequest = QaEmailNotificationRequest(name = "name", email = "email", sendGridTemplateId = "id1")
    encryptionService.mock({ encrypt("name") }, "encryptedName")
    encryptionService.mock({ encrypt("email") }, "encryptedEmail")

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/send-email-notification?trackingId=$TRACKING_ID") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(testRequest))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamTrackingId) }
    verifyBlocking(qaDashboardService) { sendEmailNotification(USER_ID, testRequest) }
  }

  @Test
  fun `SHOULD noto initiate email notification ON send-email-notification POST call WHEN id's are not provided`() = withTestApplication(controller()) {
    val testRequest = QaEmailNotificationRequest(name = "name", email = "email", sendGridTemplateId = "id1")
    encryptionService.mock({ encrypt("name") }, "encryptedName")
    encryptionService.mock({ encrypt("email") }, "encryptedEmail")

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/send-email-notification") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(testRequest))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
    verifyNoMoreInteractions(qaDashboardService)
  }

  @Test
  fun `SHOULD trigger request to orchestrator ON add-coins POST call`() = withTestApplication(controller()) {

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/add-coins?trackingId=$TRACKING_ID") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(testRequest))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamTrackingId) }
    verifyBlocking(qaDashboardService) { addCoins(USER_ID, testRequest) }
  }

  @Test
  fun `SHOULD not trigger request to orchestrator ON add-coins POST call WHEN id's are not provided`() = withTestApplication(controller()) {

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/add-coins") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(testRequest))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
    verifyNoMoreInteractions(qaDashboardService)
  }

  @Test
  fun `SHOULD return experiment variations split ON variations GET call`() = withTestApplication(controller()) {
    val experimentKey = "experimentKey"
    val experiment = Experiment(
      1, experimentKey, isActive = false,
      minimumAppVersion = 42,
      startedAt = Instant.now().minusSeconds(1000),
      finishedAt = Instant.now().minusSeconds(900),
      gameVersion = null
    )
    val variation1 = Variation(1, "var1", BigDecimal("0.40"), experiment)
    val variation2 = Variation(2, "var2", BigDecimal("0.60"), experiment)
    abTestingService.mock({ loadVariationsForExperiment(experimentKey) }, listOf(variation1, variation2))
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/qa/experiments/$experimentKey/variations")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(defaultJsonConverter.decodeFromString<ExperimentVariationsDto>(testCall.response.content!!))
      .isEqualTo(
        ExperimentVariationsDto(
          startedAt = experiment.startedAt,
          finishedAt = experiment.finishedAt,
          minimumAppVersion = 42,
          mapOf(
            variation1.key to variation1.allocation,
            variation2.key to variation2.allocation
          )
        )
      )
  }

  @Test
  fun `SHOULD update experiment variations split ON variations POST call`() = withTestApplication(controller()) {
    val experimentKey = "experimentKey"
    val request = ExperimentVariationsDto(
      startedAt = Instant.now(),
      finishedAt = Instant.now(),
      minimumAppVersion = 9999,
      mapOf(
        "var1" to BigDecimal("0.30"),
        "var2" to BigDecimal("0.70")
      )
    )

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/experiments/$experimentKey/variations") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(request))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(abTestingService) {
      updateVariationsForExperiment(experimentKey, request.variations)
    }
  }

  @Test
  fun `SHOULD remove faces from database for all users related to trackingId ON remove-face call`() = withTestApplication(controller()) {
    qaDashboardService.mock({ fetchAllUserIds(userOrTrackingIdParamTrackingId) }, listOf("userId1", "userId2", "userId3"))
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/qa/users/remove-face?trackingId=$TRACKING_ID")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { fetchAllUserIds(userOrTrackingIdParamTrackingId) }
    verifyBlocking(qaDashboardService) { removeFaceByUserId("userId1") }
    verifyBlocking(qaDashboardService) { removeFaceByUserId("userId2") }
    verifyBlocking(qaDashboardService) { removeFaceByUserId("userId3") }
  }

  @Test
  fun `SHOULD remove faces from database for user related to userId ON remove-face call`() = withTestApplication(controller()) {
    qaDashboardService.mock({ fetchAllUserIds(userOrTrackingIdParamUserId) }, listOf(USER_ID))
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/qa/users/remove-face?userId=$USER_ID")

    verifyBlocking(qaDashboardService) { fetchAllUserIds(userOrTrackingIdParamUserId) }
    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { removeFaceByUserId(USER_ID) }
    verifyNoMoreInteractions(qaDashboardService)
  }

  @Test
  fun `SHOULD do nothing for user WHEN userId AND trackingId are null ON remove-face call`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/qa/users/remove-face")

    verifyBlocking(qaDashboardService) { fetchAllUserIds(userOrTrackingIdParamNulls) }
    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyNoMoreInteractions(qaDashboardService)
  }

  @Test
  fun `SHOULD remove cache for the user by userId ON drop-user-cache call`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/qa/users/drop-user-cache?userId=$USER_ID")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamUserId) }
    verifyBlocking(qaDashboardService) { dropUserCache(userId = USER_ID) }
  }

  @Test
  fun `SHOULD not remove cache FOR empty userId and trackingId ON drop-user-cache call`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/qa/users/drop-user-cache")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
    verifyNoMoreInteractions(qaDashboardService)
  }

  @Test
  fun `SHOULD trigger cashout period end message processing ON cashout-period-end-by-user-id call`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/qa/users/cashout-period-end?userId=$USER_ID")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamUserId) }
    verifyBlocking(qaDashboardService) { endCashoutPeriod(USER_ID) }
  }

  @Test
  fun `SHOULD not trigger cashout period end message processing ON cashout-period-end-by-user-id call WHEN id's are not provided`() =
    withTestApplication(controller()) {
      val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/qa/users/cashout-period-end")

      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
      verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
      verifyNoMoreInteractions(qaDashboardService)
    }

  @Test
  fun `SHOULD trigger onTapjoyCoinsReport ON add-offerwall-offer-completion-coins`() = withTestApplication(controller()) {
    val request = QaTapjoyCoinsReport(
      10500,
      BigDecimal(1.5),
      BigDecimal(1.5)
    )
    val testCall: TestApplicationCall =
      handleRequest(method = HttpMethod.Post, uri = "/qa/users/add-offerwall-offer-completion-coins?trackingId=$TRACKING_ID") {
        addHeader("Content-Type", "application/json")
        setBody(
          defaultJsonConverter.encodeToString(request)
        )
      }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamTrackingId) }
    verifyBlocking(qaDashboardService) { addOfferwallOfferCompletionCoins(userId = USER_ID, request = request) }
  }

  @Test
  fun `SHOULD not trigger onTapjoyCoinsReport ON add-offerwall-offer-completion-coins WHEN id's are not provided`() = withTestApplication(controller()) {
    val request = QaTapjoyCoinsReport(
      10500,
      BigDecimal(1.5),
      BigDecimal(1.5)
    )
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/add-offerwall-offer-completion-coins") {
      addHeader("Content-Type", "application/json")
      setBody(
        defaultJsonConverter.encodeToString(request)
      )
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
    verifyNoMoreInteractions(qaDashboardService)
  }

  @Test
  fun `SHOULD trigger onIronSourceCoinsReport ON add-offerwall-offer-completion-coins`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall =
      handleRequest(method = HttpMethod.Post, uri = "/qa/users/add-fyber-offerwall-offer-completion-coins?trackingId=$TRACKING_ID") {
        addHeader("Content-Type", "application/json")
        setBody(
          defaultJsonConverter.encodeToString(
            QaTapjoyCoinsReport(
              10500,
              BigDecimal(1.5),
              BigDecimal(1.5)
            )
          )
        )
      }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamTrackingId) }
    verifyBlocking(qaDashboardService) {
      addFyberOfferwallOfferCompletionCoins(
        USER_ID,
        QaTapjoyCoinsReport(
          10500,
          BigDecimal(1.5),
          BigDecimal(1.5)
        )
      )
    }
  }

  @Test
  fun `SHOULD not trigger onIronSourceCoinsReport ON add-offerwall-offer-completion-coins WHEN id's are not provided`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/add-fyber-offerwall-offer-completion-coins") {
      addHeader("Content-Type", "application/json")
      setBody(
        defaultJsonConverter.encodeToString(
          QaTapjoyCoinsReport(
            10500,
            BigDecimal(1.5),
            BigDecimal(1.5)
          )
        )
      )
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
    verifyNoMoreInteractions(qaDashboardService)
  }

  @Test
  fun `SHOULD insert fraud score transaction ON fraudScore`() = withTestApplication(controller()) {
    val targetFraudScore = 99

    val testCall = handleRequest(method = HttpMethod.Patch, uri = "/qa/users/fraudScore?trackingId=$TRACKING_ID") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(QaSetFraudScoreTotal(targetFraudScore)))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)

    verifyBlocking(qaDashboardService) {
      resolveUserId(userOrTrackingIdParamTrackingId)
    }
    verifyBlocking(qaDashboardService) {
      setFraudScore(USER_ID, targetFraudScore)
    }
  }

  @Test
  fun `SHOULD not insert fraud score transaction ON fraudScore WHEN id's are not provided`() = withTestApplication(controller()) {
    val targetFraudScore = 99

    val testCall = handleRequest(method = HttpMethod.Patch, uri = "/qa/users/fraudScore") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(QaSetFraudScoreTotal(targetFraudScore)))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)

    verifyBlocking(qaDashboardService) {
      resolveUserId(userOrTrackingIdParamNulls)
    }
    verifyNoMoreInteractions(qaDashboardService)
  }


  @Test
  fun `SHOULD auto-verify user ON verify-face call`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/verify-face?trackingId=$TRACKING_ID")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamTrackingId) }
    verifyBlocking(qaDashboardService) { verifyFace(USER_ID) }
  }

  @Test
  fun `SHOULD not auto-verify user ON verify-face call`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/verify-face")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
    verifyNoMoreInteractions(qaDashboardService)
  }

  @Test
  fun `SHOULD auto-verify user ON verify-ios-specific-steps call`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/verify-face?trackingId=$TRACKING_ID")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamTrackingId) }
    verifyBlocking(qaDashboardService) { verifyFace(USER_ID) }
  }

  @Test
  fun `SHOULD not auto-verify user ON verify-ios-specific-steps call`() = withTestApplication(controller()) {
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/verify-ios-specific-steps")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
    verifyNoMoreInteractions(qaDashboardService)
  }

  @Test
  fun `SHOULD return all active experiments ON experiments-active GET call`() = withTestApplication(controller()) {
    val experiment1 = Experiment(
      id = 1,
      key = ClientExperiment.ANDROID_CASHOUT_SURVEY.key,
      isActive = true,
      minimumAppVersion = 100,
      startedAt = Instant.now().minusSeconds(1000),
      finishedAt = null,
      gameVersion = null,
    )
    val experiment2 = Experiment(
      id = 2,
      key = ClientExperiment.EARNINGS_MODEL_V2.key,
      isActive = true,
      minimumAppVersion = 100,
      startedAt = Instant.now().minusSeconds(1000),
      finishedAt = null,
      gameVersion = null,
    )

    abTestingPersistenceService.mock({ loadNotFinishedExperiments() }, listOf(experiment1, experiment2))
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/qa/experiments") {
      addHeader("Content-Type", "application/json")
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(defaultJsonConverter.decodeFromString<List<ExperimentDto>>(testCall.response.content!!))
      .isEqualTo(listOf(ExperimentDto.from(experiment2), ExperimentDto.from(experiment1)))
  }

  @Test
  fun `SHOULD return empty list ON experiments-active GET call WHEN there are no experiments`() =
    withTestApplication(controller()) {

      abTestingPersistenceService.mock({ loadNotFinishedExperiments() }, listOf())
      val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/qa/experiments") {
        addHeader("Content-Type", "application/json")
      }

      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
      assertThat(defaultJsonConverter.decodeFromString<List<ExperimentDto>>(testCall.response.content!!))
        .isEqualTo(listOf())
    }

  @Test
  fun `SHOULD set frozen fraud score for user ON frozenFraudScore call`() =
    withTestApplication(controller()) {
      val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Patch, uri = "/qa/users/frozenFraudScore?trackingId=$TRACKING_ID") {
        addHeader("Content-Type", "application/json")
        setBody(defaultJsonConverter.encodeToString(QaSetFraudScoreTotal(100)))
      }

      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
      verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamTrackingId) }
      verifyBlocking(qaDashboardService) { setFrozenFraudScore(USER_ID, 100) }
    }

  @Test
  fun `SHOULD not set frozen fraud score for user ON frozenFraudScore call WHEN id's are not provided`() =
    withTestApplication(controller()) {
      val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Patch, uri = "/qa/users/frozenFraudScore") {
        addHeader("Content-Type", "application/json")
        setBody(defaultJsonConverter.encodeToString(QaSetFraudScoreTotal(100)))
      }

      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
      verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
      verifyNoMoreInteractions(qaDashboardService)
    }

  @Test
  fun `SHOULD add user to passed strong attestation users list ON pass-strong-attestation call`() =
    withTestApplication(controller()) {
      val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/pass-strong-attestation?trackingId=$TRACKING_ID")

      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
      verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamTrackingId) }
      verifyBlocking(qaDashboardService) { passStrongAttestation(USER_ID) }
    }

  @Test
  fun `SHOULD not add user to passed strong attestation users list ON pass-strong-attestation call WHEN id's are not provided`() =
    withTestApplication(controller()) {
      val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/pass-strong-attestation")

      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
      verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
      verifyNoMoreInteractions(qaDashboardService)
    }

  @Test
  fun `SHOULD set usual cashout period qa setting ON set-usual-cashout-periods`() =
    withTestApplication(controller()) {
      val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/set-usual-cashout-periods?trackingId=$TRACKING_ID") {
        addHeader("Content-Type", "application/json")
        setBody(defaultJsonConverter.encodeToString(SetUsualCashoutPeriodsRequestDto(false)))
      }

      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
      verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamTrackingId) }
      verifyBlocking(qaDashboardService) { setUsualCashoutPeriods(USER_ID, false) }
    }

  @Test
  fun `SHOULD not set usual cashout period qa setting ON set-usual-cashout-periods WHEN id's are not provided`() =
    withTestApplication(controller()) {
      val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/set-usual-cashout-periods") {
        addHeader("Content-Type", "application/json")
        setBody(defaultJsonConverter.encodeToString(SetUsualCashoutPeriodsRequestDto(false)))
      }

      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
      verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
      verifyNoMoreInteractions(qaDashboardService)
    }

  @Test
  fun `SHOULD call markUserAsPassedDeviceAttestation ON pass-device-attestation call`() =
    withTestApplication(controller()) {
      val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/pass-device-attestation?trackingId=$TRACKING_ID")
      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
      verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamTrackingId) }
      verifyBlocking(qaDashboardService) {
        passDeviceAttestation(USER_ID)
      }
    }

  @Test
  fun `SHOULD call passJailBreakCheck ON pass-jailBreak-check call`() =
    withTestApplication(controller()) {
      val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/pass-jailBreak-check?trackingId=$TRACKING_ID")
      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
      verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamTrackingId) }
      verifyBlocking(qaDashboardService) {
        passJailBreakCheck(USER_ID)
      }
    }

  @Test
  fun `SHOULD not call markUserAsPassedDeviceAttestation ON pass-device-attestation call WHEN id's are not provided`() =
    withTestApplication(controller()) {
      val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/pass-device-attestation")
      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
      verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
      verifyNoMoreInteractions(qaDashboardService)
    }

  @Test
  fun `SHOULD call SEON API on verify-email-seon call`() = withTestApplication(controller()) {
    val seonEmailCheckResponse = EmailCheckResponseDto(
      success = true,
      error = null,
      data =
        DataDto(email = EMAIL, score = 15.0, deliverable = true, domainDetails = null, accountDetails = null, breachDetails = null)
    )
    seonClient.mock({ checkEmail(EMAIL) }, seonEmailCheckResponse)
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/users/verify-email-seon") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(QaEmailCheckRequestDto(EMAIL)))
    }
    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(testCall.response.content).isEqualTo(defaultJsonConverter.encodeToString(seonEmailCheckResponse))
  }

  @Test
  fun `SHOULD return all active ios games ON fallback-ios GET call`() = withTestApplication(controller()) {

    val game1 = IosFallbackGameApiDto(
      id = 1,
      applicationId = "applicationId",
      name = mutableMapOf("default" to "name", "en" to "name", "fr" to "name"),
      description = mutableMapOf("default" to "description", "en" to "description", "fr" to "description"),
      iconFilename = IMAGES_ROOT + "iconFilename",
      imageFilename = IMAGES_ROOT + "imageFilename",
      infoTextInstall = mutableMapOf("default" to "infoTextInstallTop", "en" to "infoTextInstallTop", "fr" to "infoTextInstallTop"),
      iosApplicationId = "id6444407009",
      iosGameUrl = "treasuremaster:LaunchTreasureMaster"
    )

    val game2 = IosFallbackGameApiDto(
      id = 2,
      applicationId = "applicationId",
      name = mutableMapOf("default" to "name", "en" to "name", "fr" to "name"),
      description = mutableMapOf("default" to "description", "en" to "description", "fr" to "description"),
      iconFilename = IMAGES_ROOT + "iconFilename",
      imageFilename = IMAGES_ROOT + "imageFilename",
      infoTextInstall = mutableMapOf("default" to "infoTextInstallTop", "en" to "infoTextInstallTop", "fr" to "infoTextInstallTop"),
      iosApplicationId = "id1667538256",
      iosGameUrl = "solitaireverse:LaunchSolitaireVerse"
    )

    val iosFallback = IosFallbackApiDto(
      market = "au-test",
      playGamesTitle = FallbackMessageTextDto("text1", "text2"),
      statusMessage = FallbackMessageTextDto("text1", "text2"),
      games = listOf(game1, game2)
    )

    gamesFallbackService.mock({ getIosFallback() }, listOf(iosFallback))
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/qa/fallback/ios") {
      addHeader("Content-Type", "application/json")
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(defaultJsonConverter.decodeFromString<Map<String, List<IosFallbackApiDto>>>(testCall.response.content!!))
      .isEqualTo(mapOf("markets" to listOf(iosFallback)))
  }

  @Test
  fun `SHOULD return all active android games ON fallback-android GET call`() = withTestApplication(controller()) {

    val androidFallback = AndroidFallbackApiDto(
      market = "au-test",
      playGamesTitle = FallbackMessageTextDto("text1", "text2"),
      statusMessage = FallbackMessageTextDto("text1", "text2"),
      items = androidApiGameListStub
    )

    gamesFallbackService.mock({ getAndroidFallback() }, listOf(androidFallback))
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/qa/fallback/android") {
      addHeader("Content-Type", "application/json")
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(defaultJsonConverter.decodeFromString<Map<String, List<AndroidFallbackApiDto>>>(testCall.response.content!!))
      .isEqualTo(mapOf("markets" to listOf(androidFallback)))
  }

  @Test
  fun `SHOULD start experiment ON ab_testing experiment start`() = withTestApplication(controller()) {
    val startAt = now.plusSeconds(1000)
    val experimentStartRequest = ExperimentStartDto(startAt = startAt, minimumAppVersion = 42)

    handleRequest(method = HttpMethod.Post, uri = "/qa/experiments/expKey/start") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(experimentStartRequest))
    }.let { testCall ->
      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    }

    verifyBlocking(abTestingService) { startExperiment(experimentKey = "expKey", startAt = startAt, minimumAppVersion = 42) }
  }

  @Test
  fun `SHOULD start experiment now ON ab_testing experiment start WHEN startAt not defined`() = withTestApplication(controller()) {
    whenever(timeService.now()).thenReturn(now)
    val experimentStartRequest = ExperimentStartDto(startAt = null, minimumAppVersion = 42)

    handleRequest(method = HttpMethod.Post, uri = "/qa/experiments/expKey/start") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(experimentStartRequest))
    }.let { testCall ->
      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    }

    verifyBlocking(abTestingService) { startExperiment(experimentKey = "expKey", startAt = now, minimumAppVersion = 42) }
  }

  @Test
  fun `SHOULD finish experiment ON ab_testing experiment finish`() = withTestApplication(controller()) {
    val finishAt = now.plusSeconds(1000)
    val experimentFinishRequest = ExperimentFinishDto(finishAt = finishAt)

    handleRequest(method = HttpMethod.Post, uri = "/qa/experiments/expKey/finish") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(experimentFinishRequest))
    }.let { testCall ->
      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    }

    verifyBlocking(abTestingService) { finishExperiment(experimentKey = "expKey", finishAt = finishAt) }
  }

  @Test
  fun `SHOULD finish experiment now ON ab_testing experiment finish WHEN finishAt not passed`() = withTestApplication(controller()) {
    whenever(timeService.now()).thenReturn(now)
    val experimentFinishRequest = ExperimentFinishDto(finishAt = null)

    handleRequest(method = HttpMethod.Post, uri = "/qa/experiments/expKey/finish") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(experimentFinishRequest))
    }.let { testCall ->
      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    }

    verifyBlocking(abTestingService) { finishExperiment(experimentKey = "expKey", finishAt = now) }
  }

  @Test
  fun `SHOULD force HT WHEN method is called for userId`() = withTestApplication(controller()) {
    handleRequest(method = HttpMethod.Post, uri = "/qa/users/force-highly-trusted?userId=$USER_ID") {
    }.let { testCall ->
      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    }

    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamUserId) }
    verifyBlocking(qaDashboardService) { forceHighlyTrusted(USER_ID) }
  }

  @Test
  fun `SHOULD not force HT WHEN method is called without id's`() = withTestApplication(controller()) {
    handleRequest(method = HttpMethod.Post, uri = "/qa/users/force-highly-trusted") {
    }.let { testCall ->
      assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
    }

    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
    verifyNoMoreInteractions(qaDashboardService)
  }

  @Test
  fun `SHOULD return decrypted value`() = withTestApplication(controller()) {
    val encrypted = "encryptedValue"
    val decrypted = "decryptedValue"
    encryptionService.mock({ decryptOrEmpty(encrypted) }, decrypted)

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/qa/$encrypted/decrypt")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(defaultJsonConverter.decodeFromString<Map<String, String>>(testCall.response.content!!))
      .isEqualTo(
        mapOf("Decrypted value:" to decrypted)
      )
  }

  @Test
  fun `SHOULD return email hash`() = withTestApplication(controller()) {
    val email = "email"
    val hash = "hash"
    hashService.mock({ (emailSha256(email)) }, hash)

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/qa/$email/hash")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(defaultJsonConverter.decodeFromString<Map<String, String>>(testCall.response.content!!))
      .isEqualTo(
        mapOf("Email hash:" to hash)
      )
  }

  @Test
  fun `SHOULD return bad request WHEN reassign is called with wrong variation key`() = withTestApplication(controller()) {
    val reassignRequest = ReassignUserRequestApiDto(ClientExperiment.EARNINGS_MODEL_V2.key, Variations.GROUP_A.variationKey)
    qaDashboardService.mock({ reassignVariationAndReturnSuccess(USER_ID, reassignRequest) }, false)

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/experiments/reassign-variation?userId=$USER_ID") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(reassignRequest))
    }

    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamUserId) }
    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
  }

  @Test
  fun `SHOULD return bad request WHEN reassign is called with empty user id`() = withTestApplication(controller()) {
    val reassignRequest = ReassignUserRequestApiDto(ClientExperiment.ENABLE_VENMO.key, Variations.IOS_SIX_GAMES_8H.variationKey)

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/experiments/reassign-variation") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(reassignRequest))
    }

    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.BadRequest)
  }

  @Test
  fun `SHOULD return OK WHEN reassign is called correctly`() = withTestApplication(controller()) {
    val reassignRequest = ReassignUserRequestApiDto(ClientExperiment.ENABLE_VENMO.key, Variations.ENABLE_VENMO.variationKey)
    qaDashboardService.mock({ reassignVariationAndReturnSuccess(USER_ID, reassignRequest) }, true)
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Post, uri = "/qa/experiments/reassign-variation?userId=$USER_ID") {
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(reassignRequest))
    }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamUserId) }
    verifyBlocking(qaDashboardService) { reassignVariationAndReturnSuccess(USER_ID, reassignRequest) }
  }

  @Test
  fun `SHOULD return assigned variations WHEN userId is provided`() = withTestApplication(controller()) {
    abTestingPersistenceService.mock({ loadActiveAssignedVariationsFor(USER_ID) }, emptyList())
    val response = AssignedVariationsResponse(
      userId = USER_ID,
      experiments = emptyList()
    )
    qaDashboardService.mock({ getAssignedVariations(USER_ID) }, response)
    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/qa/experiments/assigned-variations?userId=$USER_ID")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamUserId) }
    verifyBlocking(qaDashboardService) { getAssignedVariations(USER_ID) }
    assertThat(defaultJsonConverter.decodeFromString<AssignedVariationsResponse>(testCall.response.content!!))
      .isEqualTo(response)
  }

  @Test
  fun `SHOULD not return assigned variations WHEN id's are not provided`() = withTestApplication(controller()) {
    val response = AssignedVariationsResponse(
      userId = "unknown userId",
      experiments = emptyList()
    )

    val testCall: TestApplicationCall = handleRequest(method = HttpMethod.Get, uri = "/qa/experiments/assigned-variations")

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(qaDashboardService) { resolveUserId(userOrTrackingIdParamNulls) }
    verifyNoMoreInteractions(qaDashboardService)
    assertThat(defaultJsonConverter.decodeFromString<AssignedVariationsResponse>(testCall.response.content!!))
      .isEqualTo(response)
  }

  private fun mockHttpClient() = HttpClient(MockEngine) {
    install(JsonFeature) {
      val json = kotlinx.serialization.json.Json { ignoreUnknownKeys = true }
      serializer = KotlinxSerializer(json)
    }
    engine {
      addHandler { request ->
        when (request.url.toString()) {
          "https://test-dot-orchestrator-dot-playspot-server-dev.appspot.com/webhooks/games/progress" +
            "?game_id=com.relaxingbraintraining.numbermerge" +
            "&user_id=userId" +
            "&idfv=" +
            "&score=1" +
            "&amount=593" +
            "&is_new_record=true" -> {
            respondOk()
          }

          "https://test-dot-orchestrator-dot-playspot-server-dev.appspot.com/webhooks/games/progress" +
            "?game_id=com.relaxingbraintraining.numbermerge" +
            "&user_id=" +
            "&idfv=iosIdfv" +
            "&score=1" +
            "&amount=593" +
            "&is_new_record=true" -> {
            respondOk()
          }

          else -> error("Unhandled ${request.url.fullPath}")
        }
      }
    }
  }
}
