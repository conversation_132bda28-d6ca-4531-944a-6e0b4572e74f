package com.moregames.playtime.administration.qa

import assertk.assertThat
import assertk.assertions.isEqualTo
import org.junit.jupiter.api.Test

class QaUserSettingTest {

  @Test
  fun `SHOULD find enum element by key ON byKey`() {
    assertThat(QaUserSetting.byKey("speedUpCashoutPeriodEnd")).isEqualTo(QaUserSetting.SPEED_UP_CASHOUT_PERIOD_END)
    assertThat(QaUserSetting.byKey("skipOnboarding")).isEqualTo(QaUserSetting.SKIP_ONBOARDING)
  }
}