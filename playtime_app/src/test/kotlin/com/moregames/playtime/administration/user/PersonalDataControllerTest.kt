package com.moregames.playtime.administration.user

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.util.mock
import com.moregames.playtime.administration.user.PersonalDataController.DeletePersonalDataRequestDto
import com.moregames.playtime.administration.user.PersonalDataController.GetPersonalDataResponseDto
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutService
import com.moregames.playtime.user.verification.VerificationService
import com.moregames.playtime.util.authenticate
import com.moregames.playtime.util.defaultJsonConverter
import com.moregames.playtime.utils.AuthProviderMock
import com.moregames.playtime.utils.userDtoStub
import com.papsign.ktor.openapigen.OpenAPIGen
import com.papsign.ktor.openapigen.route.apiRouting
import io.ktor.application.*
import io.ktor.features.*
import io.ktor.http.*
import io.ktor.serialization.*
import io.ktor.server.testing.*
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Test
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoMoreInteractions
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.test.assertFailsWith

@ExperimentalSerializationApi
class PersonalDataControllerTest {
  private val jsonConverter: Json = defaultJsonConverter
  private val cashoutService: CashoutService = mock()
  private val userService: UserService = mock()
  private val personalDataService: PersonalDataService = mock()

  private fun controller(): Application.() -> Unit = {
    install(OpenAPIGen)
    install(ContentNegotiation) {
      json(json = jsonConverter)
    }
    apiRouting {
      authenticate(AuthProviderMock()) {
        PersonalDataController(
          userService = userService,
          cashoutService = cashoutService,
          personalDataService = personalDataService,
        ).startRouting(this)
      }
    }
  }

  @Test
  fun `SHOULD do nothing ON patch deletePersonalData call WHEN dry run`() = withTestApplication(controller()) {
    val googleAdId = "googleAdId"

    val testCall: TestApplicationCall =
      handleRequest(method = HttpMethod.Patch, uri = "/personalData/delete") {
        addHeader("Content-Type", "application/json")
        setBody(
          jsonConverter.encodeToString(
            DeletePersonalDataRequestDto(
              dryRun = true,
              googleAdId = googleAdId,
              email = ""
            )
          )
        )
      }

    verifyNoInteractions(personalDataService)
    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD clear personal data ON patch deletePersonalData call WHEN googleAdId is passed`() = withTestApplication(controller()) {
    val googleAdId = "googleAdId"
    val userId1 = "userId1"
    val userId2 = "userId2"

    userService.mock({ fetchAllUserIds(googleAdId) }, listOf(userId1, userId2))

    val testCall: TestApplicationCall =
      handleRequest(method = HttpMethod.Patch, uri = "/personalData/delete") {
        addHeader("Content-Type", "application/json")
        setBody(
          jsonConverter.encodeToString(
            DeletePersonalDataRequestDto(
              dryRun = false,
              googleAdId = googleAdId,
              email = ""
            )
          )
        )
      }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)

    verifyBlocking(personalDataService) { deletePersonalsForUser(userId = userId1, googleAdId = googleAdId) }
    verifyBlocking(personalDataService) { deletePersonalsForUser(userId = userId2, googleAdId = googleAdId) }
  }

  @Test
  fun `SHOULD clear personal data ON patch deletePersonalData call WHEN email is passed`() = withTestApplication(controller()) {
    val googleAdId1 = "googleAdId1"
    val googleAdId2 = "googleAdId2"
    val email = "<EMAIL>"
    val userId1 = "userId1"
    val userId2 = "userId2"
    val user1Dto = userDtoStub.copy(id = userId1, googleAdId = googleAdId1)
    val user2Dto = userDtoStub.copy(id = userId2, googleAdId = googleAdId2)

    cashoutService.mock({ loadUserIdsForEmail(email) }, listOf(userId1, userId2))
    userService.mock({ getUser(userId1, includingDeleted = true) }, user1Dto)
    userService.mock({ getUser(userId2, includingDeleted = true) }, user2Dto)

    val testCall: TestApplicationCall =
      handleRequest(method = HttpMethod.Patch, uri = "/personalData/delete") {
        addHeader("Content-Type", "application/json")
        setBody(
          jsonConverter.encodeToString(
            DeletePersonalDataRequestDto(
              dryRun = false,
              googleAdId = "",
              email = email
            )
          )
        )
      }

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)

    verifyBlocking(personalDataService) { deletePersonalsForUser(userId = userId1, googleAdId = googleAdId1) }
    verifyBlocking(personalDataService) { deletePersonalsForUser(userId = userId2, googleAdId = googleAdId2) }
  }

  @Test
  fun `SHOULD fail ON patch deletePersonalData call WHEN both email and googleAdId are empty`() = withTestApplication(controller()) {
    val exc =
      assertFailsWith<IllegalArgumentException> {
        handleRequest(method = HttpMethod.Patch, uri = "/personalData/delete") {
          addHeader("Content-Type", "application/json")
          setBody(
            jsonConverter.encodeToString(
              DeletePersonalDataRequestDto(
                dryRun = false,
                googleAdId = "",
                email = ""
              )
            )
          )
        }
      }

    assertThat(exc.message).isEqualTo("googleAdId or email must be defined")
  }

  @Test
  fun `SHOULD fail ON patch deletePersonalData call WHEN both email and googleAdId are defined`() = withTestApplication(controller()) {

    val exc = assertFailsWith<IllegalArgumentException> {
      handleRequest(method = HttpMethod.Patch, uri = "/personalData/delete") {
        addHeader("Content-Type", "application/json")
        setBody(
          jsonConverter.encodeToString(
            DeletePersonalDataRequestDto(
              dryRun = true,
              googleAdId = "googleAdId",
              email = "<EMAIL>"
            )
          )
        )
      }
    }

    assertThat(exc.message).isEqualTo("Only one parameter must be defined: googleAdId or email")
  }

  @Test
  fun `SHOULD return user de-duplicated personal data ON extract call`() = withTestApplication(controller()) {
    val now = Instant.now()
    val email = "<EMAIL>"
    val expected = GetPersonalDataResponseDto(
      names = listOf("John Doe", "John Wick"),
      emails = listOf("<EMAIL>", "<EMAIL>"),
      ips = listOf("ip1", "ip2"),
      googleAdIds = listOf("gaid1", "gaid2"),
      facePictureTrackedUntil = now.plus(VerificationService.FACE_PICTURE_AUDIT_TIME_TO_LIVE_DAYS, ChronoUnit.DAYS),
      faceMapTrackedUntil = now.plus(VerificationService.FACE_MAP_TIME_TO_LIVE_DAYS, ChronoUnit.DAYS),
    )
    cashoutService.mock({ loadUserIdsForEmail(email) }, listOf("userId1", "userId2", "userId3"))
    personalDataService.mock({ getTrackedPersonalData("userId1") }, expected)
    personalDataService.mock(
      { getTrackedPersonalData("userId2") },
      expected.copy(
        ips = listOf("ip1", "ip3"),
        facePictureTrackedUntil = expected.facePictureTrackedUntil!!.plus(1, ChronoUnit.DAYS),
        faceMapTrackedUntil = expected.faceMapTrackedUntil!!.minus(1, ChronoUnit.DAYS)
      )
    )
    personalDataService.mock(
      { getTrackedPersonalData("userId3") },
      expected.copy(
        facePictureTrackedUntil = null,
        faceMapTrackedUntil = null
      )
    )

    val testCall: TestApplicationCall =
      handleRequest(method = HttpMethod.Get, uri = "/personalData/extract?email=$email") {
        addHeader("Content-Type", "application/json")
      }

    verifyBlocking(personalDataService) { getTrackedPersonalData("userId1") }
    verifyBlocking(personalDataService) { getTrackedPersonalData("userId2") }
    verifyBlocking(personalDataService) { getTrackedPersonalData("userId3") }

    verifyNoMoreInteractions(personalDataService)

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(jsonConverter.decodeFromString<GetPersonalDataResponseDto>(testCall.response.content!!))
      .isEqualTo(
        expected.copy(
          ips = listOf("ip1", "ip2", "ip3"), // merged and deduplicated
          facePictureTrackedUntil = expected.facePictureTrackedUntil!!.plus(1, ChronoUnit.DAYS), // max is picked
          faceMapTrackedUntil = expected.faceMapTrackedUntil // max is picked
        )
      )
  }

  @Test
  fun `SHOULD return empty personal data ON extract call WHEN user data not found`() = withTestApplication(controller()) {
    val email = "<EMAIL>"
    val expected = GetPersonalDataResponseDto(
      names = emptyList(),
      emails = emptyList(),
      ips = emptyList(),
      googleAdIds = emptyList(),
      facePictureTrackedUntil = null,
      faceMapTrackedUntil = null,
    )
    cashoutService.mock({ loadUserIdsForEmail(email) }, listOf("userId1"))
    personalDataService.mock({ getTrackedPersonalData("userId1") }, expected)

    val testCall: TestApplicationCall =
      handleRequest(method = HttpMethod.Get, uri = "/personalData/extract?email=$email") {
        addHeader("Content-Type", "application/json")
      }

    verifyBlocking(personalDataService) { getTrackedPersonalData("userId1") }
    verifyNoMoreInteractions(personalDataService)

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(jsonConverter.decodeFromString<GetPersonalDataResponseDto>(testCall.response.content!!))
      .isEqualTo(expected)
  }

  @Test
  fun `SHOULD return empty personal data ON extract call WHEN user is not tracked`() = withTestApplication(controller()) {
    val email = "<EMAIL>"
    val expected = GetPersonalDataResponseDto()
    cashoutService.mock({ loadUserIdsForEmail(email) }, listOf())

    val testCall: TestApplicationCall =
      handleRequest(method = HttpMethod.Get, uri = "/personalData/extract?email=$email") {
        addHeader("Content-Type", "application/json")
      }

    verifyNoInteractions(personalDataService)

    assertThat(testCall.response.status()).isEqualTo(HttpStatusCode.OK)
    assertThat(jsonConverter.decodeFromString<GetPersonalDataResponseDto>(testCall.response.content!!))
      .isEqualTo(expected)
  }
}
