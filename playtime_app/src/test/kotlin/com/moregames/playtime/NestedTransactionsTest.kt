package com.moregames.playtime

import assertk.assertThat
import assertk.assertions.isNotNull
import assertk.assertions.isNull
import com.moregames.base.db.TransactorImpl
import com.moregames.base.gamecoingoals.GameCoinGoalEntity
import com.moregames.base.gamecoingoals.GameCoinGoalPersistenceService
import com.moregames.base.table.DatabaseExtension
import com.moregames.base.util.prepareUser
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.jetbrains.exposed.sql.Database
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import java.time.Instant
import java.util.concurrent.Executors

@ExtendWith(DatabaseExtension::class)
class NestedTransactionsTest(
  private val database: Database
) {

  private val transactor = TransactorImpl(database)
  private val underTest = GameCoinGoalPersistenceService(database, mock {
    on { now() } doReturn Instant.now()
  })

  @Test
  fun `SHOULD handle multiple nested transactions`() = runBlocking {
    val user = database.prepareUser()
    println("prepared user")
    underTest.createSet(user, GameCoinGoalEntity(null, 123, null, 1))
    println("created set")
    transactor.inTransaction {
      var set = underTest.findActiveCoinGoalSet(user)
      assertThat(set).isNotNull().transform { it.activeGoal() }.isNotNull()
      underTest.markAsClaimed(set!!.activeGoal()!!.id)
      forceContextSwitch()
      set = underTest.findActiveCoinGoalSet(user)
      assertThat(set).isNotNull().transform { it.activeGoal() }.isNull()
      println("there's no active goal in current transaction")
      val t = Thread {
        runBlocking {
          println("running concurrent transaction")
          val res = underTest.findActiveCoinGoalSet(user)
          assertThat(res).isNotNull().transform { it.activeGoal() }.isNotNull()
          println("concurrent transaction finished, goal is still in place")
        }
      }
      t.start()
      println("waiting for thread")
      t.join()
      println("thread finished")
    }
    val set = underTest.findActiveCoinGoalSet(user)
    assertThat(set).isNotNull().transform { it.activeGoal() }.isNull()
    println("after commit there's no active goal as well")
  }

  private suspend fun forceContextSwitch() {
    withContext(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
      delay(100)
      println(Thread.currentThread().name)
    }
  }
}