package com.moregames.playtime.games

import assertk.assertThat
import assertk.assertions.isFalse
import assertk.assertions.isTrue
import com.moregames.base.app.BuildVariant
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.base.util.redis.SafeJedisClient
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.mockito.kotlin.argThat
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import java.time.LocalDate
import kotlin.test.Test

class Em2IosBoostedGameServiceTest {
  private val safeJedisClient: SafeJedisClient = mock()
  private val gamePersistenceService: GamePersistenceService = mock()
  private val timeService: TimeService = mock()

  private val service = Em2IosBoostedGameService(
    safeJedisClient = safeJedisClient,
    gamePersistenceService = gamePersistenceService,
    timeService = timeService,
    buildVariant = BuildVariant.PRODUCTION,
  )

  companion object {
    const val GAME_ID = 12345
    const val OTHER_GAME_ID = 54321
    val today: LocalDate = LocalDate.now()
  }

  @BeforeEach
  fun init() {
    safeJedisClient.mock({ get(Em2IosBoostedGameService.CACHE_KEY) }, GAME_ID.toString())
    timeService.mock({ today() }, today)
  }

  @Test
  fun `SHOULD return true ON isIosBoostedGame WHEN gameId matches cached boosted game`() {
    runBlocking {
      service.isIosBoostedGame(GAME_ID)
    }.let { assertThat(it).isTrue() }
  }

  @Test
  fun `SHOULD return false ON isIosBoostedGame WHEN gameId does not match cached boosted game`() {
    safeJedisClient.mock({ get(Em2IosBoostedGameService.CACHE_KEY) }, OTHER_GAME_ID.toString())

    runBlocking {
      service.isIosBoostedGame(GAME_ID)
    }.let { assertThat(it).isFalse() }
  }

  @Test
  fun `SHOULD fetch boosted game id from persistence WHEN cache is empty AND match`() {
    safeJedisClient.mock({ get(Em2IosBoostedGameService.CACHE_KEY) }, null)
    gamePersistenceService.mock({ findTodayIosBoostedGameId(today) }, GAME_ID)

    runBlocking {
      service.isIosBoostedGame(GAME_ID)
    }.let { assertThat(it).isTrue() }

    verifyBlocking(safeJedisClient) {
      set(
        eq(value = Em2IosBoostedGameService.CACHE_KEY),
        eq(GAME_ID.toString()),
        argThat { this.toString() == "[ex, 600]" }
      )
    }
  }

  @Test
  fun `SHOULD fetch boosted game id from persistence WHEN cache is empty AND not match`() {
    safeJedisClient.mock({ get(Em2IosBoostedGameService.CACHE_KEY) }, null)
    gamePersistenceService.mock({ findTodayIosBoostedGameId(today) }, OTHER_GAME_ID)

    runBlocking {
      service.isIosBoostedGame(GAME_ID)
    }.let { assertThat(it).isFalse() }

    verifyBlocking(safeJedisClient) {
      set(
        eq(value = Em2IosBoostedGameService.CACHE_KEY),
        eq(OTHER_GAME_ID.toString()),
        argThat { this.toString() == "[ex, 600]" }
      )
    }
  }

  @Test
  fun `SHOULD use NO_BOOSTED_GAME_ID_STUB WHEN no boosted game is found`() {
    safeJedisClient.mock({ get(Em2IosBoostedGameService.CACHE_KEY) }, null)
    gamePersistenceService.mock({ findTodayIosBoostedGameId(today) }, null)

    runBlocking {
      service.isIosBoostedGame(GAME_ID)
    }.let { assertThat(it).isFalse() }

    verifyBlocking(safeJedisClient) {
      set(
        eq(value = Em2IosBoostedGameService.CACHE_KEY),
        eq("-1"),
        argThat { this.toString() == "[ex, 600]" }
      )
    }
  }
}