package com.moregames.playtime.general

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.table.DatabaseExtension
import com.moregames.playtime.general.SettingsPersistenceService.Settings
import com.moregames.playtime.general.table.SettingsTable
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.deleteAll
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(DatabaseExtension::class)
class SettingsPersistenceServiceTest(
  private val database: Database,
) {

  private lateinit var service: SettingsPersistenceService

  @BeforeEach
  fun before() {
    service = SettingsPersistenceService(database)
    transaction(database) { SettingsTable.deleteAll() }
  }

  @Test
  fun `SHOULD load settings on loadSettings WHEN we have some settings`() {
    transaction(database) {
      SettingsTable.insert {
        it[expEarningsUserFilter] = "some filter here"
        it[iosEarningsIncreasePercentage] = 10
      }
    }

    runBlocking { service.loadSettings() }.let {
      assertThat(it).isEqualTo(
        Settings(
          expEarningsUserFilter = "some filter here",
          iosEarningsIncreasePercentage = 10,
        )
      )
    }
  }

  @Test
  fun `SHOULD return settings with default values WHEN we have no settings`() {
    runBlocking { service.loadSettings() }.let {
      assertThat(it).isEqualTo(
        Settings(
          expEarningsUserFilter = null,
          iosEarningsIncreasePercentage = 0,
        )
      )
    }
  }
}