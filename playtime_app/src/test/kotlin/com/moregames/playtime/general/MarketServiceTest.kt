package com.moregames.playtime.general

import assertk.assertThat
import assertk.assertions.*
import com.ibm.icu.util.TimeZone
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.user.dto.PrivacyRegulationType
import com.moregames.base.util.mock
import com.moregames.playtime.user.AdditionalCountryInfo
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.util.toNormalizedLanguageTag
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.*
import java.util.*
import kotlin.test.assertTrue


// TODO: refactor tests after moving to non-stub implementation
class MarketServiceTest {
  private val abTestingService: AbTestingService = mock()
  private val userPersistenceService: UserPersistenceService = mock()
  private val applicationConfig: ApplicationConfig = mock {
    on { justplayMarket } doReturn "test-market"
    on { allowedCountries } doReturn setOf("US", "CA")
  }

  private val service = MarketService(abTestingService, userPersistenceService, applicationConfig)

  private companion object {
    const val userId = "userId"
  }

  @BeforeEach
  fun before() {
    userPersistenceService.mock({ isUserWhitelisted(userId) }, false)
  }

  @ParameterizedTest
  @CsvSource("us,true", "US,false", "Us,false", "uS,false", "GB,false")
  fun `SHOULD respect market ON isUsMarket`(market: String, expected: Boolean) {
    val spy: MarketService = spy(service)
    whenever(spy.getJPMarket()).thenReturn(market)

    runBlocking { spy.isUsMarket() }.let { assertThat(it).isEqualTo(expected) }
  }

  @ParameterizedTest
  @CsvSource("us-staging,true", "Us-Staging,false", "US,false", "not a market,false")
  fun `SHOULD respect market ON isUsMarket WHEN it is us-staging market`(market: String, expected: Boolean) {
    val spy: MarketService = spy(service)
    whenever(spy.getJPMarket()).thenReturn(market)

    runBlocking { spy.isUsStagingMarket() }.let { assertThat(it).isEqualTo(expected) }
  }

  @ParameterizedTest
  @CsvSource("ios-us,true", "Ios-Us,false", "US,false", "not a market,false")
  fun `SHOULD respect market ON isIosUsMarket WHEN it is ios-us market`(market: String, expected: Boolean) {
    val spy: MarketService = spy(service)
    whenever(spy.getJPMarket()).thenReturn(market)

    runBlocking { spy.isIosUsMarket() }.let { assertThat(it).isEqualTo(expected) }
  }

  @ParameterizedTest
  @CsvSource("latam,true", "laTam,false", "US,false", "not a market,false")
  fun `SHOULD respect market ON isLatamMarket WHEN it is latam market`(market: String, expected: Boolean) {
    val spy: MarketService = spy(service)
    whenever(spy.getJPMarket()).thenReturn(market)

    runBlocking { spy.isLatamMarket() }.let { assertThat(it).isEqualTo(expected) }
  }

  @ParameterizedTest
  @CsvSource("gb,true", "GB,false", "US,false")
  fun `SHOULD respect market ON isGbMarket`(market: String, expected: Boolean) {
    val spy: MarketService = spy(service)
    whenever(spy.getJPMarket()).thenReturn(market)

    runBlocking { spy.isGbMarket() }.let { assertThat(it).isEqualTo(expected) }
  }

  @ParameterizedTest
  @CsvSource("au,true", "aU,false", "us,false")
  fun `SHOULD respect market on isAuMarket`(market: String, expected: Boolean) {
    val spy: MarketService = spy(service)
    whenever(spy.getJPMarket()).thenReturn(market)

    runBlocking { spy.isAuMarket() }.let { assertThat(it).isEqualTo(expected) }
  }

  @ParameterizedTest
  @CsvSource("asia,true", "asia-test,true", "aSia,false", "us,false")
  fun `SHOULD respect market on isAsianMarket`(market: String, expected: Boolean) {
    val spy: MarketService = spy(service)
    whenever(spy.getJPMarket()).thenReturn(market)

    runBlocking { spy.isAsianMarket() }.let { assertThat(it).isEqualTo(expected) }
  }

  @Test
  fun `SHOULD load allowed countries ON getAllowedCountries`() {
    val actual = runBlocking {
      service.getAllowedCountries()
    }

    assertThat(actual).isEqualTo(setOf("US", "CA"))
  }

  @Test
  fun `SHOULD load supported currencies ON getAllowedCountries`() {
    val actual = runBlocking {
      service.getSupportedCurrencies()
    }

    assertThat(actual).isEqualTo(setOf(Currency.getInstance("USD"), Currency.getInstance("CAD")))
  }

  @Test
  fun `SHOULD return user currency ON getUserCurrency`() {
    val userId = "userId"
    userPersistenceService.mock({ getUserCountryCode(userId) }, "US")

    val actual = runBlocking {
      service.getUserCurrency(userId)
    }

    assertThat(actual).isEqualTo(Currency.getInstance("USD"))
  }

  @Test
  fun `SHOULD return user currency ON getUserCurrency WHEN currency is allowed`() {
    val userId = "userId"
    userPersistenceService.mock({ getUserCountryCode(userId) }, "CA")

    val actual = runBlocking {
      service.getUserCurrency(userId)
    }

    assertThat(actual).isEqualTo(Currency.getInstance("CAD"))
  }

  @Test
  fun `SHOULD return USD currency (fallback) ON getUserCurrency WHEN country is not allowed`() {
    userPersistenceService.mock({ getUserCountryCode(userId) }, "NG")

    val actual = runBlocking {
      service.getUserCurrency(userId)
    }

    assertThat(actual).isEqualTo(Currency.getInstance("USD"))
  }

  @ParameterizedTest
  @ValueSource(strings = ["US", "CA"])
  fun `SHOULD return country code ON getUserCountryCode WHEN country from allowed list`(allowedCountryCode: String) {
    userPersistenceService.mock({ getUserCountryCode(userId) }, allowedCountryCode)

    val actual = runBlocking {
      service.getUserAllowedCountryCodeOrUS(userId)
    }

    assertThat(actual).isEqualTo(allowedCountryCode)
  }

  @Test
  fun `SHOULD return US country code ON getUserCountryCode WHEN country not from allowed list`() {
    userPersistenceService.mock({ getUserCountryCode(userId) }, "NG")

    val actual = runBlocking {
      service.getUserAllowedCountryCodeOrUS(userId)
    }

    assertThat(actual).isEqualTo("US")
  }

  @Test
  fun `SHOULD return true if user sim network and registration country are from allowed countries ON isUserFromAllowedCountry`() {
    userPersistenceService.mock({ getUserCountryCode(userId) }, "US")
    userPersistenceService.mock({ getUserAdditionalCountryInfo(userId) }, AdditionalCountryInfo(userId, "US", "US"))

    val actual = runBlocking {
      service.isUserFromAllowedCountry(userId)
    }

    assertThat(actual).isTrue()

    runBlocking {
      service.usersSimAndNetworkFromAllowedCountry(userId)
    }.let { assertThat(it).isTrue() }
  }

  @Test
  fun `SHOULD return true if user sim network and registration country are unknown ON isUserFromAllowedCountry`() {
    userPersistenceService.mock({ getUserCountryCode(userId) }, "US")
    userPersistenceService.mock({ getUserAdditionalCountryInfo(userId) }, AdditionalCountryInfo(userId, null, null))

    val actual = runBlocking {
      service.isUserFromAllowedCountry(userId)
    }

    assertThat(actual).isTrue()

    runBlocking {
      service.usersSimAndNetworkFromAllowedCountry(userId)
    }.let { assertThat(it).isTrue() }
  }

  @Test
  fun `SHOULD return false if user sim is from not allowed country ON isUserFromAllowedCountry`() {
    userPersistenceService.mock({ getUserCountryCode(userId) }, "US")
    userPersistenceService.mock({ getUserAdditionalCountryInfo(userId) }, AdditionalCountryInfo(userId, "NG", null))

    val actual = runBlocking {
      service.isUserFromAllowedCountry(userId)
    }

    assertThat(actual).isFalse()

    runBlocking {
      service.usersSimAndNetworkFromAllowedCountry(userId)
    }.let { assertThat(it).isFalse() }
  }

  @Test
  fun `SHOULD return false if user network is from not allowed country ON isUserFromAllowedCountry`() {
    userPersistenceService.mock({ getUserCountryCode(userId) }, "US")
    userPersistenceService.mock({ getUserAdditionalCountryInfo(userId) }, AdditionalCountryInfo(userId, "US", "NG"))

    val actual = runBlocking {
      service.isUserFromAllowedCountry(userId)
    }

    assertThat(actual).isFalse()

    runBlocking {
      service.usersSimAndNetworkFromAllowedCountry(userId)
    }.let { assertThat(it).isFalse() }
  }

  @Test
  fun `SHOULD return false if user was registered from not allowed country  ON isUserFromAllowedCountry`() {
    userPersistenceService.mock({ getUserCountryCode(userId) }, "NG")
    userPersistenceService.mock({ getUserAdditionalCountryInfo(userId) }, AdditionalCountryInfo(userId, "US", "US"))

    val actual = runBlocking {
      service.isUserFromAllowedCountry(userId)
    }

    assertThat(actual).isFalse()

    runBlocking {
      service.usersSimAndNetworkFromAllowedCountry(userId)
    }.let { assertThat(it).isTrue() }
  }

  @Test
  fun `SHOULD return true ON isUserFromAllowedCountry WHEN user is whitelisted`() {
    userPersistenceService.mock({ isUserWhitelisted(userId) }, true)

    val actual = runBlocking {
      service.isUserFromAllowedCountry(userId)
    }

    assertThat(actual).isTrue()
    verifyBlocking(userPersistenceService, never()) { getUserAdditionalCountryInfo(userId) }
    verifyBlocking(userPersistenceService, never()) { getUserCountryCode(userId) }
  }

  @ParameterizedTest
  @CsvSource(
    "us, EN, US, true", "us, EN, CA, true", "us, ES, US, true", "us, FR, CA, true",
    "us, FR, ES, false", "us, PT, US, false", "us, EN, ES, false", "us, ES, MX, false", "us, DE, CA, false",
    "gb, EN, US, true", "gb, FR, FR, true", "gb, FR, DE, true",
    "us-staging, EN, US, true", "us-staging, EN, CA, true", "us-staging, ES, US, true", "us-staging, FR, CA, true",
    "us-staging, FR, ES, false", "us-staging, PT, US, false", "us-staging, EN, ES, false", "us-staging, ES, MX, false", "us-staging, DE, CA, false",
    "de, DE, DE, true",
    "latam, EN, MX, true", "latam, ES, MX, true", "latam, PT, BR, true", "latam, ES, CA, true"
  )
  fun `SHOULD check if deviceLocale matches country ON deviceLocaleMatchesCountry`(
    market: String, deviceLocale: String?, networkCountry: String?, result: Boolean
  ) {
    val spy: MarketService = spy(service)
    whenever(spy.getJPMarket()).thenReturn(market)
    val actual = spy.deviceLocaleMatchesCountry(deviceLocale, networkCountry)
    assertThat(actual).isEqualTo(result)
  }

  @ParameterizedTest
  @CsvSource("us, true", "test, false", "NG, false")
  fun `SHOULD return true (US) or false (Outside US) based on country code ON isUsUser`(market: String, result: Boolean) {
    val spy: MarketService = spy(service)
    whenever(spy.getJPMarket()).thenReturn(market)
    userPersistenceService.mock({ getUserCountryCode(userId) }, market)
    val actual = runBlocking {
      spy.isUsUser(userId)
    }
    assertThat(actual).isEqualTo(result)
  }

  @ParameterizedTest
  @CsvSource("FR, true", "gb, true", "SOME, false", "IS, true", "NO, true", "LI, true", "CH, true")
  fun `SHOULD calculate correct gdpr applies flag ON isGdprAppliesToCountry`(countryCode: String, result: Boolean) {
    val actual = runBlocking {
      service.isGdprAppliesToCountry(countryCode)
    }
    assertThat(actual).isEqualTo(result)
  }

  @ParameterizedTest
  @CsvSource("FR, true", "gb, false", "SOME, false", "CZ, true", "PT, true", "LT, true", "RO, true")
  fun `SHOULD calculate correct eu  flag ON isEUCountry`(countryCode: String, result: Boolean) {
    val actual = runBlocking {
      service.isEUCountry(countryCode)
    }
    assertThat(actual).isEqualTo(result)
  }

  @ParameterizedTest
  @ValueSource(strings = ["us", "us-staging", "us-staging-test", "au", "au-test", "some-new-market"])
  fun `SHOULD return english on getAllowedLanguages WHEN it is a unknown market or known mostly english market`(market: String) {
    val spy: MarketService = spy(service)
    whenever(spy.getJPMarket()).thenReturn(market)

    runBlocking { spy.getAllowedLanguages() }
      .let { assertThat(it).isEqualTo(listOf(Locale.ENGLISH)) }
  }

  @ParameterizedTest
  @ValueSource(strings = ["gb", "gb-test"])
  fun `SHOULD return european languages on getAllowedLanguages WHEN it is the gb market`(market: String) {
    val spy: MarketService = spy(service)
    whenever(spy.getJPMarket()).thenReturn(market)

    runBlocking { spy.getAllowedLanguages() }
      .let {
        assertThat(it.map { locale -> locale.toNormalizedLanguageTag() })
          .isEqualTo(listOf("en", "de", "es", "fr", "it", "nl", "pl", "pt"))
      }
  }

  @ParameterizedTest
  @ValueSource(strings = ["latam", "latam-test"])
  fun `SHOULD return latam languages on getAllowedLanguages WHEN it is the latam market`(market: String) {
    val spy: MarketService = spy(service)
    whenever(spy.getJPMarket()).thenReturn(market)

    runBlocking { spy.getAllowedLanguages() }
      .let {
        assertThat(it.map { locale -> locale.toNormalizedLanguageTag() })
          .isEqualTo(listOf("en", "pt-br", "es-mx"))
      }
  }

  @ParameterizedTest
  @ValueSource(strings = ["asia", "asia-test"])
  fun `SHOULD return asian languages on getAllowedLanguages WHEN it is the asia market`(market: String) {
    val spy: MarketService = spy(service)
    whenever(spy.getJPMarket()).thenReturn(market)

    runBlocking { spy.getAllowedLanguages() }
      .let {
        assertThat(it.map { locale -> locale.toNormalizedLanguageTag() })
          .isEqualTo(listOf("en", "ja", "ko", "zh-hk", "zh-tw"))
      }
  }

  @ParameterizedTest
  @ValueSource(strings = ["us-test"])
  fun `SHOULD return whole language set on getAllowedLanguages WHEN it is the us-test market`(market: String) {
    val spy: MarketService = spy(service)
    whenever(spy.getJPMarket()).thenReturn(market)

    runBlocking { spy.getAllowedLanguages() }
      .let {
        assertThat(it.map { locale -> locale.toNormalizedLanguageTag() })
          .isEqualTo(listOf("en", "de", "es", "fr", "it", "nl", "pl", "pt", "pt-br", "es-mx", "ja", "ko", "zh-hk", "zh-tw"))
      }
  }

  @Test
  fun `SHOULD return USD currency ON getSupportedCurrencies WHEN US country not supported`() {
    val spy: MarketService = spy(service)
    whenever(spy.getAllowedCountries()).thenReturn(setOf("CA", "DE"))

    assertThat(spy.getAllowedCountries()).doesNotContain("US")
    assertThat(spy.getSupportedCurrencies()).contains(Currency.getInstance("USD"))
  }

  @Test
  fun `SHOULD correctly check allowedTimeZones ON isTimeZoneAllowed`() {
    val expectedAllowedTimeZones = setOf("US", "CA").flatMap { TimeZone.getAvailableIDs(it).toList() }

    expectedAllowedTimeZones.forEach { assertTrue(runBlocking { service.isTimeZoneAllowed(it) }) }
  }

  @Test
  fun `SHOULD return all markets  ON getAllMarkets`() {
    val actual = runBlocking {
      service.getAllMarkets()
    }

    assertThat(actual).isEqualTo(
      setOf(
        "us", "us-test",
        "us-staging", "us-staging-test",
        "gb", "gb-test",
        "au", "au-test",
        "asia", "asia-test",
        "latam", "latam-test",
        "ios-us", "ios-us-test"
      )
    )
  }

  @Test
  fun `SHOULD return all languages on getAllowedLanguagesForFallback`() {
    runBlocking { service.getAllowedLanguagesForFallback() }
      .let {
        assertThat(it.map { locale -> locale.toNormalizedLanguageTag() })
          .isEqualTo(
            listOf(
              "en", "de", "es", "fr", "it", "nl", "pl", "pt",
              "pt-br", "es-mx",
              "ja", "ko", "zh-hk", "zh-tw",
              "pt-br"
            )
          )
      }
  }

  @ParameterizedTest
  @ValueSource(
    strings = [
      "AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE",
      "EL", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NL", "PL", "PT",
      "RO", "SK", "SI", "ES", "SE", "GB", "CH", "IS", "NO", "LI",
      "at"]
  )
  fun `SHOULD return gdpr privacy regulation ON getPrivacyRegulation WHEN gdpr countries`(countryCode: String) {
    service.getPrivacyRegulation(countryCode).let { result ->
      assertThat(result).isEqualTo(PrivacyRegulationType.GDPR)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["us", "US"])
  fun `SHOULD return opt-out privacy regulation ON getPrivacyRegulation WHEN us`(countryCode: String) {
    service.getPrivacyRegulation(countryCode).let { result ->
      assertThat(result).isEqualTo(PrivacyRegulationType.OPT_OUT)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["mx", "MX"])
  fun `SHOULD return null privacy regulation ON getPrivacyRegulation WHEN some other country`(countryCode: String) {
    service.getPrivacyRegulation(countryCode).let { result ->
      assertThat(result).isNull()
    }
  }
}
