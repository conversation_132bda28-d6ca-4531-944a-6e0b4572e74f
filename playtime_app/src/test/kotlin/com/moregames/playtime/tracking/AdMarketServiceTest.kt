package com.moregames.playtime.tracking

import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.dto.TrackingDataType.IDFV
import com.moregames.base.junit.MockExtension
import com.moregames.base.messaging.dto.AdMarketEvent
import com.moregames.base.util.TimeService
import com.moregames.base.util.answer
import com.moregames.base.util.mock
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.dto.UserExternalIds
import com.moregames.playtime.user.tracking.TrackingData
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.whenever
import java.time.Instant
import java.util.*

@ExtendWith(MockExtension::class)
class AdMarketServiceTest(
  private val userService: UserService,
  private val messageBus: MessageBus,
  private val timeService: TimeService,
  private val adMarketPersistenceService: AdMarketPersistenceService,
) {
  private val service = AdMarketService(
    userService = userService,
    messageBus = messageBus,
    timeService = timeService,
    adMarketPersistenceService = adMarketPersistenceService,
  )

  private val now = Instant.now()

  @BeforeEach
  fun before() {
    whenever(timeService.now()).thenReturn(now)
    adMarketPersistenceService.answer({ storeEvents(any(), any()) }, { it.arguments[1] as List<*> })
  }

  @Test
  fun `SHOULD send market events ON sendMarketEvents`() {
    val userId = UUID.randomUUID().toString()

    val allEvents = listOf(
      "cashout_2_reached_1", "cashout_2_reached_2", "cashout_2_reached_3", "cashout_2_reached_4",
      "cashout_8_reached_8", "cashout_8_reached_13"
    )

    userService.mock(
      { fetchExternalIds(userId) },
      UserExternalIds(userId, "googleAdId", "idfa", "adjustId", "firebaseAppId", null)
    )

    runBlocking { service.sendMarketEvents(userId, allEvents) }

    allEvents.forEach { eventTypeStr ->
      val eventType = when (eventTypeStr) {
        "cashout_2_reached_1" -> AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_1_DOLLAR
        "cashout_2_reached_2" -> AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_2_DOLLARS
        "cashout_2_reached_3" -> AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_3_DOLLARS
        "cashout_2_reached_4" -> AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_4_DOLLARS
        "cashout_8_reached_8" -> AdMarketEvent.EventType.EARNED_WITHIN_8_DAYS_8_DOLLARS
        "cashout_8_reached_13" -> AdMarketEvent.EventType.EARNED_WITHIN_8_DAYS_13_DOLLARS
        else -> throw Exception()
      }

      verifyBlocking(messageBus) {
        publish(
          AdMarketEvent(
            userId = userId,
            googleAdId = "googleAdId",
            firebaseAppId = "firebaseAppId",
            adjustId = "adjustId",
            idfa = "idfa",
            eventType = eventType,
            createdAt = now,
            trackingId = null,
            trackingType = null,
            appPlatform = null,
          )
        )
      }
    }

  }

  @Test
  fun `SHOULD send market events ON sendMarketEvents WHE iOS user`() {
    val userId = UUID.randomUUID().toString()
    val td = TrackingData("trackingId", IDFV, IOS)
    val allEvents = listOf(
      "cashout_2_reached_1", "cashout_2_reached_2", "cashout_2_reached_3", "cashout_2_reached_4",
      "cashout_8_reached_8", "cashout_8_reached_13"
    )

    userService.mock(
      { fetchExternalIds(userId) },
      UserExternalIds(userId, "googleAdId", null, "adjustId", "firebaseAppId", td)
    )

    runBlocking { service.sendMarketEvents(userId, allEvents) }

    allEvents.forEach { eventTypeStr ->
      val eventType = when (eventTypeStr) {
        "cashout_2_reached_1" -> AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_1_DOLLAR
        "cashout_2_reached_2" -> AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_2_DOLLARS
        "cashout_2_reached_3" -> AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_3_DOLLARS
        "cashout_2_reached_4" -> AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_4_DOLLARS
        "cashout_8_reached_8" -> AdMarketEvent.EventType.EARNED_WITHIN_8_DAYS_8_DOLLARS
        "cashout_8_reached_13" -> AdMarketEvent.EventType.EARNED_WITHIN_8_DAYS_13_DOLLARS
        else -> throw Exception()
      }

      verifyBlocking(messageBus) {
        publish(
          AdMarketEvent(
            userId = userId,
            googleAdId = "googleAdId",
            adjustId = "adjustId",
            firebaseAppId = "firebaseAppId",
            eventType = eventType,
            createdAt = now,
            trackingId = td.id,
            trackingType = td.type.name,
            appPlatform = td.platform.name,
            platform = td.platform.name,
          )
        )
      }
    }
  }

  @Test
  fun `SHOULD not send market events ON sendMarketEvents WHEN no externalIds for user`() {
    val userId = UUID.randomUUID().toString()

    val events = listOf("cashout_2_reached_1")

    userService.mock({ fetchExternalIds(userId) }, null)

    runBlocking { service.sendMarketEvents(userId, events) }

    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD not send market events ON sendMarketEvents WHEN no adjustId for user and event is not GA compatible`() {
    val userId = UUID.randomUUID().toString()

    val notGaEvent = AdMarketEvent.EventType.entries.find { it.isGaS2s }?.eventName

    userService.mock(
      { fetchExternalIds(userId) },
      UserExternalIds(userId, "googleAdId", null, null, "firebaseAppId", null)
    )

    runBlocking { service.sendMarketEvents(userId, listOfNotNull(notGaEvent)) }

    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD not send market events ON sendMarketEvents WHEN event type unknown`() {
    val userId = UUID.randomUUID().toString()

    val events = listOf("unknown event")

    userService.mock(
      { fetchExternalIds(userId) },
      UserExternalIds(userId, "googleAdId", null, null, "firebaseAppId", null)
    )

    runBlocking { service.sendMarketEvents(userId, events) }

    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD do nothing ON sendMarketEvents WHEN all events already saved`() {
    val userId = UUID.randomUUID().toString()

    val events = listOf("cashout_2_reached_2")

    userService.mock(
      { fetchExternalIds(userId) },
      UserExternalIds(userId, "googleAdId", null, "adjustId", "firebaseAppId", null)
    )

    adMarketPersistenceService.mock({ storeEvents(eq(userId), any()) }, emptyList())

    runBlocking { service.sendMarketEvents(userId, events) }

    verifyBlocking(adMarketPersistenceService) {
      storeEvents(userId, listOf(AdMarketEvent.EventType.EARNED_WITHIN_2_DAYS_2_DOLLARS))
    }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD do nothing ON sendMarketEvents WHEN no events to send`() {
    runBlocking { service.sendMarketEvents("userId", emptyList()) }

    verifyNoInteractions(messageBus)
    verifyNoInteractions(userService)
  }
}