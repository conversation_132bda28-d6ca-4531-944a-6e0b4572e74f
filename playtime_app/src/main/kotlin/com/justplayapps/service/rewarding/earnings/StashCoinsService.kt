package com.justplayapps.service.rewarding.earnings

import com.google.inject.Provider
import com.justplayapps.service.rewarding.earnings.EmExperimentBaseService.Companion.EM2_STASH_VARIATIONS
import com.justplayapps.service.rewarding.facade.AbTestingFacade
import com.moregames.base.abtesting.BaseVariation
import com.moregames.base.abtesting.ClientExperiment.EARNINGS_MODEL_V2
import com.moregames.base.util.IoCoroutineScope
import kotlinx.coroutines.launch
import java.math.BigDecimal
import java.math.RoundingMode
import javax.inject.Inject

class StashCoinsService @Inject constructor(
  private val stashCoinsPersistenceService: UserStashPersistenceService,
  private val abTestingFacade: AbTestingFacade,
  private val coroutineScope: Provider<IoCoroutineScope>,
) {

  // coins are not only calculated, but also subtracted from stash
  suspend fun seizeSomeCoinsFromStash(userId: String): BigDecimal {
    if (!shouldStashCoins(userId)) return BigDecimal.ZERO

    val allStashCoins =
      stashCoinsPersistenceService
        .loadStashCoins(userId)
        ?.takeIf { it > BigDecimal.ZERO }
        ?: return BigDecimal.ZERO

    val extraCoinsToGive = allStashCoins
      .multiply(BigDecimal("0.05"))
      .setScale(3, RoundingMode.HALF_UP) // to ignore too small reminders
      .max(BigDecimal("0.001"))
      .min(allStashCoins)

    coroutineScope.get().launch { stashCoinsPersistenceService.subtractStashCoins(userId, extraCoinsToGive) }

    return extraCoinsToGive
  }

  private suspend fun stashVariation(userId: String): BaseVariation? =
    abTestingFacade.assignedVariation(userId, EARNINGS_MODEL_V2)
      .takeIf { it in EM2_STASH_VARIATIONS }

  suspend fun shouldStashCoins(userId: String): Boolean =
    stashVariation(userId) != null

}