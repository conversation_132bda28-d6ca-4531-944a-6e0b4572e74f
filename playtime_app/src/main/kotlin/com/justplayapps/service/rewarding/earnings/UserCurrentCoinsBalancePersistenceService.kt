package com.justplayapps.service.rewarding.earnings

import com.google.inject.Inject
import com.google.inject.Singleton
import com.justplayapps.service.rewarding.earnings.table.UserCoinGoalCurrentCoinsBalanceEm2Table
import com.justplayapps.service.rewarding.earnings.table.UserCoinGoalCurrentCoinsBalanceTable
import com.justplayapps.service.rewarding.earnings.table.UserVisibleCoinsTable
import com.moregames.base.base.BasePersistenceService
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.plus
import java.math.BigDecimal

@Singleton
class UserCurrentCoinsBalancePersistenceService @Inject constructor(
  database: Database,
) : BasePersistenceService(database) {

  suspend fun createZeroBalance(userId: String) = dbQuery {
    UserCoinGoalCurrentCoinsBalanceTable.insert {
      it[UserCoinGoalCurrentCoinsBalanceTable.userId] = userId
      it[coins] = 0
      it[offerCoins] = 0
      it[bonusCoins] = 0
      it[goalCoins] = 0
    }
    UserCoinGoalCurrentCoinsBalanceEm2Table.insert {
      it[UserCoinGoalCurrentCoinsBalanceEm2Table.userId] = userId
      it[gameCoins] = BigDecimal.ZERO
      it[offerCoins] = BigDecimal.ZERO
      it[bonusCoins] = BigDecimal.ZERO
      it[goalCoins] = BigDecimal.ZERO
    }
    UserVisibleCoinsTable.insert {
      it[this.userId] = userId
      it[this.coins] = BigDecimal.ZERO
    }
  }

  suspend fun getCurrentCoinsBalanceEm1(userId: String) = dbQuery {
    UserCoinGoalCurrentCoinsBalanceTable
      .slice(
        UserCoinGoalCurrentCoinsBalanceTable.coins,
        UserCoinGoalCurrentCoinsBalanceTable.offerCoins,
        UserCoinGoalCurrentCoinsBalanceTable.bonusCoins,
        UserCoinGoalCurrentCoinsBalanceTable.goalCoins,
      )
      .select(UserCoinGoalCurrentCoinsBalanceTable.userId eq userId)
      .map {
        UserCurrentCoinsBalanceEm1(
          gameCoins = it.getOrNull(UserCoinGoalCurrentCoinsBalanceTable.coins) ?: 0,
          offerCoins = it.getOrNull(UserCoinGoalCurrentCoinsBalanceTable.offerCoins) ?: 0,
          bonusCoins = it.getOrNull(UserCoinGoalCurrentCoinsBalanceTable.bonusCoins) ?: 0,
          goalCoins = it.getOrNull(UserCoinGoalCurrentCoinsBalanceTable.goalCoins),
        )
      }
      .firstOrNull()
      ?: UserCurrentCoinsBalanceEm1(
        gameCoins = 0,
        offerCoins = 0,
        bonusCoins = 0,
        goalCoins = null,
      ).also {
        UserCoinGoalCurrentCoinsBalanceTable.insert {
          it[UserCoinGoalCurrentCoinsBalanceTable.userId] = userId
          it[coins] = 0
          it[offerCoins] = 0
          it[bonusCoins] = 0
          it[goalCoins] = 0
        }
      }
  }

  suspend fun getCurrentCoinsBalanceEm2(userId: String) = dbQuery {
    UserCoinGoalCurrentCoinsBalanceEm2Table
      .slice(
        UserCoinGoalCurrentCoinsBalanceEm2Table.gameCoins,
        UserCoinGoalCurrentCoinsBalanceEm2Table.offerCoins,
        UserCoinGoalCurrentCoinsBalanceEm2Table.bonusCoins,
        UserCoinGoalCurrentCoinsBalanceEm2Table.goalCoins,
      )
      .select(UserCoinGoalCurrentCoinsBalanceEm2Table.userId eq userId)
      .map {
        UserCurrentCoinsBalanceEm2(
          gameCoins = it.getOrNull(UserCoinGoalCurrentCoinsBalanceEm2Table.gameCoins) ?: BigDecimal.ZERO,
          offerCoins = it.getOrNull(UserCoinGoalCurrentCoinsBalanceEm2Table.offerCoins) ?: BigDecimal.ZERO,
          bonusCoins = it.getOrNull(UserCoinGoalCurrentCoinsBalanceEm2Table.bonusCoins) ?: BigDecimal.ZERO,
          goalCoins = it.getOrNull(UserCoinGoalCurrentCoinsBalanceEm2Table.goalCoins),
        )
      }
      .firstOrNull()
      ?: UserCurrentCoinsBalanceEm2(
        gameCoins = BigDecimal.ZERO,
        offerCoins = BigDecimal.ZERO,
        bonusCoins = BigDecimal.ZERO,
        goalCoins = null,
      ).also {
        UserCoinGoalCurrentCoinsBalanceEm2Table.insert {
          it[UserCoinGoalCurrentCoinsBalanceEm2Table.userId] = userId
          it[gameCoins] = BigDecimal.ZERO
          it[offerCoins] = BigDecimal.ZERO
          it[bonusCoins] = BigDecimal.ZERO
          it[goalCoins] = BigDecimal.ZERO
        }
      }
  }

  suspend fun getCurrentVisibleCoins(userId: String) = dbQuery {
    UserVisibleCoinsTable
      .slice(
        UserVisibleCoinsTable.coins,
      )
      .select(UserVisibleCoinsTable.userId eq userId)
      .map {
        it.getOrNull(UserVisibleCoinsTable.coins)
      }
      .first()
  }

  suspend fun resetConvertibleCoinsEm2(userId: String) = dbQuery {
    UserCoinGoalCurrentCoinsBalanceEm2Table.update({ UserCoinGoalCurrentCoinsBalanceEm2Table.userId eq userId }) {
      it[bonusCoins] = BigDecimal.ZERO
      it[gameCoins] = BigDecimal.ZERO
      it[offerCoins] = BigDecimal.ZERO
    }
  }

  suspend fun setVisibleCoins(userId: String, coins: BigDecimal) = dbQuery {
    UserVisibleCoinsTable.update({ UserVisibleCoinsTable.userId eq userId }) {
      it[UserVisibleCoinsTable.coins] = coins
    }
  }

  suspend fun loadCurrentCoinsBalanceEm2(userId: String): UserCurrentCoinsBalance = dbQuery {
    UserCoinGoalCurrentCoinsBalanceEm2Table
      .slice(
        UserCoinGoalCurrentCoinsBalanceEm2Table.gameCoins,
        UserCoinGoalCurrentCoinsBalanceEm2Table.bonusCoins,
        UserCoinGoalCurrentCoinsBalanceEm2Table.offerCoins
      )
      .select { UserCoinGoalCurrentCoinsBalanceEm2Table.userId eq userId }
      .firstOrNull()
      ?.let { row ->
        UserCurrentCoinsBalance(
          gameCoins = row[UserCoinGoalCurrentCoinsBalanceEm2Table.gameCoins],
          offerCoins = row[UserCoinGoalCurrentCoinsBalanceEm2Table.offerCoins],
          bonusCoins = row[UserCoinGoalCurrentCoinsBalanceEm2Table.bonusCoins],
        )
      }
  }
    ?: UserCurrentCoinsBalance(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO)

  suspend fun updateEm2AdditionalOfferCurrentCoinsBalance(userId: String, earnedCoins: BigDecimal, trackVisibleCoins: Boolean) = dbQuery {
    UserCoinGoalCurrentCoinsBalanceEm2Table
      .update({ UserCoinGoalCurrentCoinsBalanceEm2Table.userId eq userId }) {
        it.update(offerCoins, offerCoins + earnedCoins)
        it.update(goalCoins, goalCoins + earnedCoins)
      }

    if (trackVisibleCoins) {
      UserVisibleCoinsTable.update({ UserVisibleCoinsTable.userId eq userId }) {
        it.update(coins, coins + earnedCoins)
      }
    }
  }

  suspend fun updateAdditionalOfferCurrentCoinsBalance(
    userId: String,
    earnedCoins: Int,
    nonInflatedEarnedCoins: Int,
    trackVisibleCoins: Boolean
  ) = dbQuery {
    UserCoinGoalCurrentCoinsBalanceTable
      .update({ UserCoinGoalCurrentCoinsBalanceTable.userId eq userId }) {
        it.update(offerCoins, offerCoins + earnedCoins)
        it.update(goalCoins, goalCoins + nonInflatedEarnedCoins)
      }

    if (trackVisibleCoins) {
      UserVisibleCoinsTable.update({ UserVisibleCoinsTable.userId eq userId }) {
        it.update(coins, coins + nonInflatedEarnedCoins.toBigDecimal())
      }
    }
  }

  suspend fun updateEm2CurrentBonusBalance(userId: String, earnedCoins: BigDecimal, trackVisibleCoins: Boolean) = dbQuery {
    UserCoinGoalCurrentCoinsBalanceEm2Table
      .update({ UserCoinGoalCurrentCoinsBalanceEm2Table.userId eq userId }) {
        it.update(bonusCoins, bonusCoins + earnedCoins)
        it.update(goalCoins, goalCoins + earnedCoins)
      }

    if (trackVisibleCoins) {
      UserVisibleCoinsTable.update({ UserVisibleCoinsTable.userId eq userId }) {
        it.update(coins, coins + earnedCoins)
      }
    }
  }

  suspend fun updateCurrentBonusBalance(userId: String, coinsAmount: BigDecimal, trackVisibleCoins: Boolean) = dbQuery {
    UserCoinGoalCurrentCoinsBalanceTable
      .update({ UserCoinGoalCurrentCoinsBalanceTable.userId eq userId }) {
        it.update(bonusCoins, bonusCoins + coinsAmount.toInt())
        it.update(goalCoins, goalCoins + coinsAmount.toInt())
      }

    if (trackVisibleCoins) {
      UserVisibleCoinsTable.update({ UserVisibleCoinsTable.userId eq userId }) {
        it.update(coins, coins + coinsAmount)
      }
    }
  }

  suspend fun updateCurrentGamesCoinsBalance(userId: String, coinsEarned: Int, trackVisibleCoins: Boolean) = dbQuery {
    UserCoinGoalCurrentCoinsBalanceTable
      .update({ UserCoinGoalCurrentCoinsBalanceTable.userId eq userId }) {
        with(SqlExpressionBuilder) {
          it.update(coins, coins + coinsEarned)
          it.update(goalCoins, goalCoins + coinsEarned)
        }
      }

    if (trackVisibleCoins) {
      UserVisibleCoinsTable.update({ UserVisibleCoinsTable.userId eq userId }) {
        it.update(coins, coins + coinsEarned.toBigDecimal())
      }
    }
  }

  suspend fun updateEm2CurrentGamesCoinsBalance(userId: String, coinsEarned: BigDecimal, trackVisibleCoins: Boolean) = dbQuery {
    UserCoinGoalCurrentCoinsBalanceEm2Table
      .update({ UserCoinGoalCurrentCoinsBalanceEm2Table.userId eq userId }) {
        with(SqlExpressionBuilder) {
          it.update(gameCoins, gameCoins + coinsEarned)
          it.update(goalCoins, goalCoins + coinsEarned)
        }
      }

    if (trackVisibleCoins) {
      UserVisibleCoinsTable.update({ UserVisibleCoinsTable.userId eq userId }) {
        it.update(coins, coins + coinsEarned)
      }
    }
  }

  suspend fun addCoinsToCurrentBalance(userId: String, coins: BigDecimal) = dbQuery {
    UserCoinGoalCurrentCoinsBalanceEm2Table
      .update({ UserCoinGoalCurrentCoinsBalanceEm2Table.userId eq userId }) {
        with(SqlExpressionBuilder) {
          it.update(gameCoins, gameCoins + coins) // not very cool. https://app.asana.com/0/1155692811605665/1209727124312605/f
          it.update(goalCoins, goalCoins + coins)
        }
      }
  }
}

data class UserCurrentCoinsBalanceEm1(
  val gameCoins: Int,
  val offerCoins: Int,
  val bonusCoins: Int,
  val goalCoins: Int?,
)

data class UserCurrentCoinsBalanceEm2(
  val gameCoins: BigDecimal,
  val offerCoins: BigDecimal,
  val bonusCoins: BigDecimal,
  val goalCoins: BigDecimal?,
)