package com.moregames.playtime.carousel

enum class UserCarouselTaskState {
  NEW,
  IN_PROGRESS, // transitioned here when user press "Play" in UI
  UNCLAIMED, // transitioned here when user completes the task (reaches 100%)
  CLAIMED, // transitioned here when user claims the reward. reward claim also triggers cooldown.
  COMPLETED, // transitioned here cooldown exhausts
}

enum class TokenState {
  UNREWARDED, REWARDED
}