package com.moregames.playtime.carousel

import com.moregames.base.abtesting.AbTestingExperimentHook
import javax.inject.Inject

class CarouselExperimentHook @Inject constructor(
  private val carouselService: CarouselService,
) : AbTestingExperimentHook {
  override suspend fun onNonDefaultVariationAssigned(userId: String, variationKey: String) {
    carouselService.generateInitialTasksList(userId)
  }
}