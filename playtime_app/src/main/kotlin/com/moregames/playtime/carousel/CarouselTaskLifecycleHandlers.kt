package com.moregames.playtime.carousel

import com.justplayapps.playtime.carousel.firstTaskCompletedEvent
import com.moregames.base.bus.Message
import com.moregames.base.bus.MessageBus
import com.moregames.base.bus.MessageHandler
import java.util.*
import javax.inject.Inject

class CarouselTaskLifecycleHandlers @Inject constructor(
  private val carouselService: CarouselService,
  private val messageBus: MessageBus,
) {
  @MessageHandler
  suspend fun handleCarouselTaskFinishedEvent(event: CarouselTaskFinishedEvent) {
    val count = carouselService.countUnclaimedTasks(event.userId)
    if (count == 1L) {
      messageBus.publish(firstTaskCompletedEvent {
        taskId = event.taskId.toString()
      })
    }
  }
}

data class CarouselTaskFinishedEvent(val taskId: UUID, val userId: String) : Message