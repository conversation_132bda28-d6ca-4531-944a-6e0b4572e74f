package com.moregames.playtime.user.interview

import com.google.inject.Inject
import com.google.inject.Singleton
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.BaseVariation
import com.moregames.base.abtesting.ClientExperiment.IOS_USERS_INTERVIEW
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.Variations
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.messaging.DelayedMessagePublisher
import com.moregames.base.messaging.dto.CheckAndInviteToInterviewTaskDto
import com.moregames.playtime.app.iosUserInterviewDisabled
import com.moregames.playtime.app.iosUserSurveyDisabled
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.buseffects.SendInvitedToInterviewEventEffect
import com.moregames.playtime.ios.dto.user.IosInterviewApiDto
import com.moregames.playtime.ios.dto.user.IosInterviewDecisionApiDto.Decision
import com.moregames.playtime.ios.dto.user.IosInterviewPopupApiDto
import com.moregames.playtime.notifications.PushNotification.IosPushNotification.ShareYourExperienceNotification
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutPersistenceService
import com.moregames.playtime.user.cashout.dto.CashoutStatus
import com.moregames.playtime.user.fraudscore.HighlyTrustedUsersService
import java.math.BigDecimal
import kotlin.time.Duration.Companion.days

private const val CALENDLY_URL = "https://calendly.com/userfeedback-justplayapps/user-interview"
private const val SURVEY_URL = "https://forms.gle/eojg7QmWcmJC6d9v9"

@Singleton
class UserInterviewService @Inject constructor(
  private val abTestingService: AbTestingService,
  private val interviewPersistenceService: UserInterviewPersistenceService,
  private val cashoutPersistenceService: CashoutPersistenceService,
  private val highlyTrustedUsersService: HighlyTrustedUsersService,
  private val delayedMessagePublisher: DelayedMessagePublisher,
  private val userService: UserService,
  private val messageBus: MessageBus,
  private val featureFlagsFacade: FeatureFlagsFacade,
) {

  companion object {
    private val INTERVIEW_THRESHOLD = BigDecimal.ONE
    private val DAYS_TO_CHECK = 2.days
  }

  suspend fun onCashoutStatusGetCall(userId: String, appPlatform: AppPlatform, cashoutStatus: CashoutStatus) {
    val interviewVariation = abTestingService.assignedVariationValue(userId, IOS_USERS_INTERVIEW)
    val shouldInviteToInterview = appPlatform == AppPlatform.IOS &&
      interviewVariation != DEFAULT &&
      cashoutStatus.isEnabled &&
      cashoutStatus.amountUsd >= INTERVIEW_THRESHOLD &&
      interviewPersistenceService.trackUserForInterview(userId) &&
      interviewFeatureFlagIsEnabled(userId, interviewVariation) &&
      !cashoutPersistenceService.userHasSuccessfulCashout(userId) &&
      highlyTrustedUsersService.isHighlyTrustedUser(userId) &&
      userService.isUserUnique(userId)
    if (shouldInviteToInterview) {
      delayedMessagePublisher.publish(CheckAndInviteToInterviewTaskDto(userId, appPlatform), DAYS_TO_CHECK)
    }
  }

  suspend fun checkAndInviteToInterview(userId: String, appPlatform: AppPlatform) {
    val interviewVariation = abTestingService.assignedVariationValue(userId, IOS_USERS_INTERVIEW)
    val invitedToInterview =
      appPlatform == AppPlatform.IOS &&
        interviewVariation != DEFAULT &&
        userService.userExists(userId) &&
        interviewFeatureFlagIsEnabled(userId, interviewVariation) &&
        !cashoutPersistenceService.userHasSuccessfulCashout(userId) &&
        highlyTrustedUsersService.isHighlyTrustedUser(userId) &&
        interviewPersistenceService.updateInterviewStatus(userId, UserInterviewStatus.INVITED)
    if (invitedToInterview) {
      messageBus.publishAsync(PushNotificationEffect(ShareYourExperienceNotification(userId)))
      messageBus.publishAsync(SendInvitedToInterviewEventEffect(userId, appPlatform))
    }
  }

  private suspend fun interviewFeatureFlagIsEnabled(userId: String, interviewVariation: BaseVariation): Boolean {
    val disabled = when (interviewVariation) {
      Variations.INVITED_TO_INTERVIEW -> featureFlagsFacade.iosUserInterviewDisabled()
      Variations.INVITED_TO_SURVEY -> featureFlagsFacade.iosUserSurveyDisabled()
      else -> false
    }
    if (disabled) {
      interviewPersistenceService.updateInterviewStatus(userId, UserInterviewStatus.FF_SKIPPED)
    }
    return !disabled
  }

  suspend fun getInterviewInvitationInfo(userId: String): IosInterviewApiDto? {
    if (!interviewPersistenceService.isUserInvitedToInterview(userId)) return null

    return when (abTestingService.assignedVariationValue(userId, IOS_USERS_INTERVIEW)) {
      Variations.INVITED_TO_INTERVIEW -> IosInterviewApiDto(
        url = CALENDLY_URL,
        popup = IosInterviewPopupApiDto(
          title = "Interested in sharing your gaming experience?",
          description = """Click “Yes” to schedule 30 minutes interview to talk about JustPlay games and as a gratitude for your time, JustPlay would be happy to offer you a <b>$50</b> Amazon gift card!"""
        )
      )

      Variations.INVITED_TO_SURVEY -> IosInterviewApiDto(
        url = SURVEY_URL,
        popup = IosInterviewPopupApiDto(
          title = "Interested in sharing your gaming experience?",
          description = """As one of JustPlay's top gamers, click "yes" to fill out a questionnaire and help us improve!"""
        )
      )

      else -> null
    }
  }

  suspend fun saveInterviewDecision(userId: String, decision: Decision) {
    val userInterviewStatus = when (decision) {
      Decision.AGREED -> UserInterviewStatus.AGREED
      Decision.DECLINED -> UserInterviewStatus.DECLINED
    }
    interviewPersistenceService.updateInterviewStatus(userId, userInterviewStatus)
  }
}