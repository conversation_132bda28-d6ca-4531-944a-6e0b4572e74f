package com.moregames.playtime.user.inactivityreminder

import com.google.inject.Inject
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.encryption.EncryptionService
import com.moregames.base.mail.MailService
import com.moregames.base.mail.MailService.Companion.JUST_PLAY_EMAIL_NAME
import com.moregames.base.mail.MailService.Companion.JUST_PLAY_NO_REPLY_EMAIL_ADDRESS
import com.moregames.base.util.TimeService
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.notifications.NotificationType
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.InactivityReminder
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutService
import java.time.Instant
import java.time.temporal.ChronoUnit
import javax.inject.<PERSON>ton

@Singleton
class ReminderNotificationService @Inject constructor(
  private val cashoutService: CashoutService,
  private val mailService: MailService,
  private val timeService: TimeService,
  private val inactivityNotificationService: InactivityNotificationService,
  private val userService: UserService,
  private val encryptionService: EncryptionService,
  private val messageBus: MessageBus,
) {
  companion object {
    const val DO_NOT_CHECK_PERSONALS_AFTER_DAYS = 7L
    const val USER_NAME_PLACEHOLDER = "{Name}"
  }

  // return actually used config with updated email and template list rotated
  suspend fun sendEmailReminderReturnConfig(
    userId: String,
    userLatestActivity: Instant,
    emailNotificationConfig: UserPeriodicPushReminderDto.EmailNotificationConfig,
  ): UserPeriodicPushReminderDto.EmailNotificationConfig {

    if (emailNotificationConfig.stringMailTemplates.isEmpty()) return emailNotificationConfig
    if (emailNotificationConfig.appPlatform == null) return emailNotificationConfig
    //TODO if venmo user handle experiment successful we need more complex handling mechanism
    if (emailNotificationConfig.userPersonals?.email.isNullOrEmpty()) return emailNotificationConfig

    val actualUserPersonals =
      if (userLatestActivity.plus(DO_NOT_CHECK_PERSONALS_AFTER_DAYS, ChronoUnit.DAYS).isBefore(timeService.now())) {
        emailNotificationConfig.userPersonals
      } else {
        cashoutService.getLastUserPersonals(userId)
      }

    if (actualUserPersonals == null || actualUserPersonals.email.isEmpty()) return emailNotificationConfig
    // else

    val mailTemplate = emailNotificationConfig.stringMailTemplates.first()

    val email = encryptionService.decryptOrEmpty(actualUserPersonals.email)
    val fullName = encryptionService.decryptOrEmpty(actualUserPersonals.userFullName)
    mailService.sendTemplateMail(
      senderMail = JUST_PLAY_NO_REPLY_EMAIL_ADDRESS,
      senderName = JUST_PLAY_EMAIL_NAME,
      receiverMail = email,
      receiverName = fullName,
      templateId = mailTemplate,
      substitutions = mapOf("name" to fullName.split("\\W+".toRegex())[0]),
      appPlatform = emailNotificationConfig.appPlatform
    )

    userService.trackNotification(userId, NotificationType.INACTIVITY_BY_EMAIL)

    return UserPeriodicPushReminderDto.EmailNotificationConfig(
      userPersonals = actualUserPersonals,
      stringMailTemplates = emailNotificationConfig.stringMailTemplates.drop(1) + mailTemplate,
      appPlatform = emailNotificationConfig.appPlatform,
    )
  }

  // returns list of messages to be used in the next notification (rotated)
  suspend fun sendPushReminderReturnMessages(userId: String, messageIds: List<Int>): List<Int> {
    if (messageIds.isEmpty()) return messageIds
    if (userService.getUser(userId).appPlatform != ANDROID) return messageIds

    val userFullName = cashoutService.getLastUserPersonals(userId)?.userFullName?.let { encryptionService.decryptOrEmpty(it) }
    val userFirstName = userFullName?.split("\\W+".toRegex())?.firstOrNull()

    val pushMessage = inactivityNotificationService.getMessage(messageIds.first())

    return if (pushMessage != null) {
      messageBus.publishAsync(PushNotificationEffect(InactivityReminder(userId = userId, userFirstName = userFirstName, notificationConfig = pushMessage)))
      messageIds.drop(1) + messageIds.first()
    } else {
      messageIds
    }
  }

}