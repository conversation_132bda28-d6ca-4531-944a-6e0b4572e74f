package com.moregames.playtime.user.cashout

import com.google.inject.Inject
import com.google.inject.Provider
import com.moregames.base.app.BuildVariant
import com.moregames.base.dto.CashoutPeriodsConfig
import com.moregames.base.dto.CashoutPeriodsConfig.Companion.defaultConfig
import com.moregames.base.dto.CashoutPeriodsConfig.Companion.testConfig
import com.moregames.playtime.administration.qa.QaUserSettingsService
import javax.inject.Singleton

@Singleton
class CashoutPeriodsConfigService @Inject constructor(
  val buildVariantProvider: Provider<BuildVariant>,
  val qaUserSettingsService: QaUserSettingsService,
) {

  suspend fun getCashoutPeriodConfig(userId: String): CashoutPeriodsConfig =
    when {
      buildVariantProvider.get() != BuildVariant.TEST -> defaultConfig
      qaUserSettingsService.shouldUseUsualCashoutPeriods(userId) -> defaultConfig
      else -> testConfig
    }

  suspend fun firstPeriodDuration(userId: String) = getCashoutPeriodConfig(userId).firstCashoutPeriodDuration
}