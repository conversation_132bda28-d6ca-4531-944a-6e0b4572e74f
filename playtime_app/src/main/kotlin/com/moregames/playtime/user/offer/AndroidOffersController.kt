package com.moregames.playtime.user.offer

import com.google.inject.Inject
import com.moregames.base.util.logger
import com.moregames.playtime.games.AndroidPlayedGamesService
import com.moregames.playtime.games.playstore.PlaystoreTrackingService
import com.moregames.playtime.games.viewed.ViewedGameDto
import com.moregames.playtime.games.viewed.ViewedGamesApiDto
import com.moregames.playtime.user.UserController.Companion.userId
import com.moregames.playtime.user.offer.lock.AndroidGameUnlockReminderService
import com.moregames.playtime.user.offer.lock.AndroidLockedGamesService
import com.moregames.playtime.user.offer.reminder.TrackingReminderService
import com.moregames.playtime.util.getLocale
import io.ktor.application.*
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.request.*
import io.ktor.response.*
import io.ktor.routing.*
import kotlinx.serialization.ExperimentalSerializationApi
import javax.inject.<PERSON>ton

@Singleton
@OptIn(ExperimentalSerializationApi::class)
class AndroidOffersController @Inject constructor(
  private val androidOfferListService: AndroidOfferListService,
  private val androidAdditionalOffersService: AndroidAdditionalOffersService,
  private val androidPlayedGamesService: AndroidPlayedGamesService,
  private val trackingReminderService: TrackingReminderService,
  private val playstoreTrackingService: PlaystoreTrackingService,
  private val androidGameUnlockReminderService: AndroidGameUnlockReminderService,
  private val androidLockedGamesService: AndroidLockedGamesService,
) {
  // Should be called within userId context
  fun startRouting(root: Route) {
    root.route("/offers") {
      get {
        call.respond(androidOfferListService.loadOfferListForUser(userId(), getLocale(logger())))
      }
      post("/{offerId}/completion") {
        androidAdditionalOffersService.updateAdditionalOfferBalance(userId(), call.parameters["offerId"]!!.toInt())
        call.respond(OK)
      }
      /**
       * Unused from version
       * @see com.moregames.base.util.ClientVersionsSupport.ANDROID_MARK_VIEWED_GAMES_UNUSED_APP_VERSION
       */
      post("/mark-viewed-games") {
        val request = call.receive<ViewedGamesApiDto>()
        androidPlayedGamesService.trackViewedGames(ViewedGameDto.fromApiRequest(userId(), request))
        call.respond(OK)
      }
      post("/{offerId}/dismiss") {
        val offerId = call.parameters["offerId"]!!.toInt()
        if (offerId == androidLockedGamesService.unlockedAndLockedGamesInfoConfig.unlockedGameWidgetId) {
          androidGameUnlockReminderService.dismissReminder(userId())
        } else {
          trackingReminderService.dismissReminder(userId(), offerId)
        }
        call.respond(OK)
      }
      post("/{offerId}/playstore-opened") {
        playstoreTrackingService.notifyPlaystoreOpened(userId(), call.parameters["offerId"]!!.toInt())
        call.respond(OK)
      }
    }
  }
}
