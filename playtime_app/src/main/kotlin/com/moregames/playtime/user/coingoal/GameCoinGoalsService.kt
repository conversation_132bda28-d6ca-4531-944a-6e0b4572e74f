package com.moregames.playtime.user.coingoal

import com.moregames.base.gamecoingoals.GameCoinGoalPersistenceService
import java.time.Instant
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class GameCoinGoalsService @Inject constructor(
  private val gameCoinGoalPersistenceService: GameCoinGoalPersistenceService,
) {

  suspend fun resetBalance(userId: String) {
    when (val activeGoal = findActiveCoinGoal(userId)) {
      is CoinGoal.Game -> {
        gameCoinGoalPersistenceService.resetBalance(activeGoal.goalId)
      }

      is CoinGoal.General, is CoinGoal.CoolDown, null -> {}
    }
  }

  suspend fun findActiveCoinGoal(userId: String): CoinGoal? {
    val goalSet = gameCoinGoalPersistenceService.findActiveCoinGoalSet(userId) ?: return null
    if (goalSet.nextSetTimestamp != null) {
      return CoinGoal.CoolDown(goalSet.nextSetTimestamp!!)
    }
    val activeGoal = goalSet.activeGoal() ?: return null
    return if (activeGoal.gameId != null) {
      CoinGoal.Game(goalSet.id, activeGoal.id, activeGoal.gameId!!, activeGoal.goal, activeGoal.goalBalance!!, activeGoal.notifiedOnCompletion)
    } else {
      CoinGoal.General(goalSet.id, activeGoal.id, activeGoal.goal, activeGoal.notifiedOnCompletion)
    }
  }

  suspend fun markAsNotified(goalId: String) {
    gameCoinGoalPersistenceService.markAsNotified(goalId)
  }

  suspend fun completeSet(id: String, nextSetTimestamp: Instant) {
    gameCoinGoalPersistenceService.completeSet(id, nextSetTimestamp)
  }

  suspend fun markAsClaimed(goalId: String) {
    gameCoinGoalPersistenceService.markAsClaimed(goalId)
  }
}