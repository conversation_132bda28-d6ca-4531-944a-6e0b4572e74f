package com.moregames.playtime.user.challenge.dto

import com.moregames.playtime.user.challenge.api.admin.ChallengeAdminApiDto
import com.moregames.playtime.user.challenge.progress.ObjectiveProgressCalculatorType

data class Challenge(
  val id: ChallengeId,
  val eventId: ChallengeEventId,
  val title: String,
  val icon: String,
  val progressMax: Int,
  val gameId: Int,
  val calculator: ObjectiveProgressCalculatorType,
  val order: Int,
  val goal: Int?,
  val applyEarningsCut: Boolean,
  val challengeType: ChallengeType,
) {
  fun toChallengeAdminApiDto(applicationId: String) = ChallengeAdminApiDto(
    id = id.value,
    title = title,
    icon = icon,
    progressMax = progressMax,
    gameApplicationId = applicationId,
    calculator = calculator,
    order = order,
    goal = goal,
    applyEarningsCut = applyEarningsCut,
    challengeType = challengeType,
  )
}