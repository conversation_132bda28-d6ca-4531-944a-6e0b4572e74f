package com.moregames.playtime.user.challenge

import com.google.inject.Inject
import com.google.inject.Singleton
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.exceptions.BaseException.Companion.DEFAULT_EXTERNAL_MESSAGE
import com.moregames.base.util.TimeService
import com.moregames.base.util.logger
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.SpecialChallengeClaimedNotification
import com.moregames.playtime.translations.TranslationResource
import com.moregames.playtime.translations.TranslationService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.challenge.common.ChallengeService
import com.moregames.playtime.user.challenge.dto.ChallengeState
import com.moregames.playtime.user.challenge.dto.ChallengeType
import com.moregames.playtime.user.challenge.dto.UserChallenge
import com.moregames.playtime.user.challenge.dto.bq.UserChallengeUpdatedBqDto
import com.moregames.playtime.user.challenge.dto.claim.challenge.ClaimChallengeRequestApiDto
import com.moregames.playtime.user.challenge.dto.claim.challenge.ClaimChallengeResponseApiDto
import java.util.*

@Singleton
class ClaimChallengeService @Inject constructor(
  private val challengeRewardingService: ChallengeRewardingService,
  private val translationService: TranslationService,
  private val challengeService: ChallengeService,
  private val bigQueryEventPublisher: BigQueryEventPublisher,
  private val timeService: TimeService,
  private val messageBus: MessageBus,
  private val userService: UserService,
) {

  suspend fun claimChallenge(userId: String, request: ClaimChallengeRequestApiDto, locale: Locale): ClaimChallengeResponseApiDto {

    val userChallenge = challengeService.getUserChallenge(request.challengeId, userId)
    if (userChallenge == null || !userChallenge.state.isFinal()) {
      return ClaimChallengeResponseApiDto(errorMessage = DEFAULT_EXTERNAL_MESSAGE)
    }
    val claimedText = when(userChallenge.challenge.challengeType) {
      ChallengeType.REGULAR -> translationService.translateOrDefault(TranslationResource.CHALLENGE_CLAIM_TEXT, locale)
      ChallengeType.SPECIAL -> translationService.translateOrDefault(TranslationResource.SPECIAL_CHALLENGE_KEY_COLLECTED_NOTIFICATION_TITLE, locale)
    }
    if (userChallenge.state == ChallengeState.CLAIMED) {
      logger().warn("Duplicate claim of challenge ${request.challengeId.value} for user $userId")
      return ClaimChallengeResponseApiDto(
        coins = userChallenge.coins,
        text = claimedText,
      )
    }
    val coins = challengeRewardingService.calculateChallengeReward(request.challengeId, userId)
    val claimed = challengeService.claimChallenge(request.challengeId, userId, coins)
    if (claimed) {
      sendClaimedChallengeToBq(userChallenge)
      sendSpecialChallengeClaimedNotification(userChallenge)
      return ClaimChallengeResponseApiDto(coins = coins, text = claimedText)
    } else {
      logger().warn("Parallel claiming of challenge ${request.challengeId.value} for user $userId")
    }
    val updatedChallenge = challengeService.getUserChallenge(request.challengeId, userId)
    if (updatedChallenge == null || updatedChallenge.state != ChallengeState.CLAIMED) {
      logger().warn("Invalid state of challenge ${request.challengeId} for user $userId")
      return ClaimChallengeResponseApiDto(
        coins = 0,
        errorMessage = DEFAULT_EXTERNAL_MESSAGE
      )
    }
    return ClaimChallengeResponseApiDto(coins = updatedChallenge.coins, text = claimedText)
  }

  private suspend fun sendSpecialChallengeClaimedNotification(userChallenge: UserChallenge) {
    if (userChallenge.challenge.challengeType == ChallengeType.REGULAR) return
    val userId = userChallenge.userId
    val user = userService.getUser(userChallenge.userId)
    val locale = user.locale
    messageBus.publishAsync(PushNotificationEffect(SpecialChallengeClaimedNotification(userId, locale)))
  }

  private suspend fun sendClaimedChallengeToBq(userChallenge: UserChallenge) {
    bigQueryEventPublisher.publish(UserChallengeUpdatedBqDto(
      userId = userChallenge.userId,
      challengeId = userChallenge.challenge.id,
      challengeEventId = userChallenge.challenge.eventId,
      state = ChallengeState.CLAIMED,
      gameId = userChallenge.challenge.gameId,
      completedAt = userChallenge.completedAt,
      createdAt = timeService.now(),
    ))
  }
}