package com.moregames.playtime.user.offer.combine.experiment

import com.google.inject.Inject
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.Variations
import com.moregames.base.util.ApplicationId
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.user.offer.OfferItemGameApiDto
import javax.inject.Singleton

@Singleton
class BadgeService @Inject constructor(
  private val abTestingService: AbTestingService,
  private val imageService: ImageService,
) {

  companion object {
    val badgesMap = mapOf(
      0 to "most_coins_badge.png",
      1 to "red_big_bucks_badge.png",
      2 to "red_big_payouts_badge.png",
      3 to "red_bonus_king_badge.png",
      4 to "red_cash_blast_badge.png",
      5 to "red_cash_craze_badge.png",
      6 to "red_dollar_dash_badge.png",
      7 to "red_fan_favorite_badge.png",
      8 to "red_money_boost_badge.png",
      9 to "red_players_choice_badge.png",
    )

    private val appBadges = mapOf(
      ApplicationId.TILE_MATCH_PRO_APP_ID to "00_tile-match-pro.png",
      ApplicationId.TANGRAM_APP_ID to "01_tangram-heaven.png",
      ApplicationId.SOLITAIRE_VERSE_APP_ID to "02_solitaire.png",
      ApplicationId.WOODEN_PUZZLE_APP_ID to "03_zen-tiles.png",
      ApplicationId.WATER_SORTER_APP_ID to "04_water-sorter.png",
      ApplicationId.SPACE_CONNECT_APP_ID to "05_space-connect.png",
      ApplicationId.WORD_SEEKER_APP_ID to "06_word-seeker.png",
      ApplicationId.SUDOKU_APP_ID to "07_sudoku.png",
      ApplicationId.TREASURE_MASTER_APP_ID to "08_treasure-master.png",
      ApplicationId.MERGE_BLAST_APP_ID to "09_merge-blast.png",
      ApplicationId.MIX_BLOX_APP_ID to "10_mix-blox.png",
      ApplicationId.BALL_BOUNCE_APP_ID to "11_ball-bounce.png",
      ApplicationId.BRICK_DOKU_APP_ID to "12_brickodu.png",
      ApplicationId.MAD_SMASH_APP_ID to "13_mad-smash.png",
      ApplicationId.IDLE_MERGE_FUN_APP_ID to "14_idle-merge-fun.png",
      ApplicationId.EMOJICLICKERS_APP_ID to "15_emoji-clickers.png",
      ApplicationId.SPIRAL_DROP_APP_ID to "16_helix-dash.png",
      ApplicationId.WORD_KITCHEN_APP_ID to "17_word-kitchen.png",
      ApplicationId.HEXA_PUZZLE_FUN_APP_ID to "18_puzzle-fun.png",
      ApplicationId.DICE_LOGIC_APP_ID to "19_make-ten.png",
      ApplicationId.PUZZLE_POP_BLASTER_APP_ID to "20_puzzle-pop.png",
      ApplicationId.BUBBLE_POP_APP_ID to "21_Bubble-Pop.png",
      ApplicationId.BLOCK_HOLE_CLASH_APP_ID to "22_block-hole-clash.png",
      ApplicationId.SUGAR_RUSH_APP_ID to "23_sugar-rush.png",
      ApplicationId.HEX_MATCH_APP_ID to "24_hex-match.png",
      ApplicationId.CARS_MERGE_APP_ID to "25_cars-merger.png",
      ApplicationId.COLOR_LOGIC_APP_ID to "26_color-logic.png",
      ApplicationId.BLOCK_SLIDER_APP_ID to "27_block-slider.png",
      ApplicationId.TRIVIA_MADNESS_APP_ID to "28_trivia-madness.png",
      ApplicationId.MARBLE_MADNESS_APP_ID to "29_marble.png",
      ApplicationId.CRYSTAL_CRUSH_APP_ID to "30_crystal-crash.png",
      // blockbuster is experimental and replaces TM. be attentive on offboarding - better to give BB its own picture
      ApplicationId.BLOCKBUSTER_APP_ID to "08_treasure-master.png",
    )

    private val variationDir = mapOf(
      Variations.DAILY_V1_BADGE_ANDROID to "daily_v1",
      Variations.WEEKLY_V1_BADGE_ANDROID to "weekly_v1",
      Variations.DAILY_V2_BADGE_ANDROID to "daily_v2",
      Variations.TEST_BADGE_ANDROID to "test",
    )

  }

  suspend fun applyBestCoinsBadgeExperiment(userId: String, offer: OfferItemGameApiDto, gameIndex: Int): OfferItemGameApiDto {
    val bestCoinBadgeVariation = abTestingService.assignedVariationValue(userId, ClientExperiment.BEST_COINS_BADGE)
    val blockBusterVariation = abTestingService.assignedVariationValue(userId, ClientExperiment.ANDROID_GAMES_ORDER)
    val badgeOffer =
      if (blockBusterVariation == Variations.ANDROID_BLOCK_BUSTER_1ST_ORDER) ApplicationId.BLOCKBUSTER_APP_ID
      else ApplicationId.TREASURE_MASTER_APP_ID

    return when {
      bestCoinBadgeVariation == Variations.SHOW_BADGE_ANDROID && (offer.applicationId == badgeOffer) ->
        offer.copy(showBadge = true, badgeUrl = imageService.toUrl(badgesMap[0]!!))

      bestCoinBadgeVariation == Variations.SHOW_FIRST_GAME_BADGE_ANDROID && gameIndex == 0 ->
        offer.copy(showBadge = true, badgeUrl = imageService.toUrl(badgesMap[0]!!))

      bestCoinBadgeVariation == Variations.SHOW_ALL_GAMES_BADGE_ANDROID && gameIndex < badgesMap.size ->
        offer.copy(showBadge = true, badgeUrl = imageService.toUrl(badgesMap[gameIndex]!!))

      bestCoinBadgeVariation in
        listOf(Variations.DAILY_V1_BADGE_ANDROID, Variations.WEEKLY_V1_BADGE_ANDROID, Variations.DAILY_V2_BADGE_ANDROID, Variations.TEST_BADGE_ANDROID) -> {
        val badge = appBadges[offer.applicationId]
        val bucket = variationDir[bestCoinBadgeVariation]

        if (badge != null && bucket != null)
          offer.copy(showBadge = true, badgeUrl = imageService.toUrl("badge/$bucket/$badge"))
        else offer
      }

      //PLAT-2406: DEFAULT variation. return offer without changes
      else -> offer
    }
  }
}