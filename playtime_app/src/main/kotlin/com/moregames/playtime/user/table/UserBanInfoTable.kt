package com.moregames.playtime.user.table

import org.jetbrains.exposed.sql.Table

/**
 * Detailed information about user ban reason
 *
 * - user_id - [User identifier][com.moregames.base.table.UserTable.id]
 * - reason - Short description of ban reason
 * - description - Detailed description of ban reason
 */
object UserBanInfoTable : Table("playtime.user_ban_info") {
  val userId = varchar("user_id", 36)
  val reason = varchar("reason", 100)
  val description = varchar("description", 200)
}