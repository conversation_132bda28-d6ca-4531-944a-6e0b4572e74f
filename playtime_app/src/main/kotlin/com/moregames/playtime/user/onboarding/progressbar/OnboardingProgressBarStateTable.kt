package com.moregames.playtime.user.onboarding.progressbar

import org.jetbrains.exposed.dao.id.IntIdTable

object OnboardingProgressBarStateTable : IntIdTable("playtime.onboarding_progress_bar_state") {
  val userId = varchar("user_id", 36)
  val text = varchar("text", 300)
  val type = enumerationByName("type", 100, OnboardingProgressBarStepType::class)
  val status = enumerationByName("status", 100, OnboardingProgressBarStepStatus::class)
  val order = integer("order")
}