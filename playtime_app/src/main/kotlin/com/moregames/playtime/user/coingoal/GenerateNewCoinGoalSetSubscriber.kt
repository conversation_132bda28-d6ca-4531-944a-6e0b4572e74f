package com.moregames.playtime.user.coingoal

import com.moregames.base.abtesting.AbGameCoinGoalsService
import com.moregames.base.abtesting.AbTestingService.IsExperimentParticipant
import com.moregames.base.messaging.dto.MessageDto
import com.moregames.base.messaging.push.GenericPushSubscriber
import com.moregames.base.util.alert
import com.moregames.base.util.logger
import com.moregames.playtime.user.UserService
import kotlinx.serialization.Serializable
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class GenerateNewCoinGoalSetSubscriber @Inject constructor(
  private val generator: GameCoinGoalSetGenerator,
  private val userService: UserService,
  private val abGameCoinGoalsService: AbGameCoinGoalsService,
) : GenericPushSubscriber<GenerateNewSetCommand>(GenerateNewSetCommand::class) {

  override suspend fun handle(message: GenerateNewSetCommand) = with(message) {
    if (!userService.userExists(userId)) return@with

    val user = userService.getUser(userId)

    val variation = when (val participant = abGameCoinGoalsService.isGameCoinGoalsParticipant(user.id, user.appPlatform)) {
      is IsExperimentParticipant.Yes -> participant.variation
      IsExperimentParticipant.No -> {
        logger().alert("Received GenerateNewSetCommand for user ${user.id} who is not a participant of the experiment")
        return@with
      }
    }
    generator.generateNewSet(user, variation)
  }

  override val url: String = TOPIC_NAME
}

@Serializable
data class GenerateNewSetCommand(
  val userId: String,
) : MessageDto {
  override fun defaultPubsubTopicName(): String = TOPIC_NAME
  override fun defaultCloudTaskQueueName(): String = QUEUE_NAME
}

private const val TOPIC_NAME = "generate-new-coin-goal-set"
private const val QUEUE_NAME = "generate-new-coin-goal-set-tf"