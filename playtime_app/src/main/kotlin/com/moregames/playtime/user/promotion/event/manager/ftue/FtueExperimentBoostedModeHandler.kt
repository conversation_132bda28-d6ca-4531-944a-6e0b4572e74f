package com.moregames.playtime.user.promotion.event.manager.ftue

import com.google.inject.Inject
import com.moregames.base.bus.EffectHandler
import com.moregames.playtime.boost.BoostedModeService
import com.moregames.playtime.user.cashout.CashoutPeriodsService
import javax.inject.Singleton

@Singleton
class FtueExperimentBoostedModeHandler @Inject constructor(
  private val cashoutPeriodService: CashoutPeriodsService,
  private val boostedModeService: BoostedModeService,
) {

  @EffectHandler
  suspend fun handleBoostedModeEffect(effect: FtueBoostedModeEffect) {
    val userId = effect.userId
    val currentCashoutPeriod = cashoutPeriodService.getCurrentCashoutPeriod(userId)
    boostedModeService.createSessionTillCpEnd(userId, effect.boostedModePresetId, currentCashoutPeriod)
  }
}