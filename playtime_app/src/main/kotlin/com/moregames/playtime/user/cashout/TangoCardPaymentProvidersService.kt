package com.moregames.playtime.user.cashout

import com.google.inject.Inject
import com.google.inject.Provider
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.payments.TangoCardPaymentProvider.Companion.toPaymentProviders
import com.moregames.base.util.logger
import com.moregames.base.util.redis.RedisCachedBuilder
import com.tangocard.raas.RaasClient
import kotlinx.coroutines.runBlocking
import redis.clients.jedis.params.SetParams

class TangoCardPaymentProvidersService @Inject constructor(
  private val tangoCardClient: Provider<RaasClient>,
  private val cashoutPersistenceService: CashoutPersistenceService,
  private val applicationConfig: ApplicationConfig,
  redisCachedBuilder: RedisCachedBuilder,
) {

  companion object {
    const val TANGO_CARD_CATALOG_CACHE_SEC = 24L * 60L * 60L
  }

  private val cache = redisCachedBuilder.build(
    keyPrefix = "tangoCardCatalog:",
    setParams = SetParams().ex(TANGO_CARD_CATALOG_CACHE_SEC)
  ) { _ ->
    update()
    "dummy"
  }

  suspend fun init() {
    cache.get("${applicationConfig.justplayMarket}:${applicationConfig.gaeServiceVersion}")
  }

  private fun update() {
    logger().info("Getting Tango Card payment providers.")
    val catalogModel = try {
      tangoCardClient.get().catalog.catalog
    } catch (e: Exception) {
      // providing e causes error during build phase
      //https://softbakedapps.slack.com/archives/C01MTD2KFM5/p1651489998960139
      logger().error("Can't connect to Tango Card service: ${e.message}")
      return
    }

    catalogModel.brands.toPaymentProviders()
      .onEach { (providerTypeAndCountryCode, tangoProvider) ->
        runBlocking {
          cashoutPersistenceService.updatePaymentProviders(
            paymentProviderType = tangoProvider.paymentProviderType,
            countryCode = providerTypeAndCountryCode.countryCode,
            minimumAmount = tangoProvider.minimumAmount,
            maximumAmount = tangoProvider.maximumAmount,
            disclaimer = tangoProvider.disclaimer
          )
        }
      }
  }
}