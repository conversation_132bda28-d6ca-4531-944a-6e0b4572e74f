package com.moregames.playtime.user.gamescoinsbooster

import com.google.inject.Inject
import com.google.inject.Singleton
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment.IOS_GAMES_COINS_BOOSTER
import com.moregames.base.abtesting.variations.gamescoinsbooster.IosGamesCoinsBoosterVariation
import com.moregames.base.dto.AppPlatform.*
import com.moregames.playtime.user.UserService

@Singleton
class GameCoinsBoosterService @Inject constructor(
  private val userService: UserService,
  private val abTestingService: AbTestingService,
) {

  suspend fun getCoinsBoosterConfiguration(userId: String): GameCoinsBoosterConfigApiDto =
    when (userService.getUser(userId, includingDeleted = true).appPlatform) {
      ANDROID -> "30,180"
      IOS, IOS_WEB -> (abTestingService.assignedVariationValue(userId, IOS_GAMES_COINS_BOOSTER) as? IosGamesCoinsBoosterVariation)?.parameters
    }.let { parameters ->
      GameCoinsBoosterConfigApiDto(
        parameters = parameters
      )
    }

}