package com.moregames.playtime.user.unifiedid

import com.moregames.base.exceptions.BaseException
import com.moregames.base.exceptions.ErrorCode
import com.moregames.base.exceptions.ErrorType
import com.moregames.base.exceptions.PlaytimeErrorCodes

class UnifiedIdException(internalMessage: String) : BaseException(internalMessage) {
  constructor(internalMessage: String, cause: Throwable) : this(internalMessage) {
    initCause(cause)
  }

  override val errorCode: ErrorCode = PlaytimeErrorCodes.UNABLE_TO_GENERATE_UNIFIED_ID_TOKEN
  override val errorType: ErrorType = ErrorType.SERVER_ERROR
}