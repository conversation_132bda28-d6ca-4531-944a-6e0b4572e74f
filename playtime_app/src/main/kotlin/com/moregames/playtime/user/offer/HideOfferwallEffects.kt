package com.moregames.playtime.user.offer

import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.Variations.P90_FIRST_3_DAYS
import com.moregames.base.abtesting.Variations.P90_FIRST_7_DAYS
import com.moregames.base.bus.Message
import com.moregames.base.bus.MessageBus
import com.moregames.base.bus.MessageHandler
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.OnOfferwallNowAllowedNotification
import com.moregames.playtime.user.UserService
import javax.inject.Inject

class HideOfferwallEffects @Inject constructor(
  private val abTestingService: AbTestingService,
  private val userService: UserService,
  private val messageBus: MessageBus,
) {

  companion object {
    val variationsWithNotifications = setOf(P90_FIRST_3_DAYS, P90_FIRST_7_DAYS)
    private val HIGH_ENOUGH_ECPM_GROUPS = setOf(0, 1)
  }

  @MessageHandler
  suspend fun handle(message: HideOfferwallNotificationCommand) {
    val userId = message.userId
    val variation = abTestingService.assignedVariationValue(userId, ClientExperiment.ANDROID_HIDE_OFW)
    if (variation !in variationsWithNotifications)
      return

    val user = userService.getUser(userId, true)
    val ecpmGroup = userService.getUserEcpmGroup(userId)

    if (user.isDeleted || user.isBanned || ecpmGroup == null || ecpmGroup !in HIGH_ENOUGH_ECPM_GROUPS)
      return

    messageBus.publishAsync(PushNotificationEffect(OnOfferwallNowAllowedNotification(userId)))
  }
}

data class HideOfferwallNotificationCommand(val userId: String) : Message