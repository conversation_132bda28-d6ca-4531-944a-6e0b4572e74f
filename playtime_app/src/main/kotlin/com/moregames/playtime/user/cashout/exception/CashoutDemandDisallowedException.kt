package com.moregames.playtime.user.cashout.exception

import com.moregames.base.exceptions.BaseException
import com.moregames.base.exceptions.ErrorType
import com.moregames.base.exceptions.PlaytimeErrorCodes

class CashoutDemandDisallowedException(userId: String) : BaseException(
  internalMessage = "Cashout demand is not allowed for userId '$userId'"
) {
  override val errorCode = PlaytimeErrorCodes.CASHOUT_DISALLOWED
  override val errorType = ErrorType.INPUT_ERROR
}