package com.moregames.playtime.user

import com.google.inject.Inject
import com.moregames.base.util.getRequestFromBodyOrNull
import com.moregames.base.util.logger
import com.moregames.playtime.user.dto.NotificationReportApiDto
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.response.*
import io.ktor.routing.*
import javax.inject.Singleton

@Singleton
class NotificationsController @Inject constructor(
  private val notificationsPersistenceService: NotificationsPersistenceService
) {
  // Should be called within userId context
  fun startRouting(route: Route) {
    route.route("/notificationReport") {
      post {
        val notificationReport = getRequestFromBodyOrNull<NotificationReportApiDto>()
          ?: throw IllegalArgumentException("Notification report is empty")
        notificationsPersistenceService.saveNotificationReport(userId(), notificationReport)
        logger().info(notificationReport.toString())
        call.respond(HttpStatusCode.OK)
      }
    }
  }

  companion object {
    val userId: ParamExtractor = { call.parameters[UserController.USER_ID_PARAMETER]!! }
  }
}