package com.moregames.playtime.user.table

import org.jetbrains.exposed.dao.id.IntIdTable
import org.jetbrains.exposed.sql.`java-time`.timestamp
import java.time.Instant


object UserAppVersionInfoTable : IntIdTable("playtime.user_app_version_info") {
  val userId = varchar("user_id", 36)
  val appVersion = integer("app_version")
  val createdAt = timestamp("created_at").clientDefault { Instant.now() }
  val appPlatform = varchar("app_platform", 36).nullable()
}
