package com.moregames.playtime.user.cashout.dto

import com.moregames.base.app.PaymentProviderType
import com.moregames.base.messaging.dto.SignedMessage
import com.moregames.playtime.util.base64Encoded
import kotlinx.serialization.Serializable

@Serializable
data class CashoutDemandApiDto(
  val provider: PaymentProviderType,
  val name: String,
  val encryptedName: String? = null,
  val address: String? = null,
  val email: String? = null,
  val normalizedEmail: String? = null,
  val encryptedAddress: String? = null,
  val encryptedEmail: String? = null,
  val normalizedEncryptedEmail: String? = null,
  @Deprecated("Remove support after offboarding on app level")
  val isBonusRequest: Boolean,
  val userHandle: String? = null
) : SignedMessage {
  override fun getMessageForSignature(): String = "${provider.key}${name}${email?.trim()}".base64Encoded()
}
