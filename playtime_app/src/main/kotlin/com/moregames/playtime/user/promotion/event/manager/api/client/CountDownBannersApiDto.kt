package com.moregames.playtime.user.promotion.event.manager.api.client

import com.moregames.base.messaging.customnotification.ButtonAction
import kotlinx.serialization.Serializable

@Serializable
data class CountDownBannersApiDto(
  val startPosition: Int,
  val step: Int,
  val max: Int,
  val title: String,
  val backgroundImage: String,
  val infoImages: List<String>,
  val infoTitle: String,
  val infoSections: List<CountDownInfoSectionApiDto>,
  val infoButtonClickAction: ButtonAction? = null,
  val infoButtonText: String? = null,
  val endTime: Long? = null,
)
