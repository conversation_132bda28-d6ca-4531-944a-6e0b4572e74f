package com.moregames.playtime.user.verification.email.table

import org.jetbrains.exposed.sql.Table

object EmailVerificationResultTable : Table("playtime.email_verification_result") {
  val userId = varchar("user_id", 36)
  val score = decimal("score", 6, 2)
  val encryptedEmail = varchar("encrypted_email", 750).nullable()
  val emailHash = varchar("email_hash", 64).nullable()
  val normalizedEncryptedEmail = varchar("normalized_encrypted_email", 750).nullable()
  val normalizedEmailHash = varchar("normalized_email_hash", 64).nullable()

  override val primaryKey: PrimaryKey = PrimaryKey(userId, emailHash)
}