package com.moregames.playtime.user.objectives.progress

import com.google.inject.Inject
import com.moregames.base.bus.MessageBus
import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.messaging.push.GenericPushSubscriber
import com.moregames.playtime.carousel.HandleCarouselProgressCommand
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.challenge.progress.ChallengeProgressService
import com.moregames.playtime.user.gamerank.UserGameRankProgressService

class ObjectiveProgressSubscriber @Inject constructor(
  private val challengeProgressService: ChallengeProgressService,
  private val userService: UserService,
  private val userGameRankProgressService: UserGameRankProgressService,
  private val messageBus: MessageBus,
) : GenericPushSubscriber<UserChallengeProgressDto>(UserChallengeProgressDto::class) {

  override suspend fun handle(message: UserChallengeProgressDto) {
    if (!userService.userExists(message.userId)) return

    challengeProgressService.handleUserChallengeProgress(message)
    userGameRankProgressService.handleUserProgress(message)
    messageBus.publish(HandleCarouselProgressCommand(message))
  }

  override val url: String = "user-challenge-progress"
}