package com.moregames.playtime.checks.table

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.`java-time`.timestamp

object IpRegistryTable : Table("playtime.ip_registry_data") {
  val ip = varchar("ip", 45)
  val isAbuser = bool("is_abuser").nullable()
  val isAttacker = bool("is_attacker").nullable()
  val isBogon = bool("is_bogon").nullable()
  val isCloudProvider = bool("is_cloud_provider").nullable()
  val isProxy = bool("is_proxy").nullable()
  val isRelay = bool("is_relay").nullable()
  val isTor = bool("is_tor").nullable()
  val isTorExit = bool("is_tor_exit").nullable()
  val isVpn = bool("is_vpn").nullable()
  val isAnonymous = bool("is_anonymous").nullable()
  val isThreat = bool("is_threat").nullable()
  val regionCode = varchar("region_code", 10).nullable()
  val countryCode = varchar("country_code", 10).nullable()
  val countryName = varchar("country_name", 100).nullable()
  val regionName = varchar("region_name", 100).nullable()
  val cityName = varchar("city_name", 300).nullable()
  val timeZone = varchar("time_zone", 100).nullable()

  val updatedAt = timestamp("updated_at")

  override val primaryKey = PrimaryKey(ip)
}