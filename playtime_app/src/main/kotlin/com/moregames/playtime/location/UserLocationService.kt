package com.moregames.playtime.location

import com.google.inject.Inject
import com.google.inject.Singleton
import com.moregames.base.util.logger
import com.moregames.playtime.checks.ipregistry.IpRegistryService
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.user.dto.GdprStateDto
import kotlinx.serialization.Serializable

@Singleton
class UserLocationService @Inject constructor(
  private val marketService: MarketService,
  private val ipRegistryService: IpRegistryService,
) {

  suspend fun getGdprState(locationHeader: String?, forwardedForHeader: String?): GdprStateDto =
    GdprStateDto(marketService.isGdprAppliesToCountry(getUserCountry(locationHeader, forwardedForHeader)))

  suspend fun getAttributes(locationHeader: String?, forwardedForHeader: String?): LocationAttributesApiDto {
    val country = getUserCountry(locationHeader, forwardedForHeader)
    return LocationAttributesApiDto(
      useGdpr = marketService.isGdprAppliesToCountry(country),
      isUs = country == "US",
    )
  }

  private suspend fun getUserCountry(locationHeader: String?, forwardedForHeader: String?): String {
    val country = locationHeader?.split(",")?.take(1)?.firstOrNull()
    if (!country.isNullOrBlank()) {
      return country
    }
    val userIp = forwardedForHeader?.split(",")?.takeLast(4)?.firstOrNull()
    if (!userIp.isNullOrBlank()) {
      return ipRegistryService.findIpRegistryInfo(userIp)?.countryCode ?: "ZZ"
    }
    logger().error("Can't determine user country by headers '$locationHeader' and '$forwardedForHeader'")
    return "ZZ"
  }
}

@Serializable
data class LocationAttributesApiDto(
  val useGdpr: Boolean,
  val isUs: Boolean
)