package com.moregames.playtime.app

import com.moregames.base.secret.Secret

enum class PlaytimeSecrets(override val key: String) : Secret {

  BASIC_AUTH_ADMIN("basic-auth-admin"),
  BASIC_AUTH_ADMIN_TEST("basic-auth-admin-test"),
  BASIC_AUTH_FACETEC_ADMIN("basic-auth-facetec-admin"),
  BASIC_AUTH_QA_TEST("basic-auth-qa-test"),
  BASIC_AUTH_PLAYTIME("basic-auth-playtime"),
  BASIC_AUTH_CLIENT_MANAGEMENT("basic-auth-client-management"),
  BASIC_AUTH_CLIENT_MANAGEMENT_TEST("basic-auth-client-management-test"),

  DATABASE_USER_APP_ENGINE("database-user-app-engine"),
  DATABASE_USER_LIQUIBASE("database-user-liquibase"),

  // See: https://blog.fyber.com/new-separate-security-tokens-for-serverside-callbacks-and-sdk/
  FYBER_SECURITY_TOKEN("fyber-security-token"),
  FYBER_CLIENT_SECURITY_TOKEN("fyber-client-security-token"),

  IRON_SOURCE_PRIVATE_KEY("iron-source-private-key"),
  TAPJOY_VIRTUAL_CURRENCY_SECRET_KEY("tapjoy-virtual-currency-secret-key"),

  GOOGLE_ANALYTICS_API_SECRET("google-analytics-api-secret"),
  GOOGLE_ANALYTICS_API_SECRET_IOS("google-analytics-api-secret-ios"),

  ADJUST_S2S_AUTH("adjust-auth-token"),
  MOLOCO_S2S_AUTH("moloco-api-key"),

  @Suppress("unused") // reserved for nearest future
  ADJUST_EVENT_USER_FIRST_CASHOUT_TOKEN("adjust-event-token-user-first-cashout"),

  @Suppress("unused") // reserved for nearest future
  ADJUST_EVENT_USER_REACHED_REV_THRESHOLD_TOKEN("adjust-event-token-user-reached-rev-threshold"),

  @Suppress("unused") // reserved for nearest future
  ADJUST_EVENT_CASHOUT_SELECT_PROVIDER_SHOW_TOKEN("adjust-event-token-cashout-select-provider-show"),

  EXCHANGERATESAPI_API_KEY("exchangeratesapi-api-key"),

  IP_QUALITY_SCORE_API_KEY("ip-quality-score-api-key"),

  APK_CERTIFICATE_DIGEST_SHA256("apk-certificate-digest-sha256"),

  IP_REGISTRY_API_KEY("ip-registry-api-key"),

  ORCHESTRATOR_AUTH_TOKEN("orchestrator-auth-token"),
  ORCHESTRATOR_AUTH_TOKEN_SALT("orchestrator-auth-token-salt"),

  METABASE_USER_DASHBOARD_LINK("metabase-user-dashboard-link"),
  FACETEC_MONGODB7_DATA_API_KEY("facetec-mongodb7-data-api-key"),

  IOS_APP_TEAM_ID("ios-app-team-id"),
  SEON_API_KEY("seon-api-key"),
  AMPLITUDE_API_KEY("amplitude-api-key"),
  AMPLITUDE_API_KEY_SANDBOX("amplitude-api-key-sandbox"),

  PLAY_INTEGRITY_SERVICE_ACCOUNT_KEY("play-integrity-service-account-key"),
  GAMES_PLAY_INTEGRITY_KEY_TREASURE_MASTER("games-play-integrity-key-treasure-master"),
  ADJOE_SECRET_KEY("adjoe-secret-key"),
  UID_2_SECRET_PROD("uid-2-api-secret-prod"),
  UID_2_SECRET_TEST("uid-2-api-secret-test"),
  UID_2_API_KEY_PROD("uid-2-api-key-prod"),
  UID_2_API_KEY_TEST("uid-2-api-key-test"),
  ;
}
