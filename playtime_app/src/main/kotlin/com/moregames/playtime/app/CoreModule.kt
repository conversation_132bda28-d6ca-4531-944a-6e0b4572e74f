package com.moregames.playtime.app

import ch.veehait.devicecheck.appattest.AppleAppAttest
import ch.veehait.devicecheck.appattest.attestation.AttestationValidator
import ch.veehait.devicecheck.appattest.common.App
import ch.veehait.devicecheck.appattest.common.AppleAppAttestEnvironment.DEVELOPMENT
import ch.veehait.devicecheck.appattest.common.AppleAppAttestEnvironment.PRODUCTION
import com.google.cloud.tasks.v2.CloudTasksClient
import com.google.cloud.tasks.v2.CloudTasksSettings
import com.google.inject.Injector
import com.google.inject.Provider
import com.google.inject.Provides
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.moregames.base.abtesting.AbTestingExperimentHook
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.ExperimentBase
import com.moregames.base.app.BaseModule
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.DataSourceProvider
import com.moregames.base.app.DefaultDataSourceProvider
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.ktor.HttpClientTracing
import com.moregames.base.messaging.auth.AuthService
import com.moregames.base.messaging.auth.HttpClientAuth
import com.moregames.base.messaging.commands.CommandsClient
import com.moregames.base.payments.TangoCardCredentialsService
import com.moregames.base.secret.K8sSecretService
import com.moregames.base.secret.SecretService
import com.moregames.base.util.RandomGenerator
import com.moregames.playtime.carousel.CarouselExperimentHook
import com.moregames.playtime.ios.dto.IosBundleId.JP_BUNDLE_ID
import com.moregames.playtime.ios.dto.IosBundleId.JP_QA_BUNDLE_ID
import com.moregames.playtime.user.cashout.offers.CashoutOffersAbTestingHook
import com.moregames.playtime.user.luckyhour.LuckyHourAbTestingHook
import com.moregames.playtime.user.offer.HideOfferwallAbTestingHook
import com.moregames.playtime.user.onboarding.progressbar.OnboardingProgressBarAbTestingHook
import com.moregames.playtime.user.promotion.event.manager.ftue.FtueExperimentHook
import com.moregames.playtime.util.defaultJsonConverter
import com.uid2.client.PublisherUid2Helper
import io.ktor.application.*
import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.features.*
import io.ktor.client.features.json.*
import io.ktor.client.features.json.serializer.*
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.json.Json
import org.jetbrains.exposed.sql.Database
import redis.clients.jedis.JedisPool
import redis.clients.jedis.JedisPoolConfig
import kotlin.reflect.KClass

class CoreModule(
  application: Application,
  private val buildVariant: BuildVariant,
  dataSourceProvider: DataSourceProvider = DefaultDataSourceProvider(),
) : BaseModule(application, buildVariant, dataSourceProvider) {

  companion object {
    const val APPLOVIN_HTTP_CLIENT = "applovinHttpClient"
    const val ADJUST_HTTP_CLIENT = "adjustHttpClient"
    const val AMPLITUDE_HTTP_CLIENT = "amplitudeHttpClient"
    const val MONGODB_HTTP_CLIENT = "mongodbHttpClient"
    const val EXCHANGERATESAPI_HTTP_CLIENT = "exchangeRatesApiHttpClient"
    const val IPQS_HTTP_CLIENT = "ipQualityServiceHttpClient"
    const val COUNTRY_DETECTOR_HTTP_CLIENT = "countryDetectorHttpClient"
    const val CROSS_SERVICE_HTTP_CLIENT = "crossServiceHttpClient"
    const val MOLOCO_HTTP_CLIENT = "molocoHttpClient"
    const val UNIFIED_ID_HTTP_CLIENT = "unifiedIdHttpClient"
    const val PUBLISHER_UID_2_HELPER = "publisherUid2Helper"
    const val IOS_ATTESTATION_VALIDATOR_SANDBOX_JP = "ios_attestation_validator_sandbox_jp"
    const val IOS_ATTESTATION_VALIDATOR_SANDBOX_QA = "ios_attestation_validator_sandbox_qa"
    const val IOS_ATTESTATION_VALIDATOR_PROD_JP = "ios_attestation_validator_prod_jp"
    const val IOS_ATTESTATION_VALIDATOR_PROD_QA = "ios_attestation_validator_prod_qa"
  }

  @Provides
  @Singleton
  fun database(secretService: SecretService): Database = connectToDatabase(DbConfigs.APP_ENGINE, secretService, "new")

  override fun provideAbTestingHooks(): Map<ExperimentBase, KClass<out AbTestingExperimentHook>> {
    return mapOf(
      ClientExperiment.SPECIAL_CASHOUT_OFFERS to CashoutOffersAbTestingHook::class,
      ClientExperiment.ANDROID_ONBOARDING_PROGRESS_BAR to OnboardingProgressBarAbTestingHook::class,
      ClientExperiment.ANDROID_HIDE_OFW to HideOfferwallAbTestingHook::class,
      ClientExperiment.ANDROID_LUCKY_HOUR_2 to LuckyHourAbTestingHook::class,
      ClientExperiment.FTUE to FtueExperimentHook::class,
      ClientExperiment.ENGAGEMENT_CAROUSEL to CarouselExperimentHook::class
    )
  }

  override fun provideCloudTasksClient(): CloudTasksClient {
    val settings = CloudTasksSettings.newBuilder()
      .setTransportChannelProvider(
        CloudTasksSettings.defaultGrpcTransportProviderBuilder()
          .build()
      )
      .build()
    return CloudTasksClient.create(settings)
  }

  override fun provideSecretService(): Provider<SecretService> {
    return Provider { K8sSecretService() }
  }

  @Provides
  @Singleton
  fun jsonConverter(): Json = defaultJsonConverter

  @Provides
  @Singleton
  fun httpClient() = HttpClient(CIO) {
    install(JsonFeature) {
      val json = kotlinx.serialization.json.Json { ignoreUnknownKeys = true }
      serializer = KotlinxSerializer(json)
    }
    install(HttpTimeout) {
      requestTimeoutMillis = 15 * 1000
    }
    install(HttpClientTracing) {
      spanName = "httpClient"
    }
  }

  @Provides
  @Singleton
  @Named(APPLOVIN_HTTP_CLIENT)
  fun applovinHttpClient() = HttpClient(CIO) {
    install(JsonFeature) {
      val json = kotlinx.serialization.json.Json {
        ignoreUnknownKeys = true
      }
      serializer = KotlinxSerializer(json)
    }
    install(HttpTimeout) {
      requestTimeoutMillis = 15 * 1000
    }
    install(HttpClientTracing) {
      spanName = "applovin"
    }
    expectSuccess = false
  }

  @Provides
  @Singleton
  @Named(ADJUST_HTTP_CLIENT)
  fun adjustHttpClient() = HttpClient(CIO) {
    install(HttpTimeout) {
      requestTimeoutMillis = 5 * 1000
    }
    install(HttpClientTracing) {
      spanName = "adjust"
    }
  }

  @Provides
  @Singleton
  @Named(AMPLITUDE_HTTP_CLIENT)
  fun amplitudeHttpClient() = HttpClient(CIO) {
    install(JsonFeature) {
      val json = kotlinx.serialization.json.Json {
        ignoreUnknownKeys = true
      }
      serializer = KotlinxSerializer(json)
    }
    install(HttpTimeout) {
      requestTimeoutMillis = 15 * 1000
    }
    install(HttpClientTracing) {
      spanName = "amplitude"
    }
    expectSuccess = false
  }

  @Provides
  @Singleton
  @Named(MONGODB_HTTP_CLIENT)
  fun mongodbHttpClient() = HttpClient(CIO) {
    install(HttpTimeout) {
      requestTimeoutMillis = 60 * 1000 // 60 seconds
    }
    install(JsonFeature) {
      val json = kotlinx.serialization.json.Json {
        ignoreUnknownKeys = true
        encodeDefaults = false
      }
      serializer = KotlinxSerializer(json)
    }
    install(HttpClientTracing) {
      spanName = "mongodb"
    }
  }


  @Provides
  @Singleton
  @Named(EXCHANGERATESAPI_HTTP_CLIENT)
  fun exchangeRatesApiHttpClient(json: Json) = HttpClient(CIO) {
    install(JsonFeature) {
      serializer = KotlinxSerializer(json)
    }
    install(HttpTimeout) {
      requestTimeoutMillis = 15 * 1000
    }
    install(HttpClientTracing) {
      spanName = "exchangeRates"
    }
  }

  @Provides
  @Singleton
  @Named(IPQS_HTTP_CLIENT)
  fun ipqsHttpClient(json: Json) = HttpClient(CIO) {
    install(JsonFeature) {
      serializer = KotlinxSerializer(json)
    }
    install(HttpTimeout) {
      requestTimeoutMillis = 15 * 1000
    }
    install(HttpClientTracing) {
      spanName = "ipqs"
    }
  }

  @Provides
  @Singleton
  @Named(COUNTRY_DETECTOR_HTTP_CLIENT)
  fun countryDetectorHttpClient(json: Json) = HttpClient(CIO) {
    install(JsonFeature) {
      serializer = KotlinxSerializer(json)
    }
    install(HttpTimeout) {
      requestTimeoutMillis = 3 * 1000
    }
    install(HttpClientTracing) {
      spanName = "countryDetector"
    }
  }

  @Provides
  @Singleton
  @Named(CROSS_SERVICE_HTTP_CLIENT)
  fun crossServiceHttpClient(json: Json, authService: AuthService) = HttpClient(CIO) {
    install(JsonFeature) {
      serializer = KotlinxSerializer(json)
    }
    install(HttpTimeout) {
      requestTimeoutMillis = 15 * 1000
    }
    install(HttpClientTracing) {
      spanName = "crossService"
    }
    install(HttpClientAuth) {
      this.authService = authService
    }
  }

  @Provides
  @Singleton
  @Named(MOLOCO_HTTP_CLIENT)
  fun molocoHttpClient() = HttpClient(CIO) {
    install(JsonFeature) {
      val json = kotlinx.serialization.json.Json {
        ignoreUnknownKeys = true
      }
      serializer = KotlinxSerializer(json)
    }
    install(HttpTimeout) {
      requestTimeoutMillis = 15 * 1000
    }
    install(HttpClientTracing) {
      spanName = "moloco"
    }
  }

  @Provides
  @Singleton
  @Named(UNIFIED_ID_HTTP_CLIENT)
  fun unifiedIdHttpClient() = HttpClient(CIO) {
    install(JsonFeature) {
      val json = kotlinx.serialization.json.Json {
        ignoreUnknownKeys = true
      }
      serializer = KotlinxSerializer(json)
    }
    install(HttpTimeout) {
      requestTimeoutMillis = 15 * 1000
    }
    install(HttpClientTracing) {
      spanName = "unifiedId"
    }
  }

  @Provides
  @Singleton
  @Named(PUBLISHER_UID_2_HELPER)
  fun publisherUid2Helper(secretService: SecretService) = runBlocking {
    PublisherUid2Helper(
      if (buildVariant == BuildVariant.PRODUCTION) {
        secretService.secretValue(PlaytimeSecrets.UID_2_SECRET_PROD)
      } else {
        secretService.secretValue(PlaytimeSecrets.UID_2_SECRET_TEST)
      }
    )
  }

  @Provides
  fun tangoCredentials() =
    if (buildVariant == BuildVariant.PRODUCTION) {
      TangoCardCredentialsService.PRODUCTION
    } else {
      TangoCardCredentialsService.SANDBOX
    }

  @Provides
  fun tangoCardClient(
    secretService: SecretService,
    tangoCardCredentialsService: TangoCardCredentialsService
  ) = runBlocking {
    tangoCardCredentialsService.client(secretService)
  }

  @Provides
  @Singleton
  fun databases(injector: Injector) =
    injector.allBindings.keys.filter { Database::class.java.isAssignableFrom(it.typeLiteral.rawType) }.map { injector.getInstance(it) as Database }.toSet()

  @Provides
  @Singleton
  fun jedisPool(applicationConfig: ApplicationConfig): JedisPool {
    val jedisPoolConfig = JedisPoolConfig()
    jedisPoolConfig.maxTotal = 200
    return JedisPool(jedisPoolConfig, applicationConfig.redisHost, applicationConfig.redisPort)
  }

  @Provides
  @Singleton
  fun commandsClient(json: Json, authService: AuthService, randomGenerator: RandomGenerator): CommandsClient {
    return CommandsClient(json, authService, randomGenerator)
  }

  @Provides
  @Singleton
  @Named("api-http-client")
  fun apiHttpClient() = HttpClient(CIO) {
    install(JsonFeature) {
      val json = kotlinx.serialization.json.Json { ignoreUnknownKeys = true }
      serializer = KotlinxSerializer(json)
    }
    install(HttpTimeout) {
      requestTimeoutMillis = 15 * 1000
    }
    install(HttpClientTracing) {
      spanName = "api"
    }
  }

  @Provides
  @Named(IOS_ATTESTATION_VALIDATOR_SANDBOX_JP)
  @Singleton
  fun getAttestationValidatorSandbox(secretService: SecretService): AttestationValidator =
    AppleAppAttest(
      app = App(
        teamIdentifier = runBlocking { secretService.secretValue(PlaytimeSecrets.IOS_APP_TEAM_ID) },
        bundleIdentifier = JP_BUNDLE_ID.bundleId
      ),
      appleAppAttestEnvironment = DEVELOPMENT
    ).createAttestationValidator()

  @Provides
  @Named(IOS_ATTESTATION_VALIDATOR_PROD_JP)
  @Singleton
  fun getAttestationValidatorProd(secretService: SecretService): AttestationValidator =
    AppleAppAttest(
      app = App(
        teamIdentifier = runBlocking { secretService.secretValue(PlaytimeSecrets.IOS_APP_TEAM_ID) },
        bundleIdentifier = JP_BUNDLE_ID.bundleId
      ),
      appleAppAttestEnvironment = PRODUCTION
    ).createAttestationValidator()

  @Provides
  @Named(IOS_ATTESTATION_VALIDATOR_SANDBOX_QA)
  @Singleton
  fun getAttestationValidatorSandboxQa(secretService: SecretService): AttestationValidator =
    AppleAppAttest(
      app = App(
        teamIdentifier = runBlocking { secretService.secretValue(PlaytimeSecrets.IOS_APP_TEAM_ID) },
        bundleIdentifier = JP_QA_BUNDLE_ID.bundleId
      ),
      appleAppAttestEnvironment = DEVELOPMENT
    ).createAttestationValidator()

  @Provides
  @Named(IOS_ATTESTATION_VALIDATOR_PROD_QA)
  @Singleton
  fun getAttestationValidatorProdQa(secretService: SecretService): AttestationValidator =
    AppleAppAttest(
      app = App(
        teamIdentifier = runBlocking { secretService.secretValue(PlaytimeSecrets.IOS_APP_TEAM_ID) },
        bundleIdentifier = JP_QA_BUNDLE_ID.bundleId
      ),
      appleAppAttestEnvironment = PRODUCTION
    ).createAttestationValidator()

  private enum class DbConfigs(override val username: String, override val password: PlaytimeSecrets, override val maximumPoolSize: Int) : DbConfig {
    APP_ENGINE("app_engine", PlaytimeSecrets.DATABASE_USER_APP_ENGINE, 40)
  }
}
