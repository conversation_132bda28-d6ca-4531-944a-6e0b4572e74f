package com.moregames.playtime.app

import com.google.inject.Inject
import com.moregames.base.util.alert
import com.moregames.base.util.logger
import java.net.URI
import javax.inject.Singleton

const val IMAGES_ROOT = "https://storage.googleapis.com/public-playtime/images/"

@Singleton
class ImageService @Inject constructor() {

  companion object {
    const val STUBBY = "${IMAGES_ROOT}failsafe-nonexistent-url"
  }

  fun toUrl(filename: String): String {
    val result = IMAGES_ROOT + filename
    return try {
      URI(result).normalize().toASCIIString()
    } catch (e: Exception) {
      logger().alert("Failed to generated correct URL link from '$filename' string. Exception message: ${e.message}")
      URI(STUBBY).normalize().toASCIIString()
    }
  }
}
