package com.moregames.playtime.webhook.adjust.dto

import com.justplayapps.orchestrator.events.*
import com.moregames.base.util.fromProto
import io.ktor.util.*
import java.time.Instant

data class AdjustInstallation(
  val campaignName: String? = null,
  val trackerId: String? = null,
  val trackerName: String? = null,
  val adNetwork: String? = null,
  val googleAdId: String? = null,
  val adjustId: String? = null,
  val userId: String,
  val ip: String? = null,
  val countryCode: String? = null,
  val installedAt: Instant,
  val packageName: String? = null,
  val osVersion: String? = null,
  val device: String? = null,
  val userAgent: String? = null,
  val limitAdTracking: Boolean? = null,
  val isOrganic: Boolean? = null,
  val adgroupName: String? = null,
  val creativeName: String? = null,
  val googleStoreReferrer: String? = null,
  val idfa: String? = null,
  val idfv: String? = null,
  val appSetId: String? = null,
  val outdatedTracker: String? = null,
  val outdatedTrackerName: String? = null,
  val attributionUpdatedAt: Instant? = null,
  val activityKind: String? = null,
  val createdAt: Instant,
) {

  companion object {
    fun fromQueryParameters(userId: String, queryParameters: StringValues): AdjustInstallation =
      queryParameters.let {
        AdjustInstallation(
          campaignName = it["campaign_name"]?.ifBlank { null },
          trackerId = it["tracker"]?.ifBlank { null },
          trackerName = it["tracker_name"]?.ifBlank { null },
          adNetwork = it["network_name"]?.ifBlank { null },
          googleAdId = it["gps_adid"]?.ifBlank { null },
          userId = userId,
          adjustId = it["adid"]?.ifBlank { null },
          ip = it["ip_address"]?.ifBlank { null },
          countryCode = it["country"]?.ifBlank { null },
          installedAt = it["installed_at"]?.let { installTimeStamp -> Instant.ofEpochSecond(installTimeStamp.toLong()) } ?: Instant.now(),
          packageName = it["app_name"]?.take(200)?.ifBlank { null },
          osVersion = it["os_version"]?.take(100)?.ifBlank { null },
          device = it["device_name"]?.take(50)?.ifBlank { null },
          userAgent = it["user_agent"]?.take(200)?.ifBlank { null },
          limitAdTracking = it["tracking_limited"]?.ifBlank { null }?.let { limitAdTracking -> limitAdTracking == "1" },
          isOrganic = it["is_organic"]?.ifBlank { null }?.let { isOrganic -> isOrganic == "1" },
          adgroupName = it["adgroup_name"]?.take(100)?.ifBlank { null },
          creativeName = it["creative_name"]?.take(100)?.ifBlank { null },
          googleStoreReferrer = it["referrer"]?.take(200)?.ifBlank { null },
          idfa = it["idfa"]?.ifBlank { null },
          idfv = it["idfv"]?.ifBlank { null },
          appSetId = it["google_app_set_id"]?.ifBlank { null },
          outdatedTracker = it["outdated_tracker"]?.ifBlank { null },
          outdatedTrackerName = it["outdated_tracker_name"]?.ifBlank { null },
          attributionUpdatedAt = it["attribution_updated_at"]?.let { installTimeStamp -> Instant.ofEpochSecond(installTimeStamp.toLong()) },
          activityKind = it["activity_kind"]?.ifBlank { null },
          createdAt = it["created_at"]?.let { eventTimeStamp -> Instant.ofEpochSecond(eventTimeStamp.toLong()) } ?: Instant.now(),
        )
      }

    fun fromProto(message: OrchestratorEvents.AdjustInstallEvent): AdjustInstallation {
      return AdjustInstallation(
        campaignName = message.campaignNameOrNull?.value,
        trackerId = message.trackerIdOrNull?.value,
        trackerName = message.trackerNameOrNull?.value,
        adNetwork = message.adNetworkOrNull?.value,
        googleAdId = message.googleAdIdOrNull?.value,
        adjustId = message.adjustIdOrNull?.value,
        userId = message.userId,
        ip = message.ipOrNull?.value,
        countryCode = message.countryCodeOrNull?.value,
        installedAt = message.installedAt.fromProto(),
        packageName = message.packageNameOrNull?.value,
        osVersion = message.osVersionOrNull?.value,
        device = message.deviceOrNull?.value,
        userAgent = message.userAgentOrNull?.value,
        limitAdTracking = message.limitAdTrackingOrNull?.value,
        isOrganic = message.isOrganicOrNull?.value,
        adgroupName = message.adgroupNameOrNull?.value,
        creativeName = message.creativeNameOrNull?.value,
        googleStoreReferrer = message.googleStoreReferrerOrNull?.value,
        idfa = message.idfaOrNull?.value,
        idfv = message.idfvOrNull?.value,
        appSetId = message.appSetIdOrNull?.value,
        outdatedTracker = message.outdatedTrackerOrNull?.value,
        outdatedTrackerName = message.outdatedTrackerNameOrNull?.value,
        attributionUpdatedAt = message.attributionUpdatedAtOrNull?.fromProto(),
        activityKind = message.activityKindOrNull?.value,
        createdAt = message.createdAt.fromProto()
      )
    }
  }

  fun isOrganic() = (isOrganic == true) || adNetwork == "Google Organic Search"
}