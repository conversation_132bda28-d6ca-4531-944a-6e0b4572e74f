package com.moregames.playtime.ios.dto.user

import kotlinx.serialization.Serializable

@Serializable
data class IosUserConfigurationApiDto(
  val cashoutForm: String = "doubleEmail",
  val cashoutConfirmationMode: String = "oneClickCashout",
  val moreGamesMode: String? = "horizontalBig",
  val coinGoalSignsMode: String? = "showGoalMarkers",
  val newCashoutFlowEnabled: Boolean = true,
  val preGameScreenMode: String? = null,
  val allGamesButtonMode: String = "hideButton",
  val rewardsScreenMode: String? = "combined",
  val beEmailValidation: Boolean? = true,
  val coinsAmountSeparatorMode: String? = "localeRelated",
  val interviewParticipant: Boolean? = null,
  val showStatsOnMainScreen: Boolean? = null,
)