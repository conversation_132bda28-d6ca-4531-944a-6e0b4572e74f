package com.moregames.playtime.ios

import com.moregames.base.util.ApplicationId.BALL_BOUNCE_APP_ID
import com.moregames.base.util.ApplicationId.BUBBLE_POP_APP_ID
import com.moregames.base.util.ApplicationId.EMOJICLICKERS_APP_ID
import com.moregames.base.util.ApplicationId.FAIRY_TALE_MANSION_APP_ID
import com.moregames.base.util.ApplicationId.MAD_SMASH_APP_ID
import com.moregames.base.util.ApplicationId.MERGE_BLAST_APP_ID
import com.moregames.base.util.ApplicationId.MIX_BLOX_APP_ID
import com.moregames.base.util.ApplicationId.PUZZLE_POP_BLASTER_APP_ID
import com.moregames.base.util.ApplicationId.SOLITAIRE_VERSE_APP_ID
import com.moregames.base.util.ApplicationId.SUGAR_RUSH_APP_ID
import com.moregames.base.util.ApplicationId.TILE_MATCH_PRO_APP_ID
import com.moregames.base.util.ApplicationId.TREASURE_MASTER_APP_ID
import com.moregames.base.util.ApplicationId.WATER_SORTER_APP_ID
import com.moregames.base.util.ApplicationId.WOODEN_PUZZLE_APP_ID
import com.moregames.base.util.ApplicationId.WORD_SEEKER_APP_ID
import com.moregames.base.util.RandomGenerator
import com.moregames.base.util.TimeService
import com.moregames.playtime.ios.dto.GameApiDto
import java.math.BigDecimal
import java.math.MathContext
import java.math.RoundingMode
import java.time.ZoneOffset
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class IosOnlineUsersService @Inject constructor(
  private val timeService: TimeService,
  private val randomGenerator: RandomGenerator
) {

  fun getActiveUsers(): Int {
    return usersMap.getValue(timeService.now().atOffset(ZoneOffset.UTC).hour)
  }

  fun getActiveUsersForGames(games: List<GameApiDto>): Map<Int, Int> {
    val activeUsers = getActiveUsers().toBigDecimal()
    return games.associate { game ->
      game.id to activeUsers
        .multiply(gamesShare[game.applicationId] ?: BigDecimal("0.01"), MathContext(4, RoundingMode.DOWN))
        .multiply(randomGenerator.nextDouble(0.95, 1.05).toBigDecimal(), MathContext(4, RoundingMode.DOWN))
        .toInt()
    }
  }

  private val usersMap = mapOf(
    0 to 48954,
    1 to 47692,
    2 to 46075,
    3 to 43535,
    4 to 39747,
    5 to 36585,
    6 to 34493,
    7 to 32750,
    8 to 31529,
    9 to 31113,
    10 to 32320,
    11 to 35408,
    12 to 39068,
    13 to 42204,
    14 to 45135,
    15 to 47321,
    16 to 48989,
    17 to 50670,
    18 to 52434,
    19 to 54370,
    20 to 55929,
    21 to 55764,
    22 to 54263,
    23 to 51476,
  ).withDefault { 55764 }

  private val gamesShare = mapOf(
    TREASURE_MASTER_APP_ID to BigDecimal("0.25"),
    SOLITAIRE_VERSE_APP_ID to BigDecimal("0.15"),
    BALL_BOUNCE_APP_ID to BigDecimal("0.08"),
    WOODEN_PUZZLE_APP_ID to BigDecimal("0.08"),
    BUBBLE_POP_APP_ID to BigDecimal("0.08"),
    SUGAR_RUSH_APP_ID to BigDecimal("0.06"),
    MERGE_BLAST_APP_ID to BigDecimal("0.06"),
    MAD_SMASH_APP_ID to BigDecimal("0.06"),
    WORD_SEEKER_APP_ID to BigDecimal("0.04"),
    MIX_BLOX_APP_ID to BigDecimal("0.04"),
    EMOJICLICKERS_APP_ID to BigDecimal("0.04"),
    WATER_SORTER_APP_ID to BigDecimal("0.03"),
    FAIRY_TALE_MANSION_APP_ID to BigDecimal("0.03"),
    PUZZLE_POP_BLASTER_APP_ID to BigDecimal("0.03"),
    TILE_MATCH_PRO_APP_ID to BigDecimal("0.02"),
  )
}
