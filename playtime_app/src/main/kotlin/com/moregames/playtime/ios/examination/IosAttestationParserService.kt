package com.moregames.playtime.ios.examination

import ch.veehait.devicecheck.appattest.attestation.AttestationValidator
import ch.veehait.devicecheck.appattest.attestation.ValidatedAttestation
import com.google.inject.Inject
import com.google.inject.Provider
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.BuildVariant.PRODUCTION
import com.moregames.base.util.TimeService
import com.moregames.base.util.base64Decoded
import com.moregames.base.util.logger
import com.moregames.playtime.app.CoreModule.Companion.IOS_ATTESTATION_VALIDATOR_PROD_JP
import com.moregames.playtime.app.CoreModule.Companion.IOS_ATTESTATION_VALIDATOR_PROD_QA
import com.moregames.playtime.app.CoreModule.Companion.IOS_ATTESTATION_VALIDATOR_SANDBOX_JP
import com.moregames.playtime.app.CoreModule.Companion.IOS_ATTESTATION_VALIDATOR_SANDBOX_QA
import com.moregames.playtime.ios.dto.IosExaminationEnv
import com.moregames.playtime.ios.dto.IosExaminationEnv.*
import com.moregames.playtime.ios.examination.dto.IosAttestationStatementDto
import java.util.*
import javax.inject.Named

class IosAttestationParserService @Inject constructor(
  private val timeService: TimeService,
  @Named(IOS_ATTESTATION_VALIDATOR_SANDBOX_JP) private val attestationValidatorSandboxJp: AttestationValidator,
  @Named(IOS_ATTESTATION_VALIDATOR_SANDBOX_QA) private val attestationValidatorSandboxQa: AttestationValidator,
  @Named(IOS_ATTESTATION_VALIDATOR_PROD_JP) private val attestationValidatorProdJp: AttestationValidator,
  @Named(IOS_ATTESTATION_VALIDATOR_PROD_QA) private val attestationValidatorProdQa: AttestationValidator,
  private val buildVariantProvider: Provider<BuildVariant>,
) {


  suspend fun parseAndVerify(
    attestationStatement: String,
    keyIdBase64: String,
    challenge: String,
    iosExaminationEnv: IosExaminationEnv
  ): IosAttestationStatementDto {
    // https://developer.apple.com/documentation/devicecheck
    // https://github.com/veehaitch/devicecheck-appattest

    return with(getValidator(iosExaminationEnv)) {
      try {
        validateAsync(
          attestationObject = attestationStatement.base64Decoded(),
          keyIdBase64 = keyIdBase64,
          serverChallenge = challenge.toByteArray(),
        ).toAttestationStatementDto(keyIdBase64, challenge)
      } catch (e: Exception) {
        logger().warn("DeviceCheck validation fail. Statement: '$attestationStatement'", e)
        IosAttestationStatementDto.errorAttestationStatement(
          creationTime = timeService.now(),
          challenge = challenge,
          keyIdBase64 = keyIdBase64,
          error = e.message ?: "Null-message exception in processing"
        )
      }
    }
  }

  private fun getValidator(examinationEnvironment: IosExaminationEnv): AttestationValidator {
    if (buildVariantProvider.get() == PRODUCTION) return attestationValidatorProdJp

    return when (examinationEnvironment) {
      PROD_JP -> attestationValidatorProdJp
      PROD_QA -> attestationValidatorProdQa
      SANDBOX_JP -> attestationValidatorSandboxJp
      SANDBOX_QA -> attestationValidatorSandboxQa
    }
  }
}

fun ValidatedAttestation.toAttestationStatementDto(keyIdBase64: String, challenge: String): IosAttestationStatementDto {
  return IosAttestationStatementDto(
    creationTime = this.receipt.payload.creationTime.value,
    challenge = challenge,
    keyIdBase64 = keyIdBase64,
    error = null,
    iosVersion = this.iOSVersion,
    appId = this.receipt.payload.appId.value,
    attestationCertificate = this.receipt.payload.attestationCertificate.value.toString(),
    clientHash = Base64.getEncoder().encodeToString(this.receipt.payload.clientHash.value),
    token = this.receipt.payload.token.value,
    receiptType = this.receipt.payload.type.value.toString(),
    environment = this.receipt.payload.environment?.value,
    riskMetric = this.receipt.payload.riskMetric?.value,
    notBefore = this.receipt.payload.notBefore?.value,
    expirationTime = this.receipt.payload.expirationTime.value
  )
}