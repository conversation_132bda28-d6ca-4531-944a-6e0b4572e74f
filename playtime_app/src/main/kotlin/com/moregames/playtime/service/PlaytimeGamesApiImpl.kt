package com.moregames.playtime.service

import com.google.protobuf.Empty
import com.justplayapps.base.Common.AppPlatformProto
import com.justplayapps.playtime.proto.Playtime
import com.justplayapps.playtime.proto.PlaytimeGamesApiGrpcKt
import com.justplayapps.playtime.proto.gameIdDto
import com.justplayapps.playtime.proto.gameIdList
import com.moregames.playtime.games.GamesService
import javax.inject.Inject

class PlaytimeGamesApiImpl @Inject constructor(
  private val gamesService: GamesService,
) : PlaytimeGamesApiGrpcKt.PlaytimeGamesApiCoroutineImplBase() {
  override suspend fun getAllGames(request: Empty): Playtime.GameIdList {
    return gamesService.getGames().map {
      gameIdDto {
        this.gameId = it.key
        this.applicationId = it.value.applicationId
        this.appPlatform = it.value.platform?.name?.let { AppPlatformProto.valueOf(it) } ?: AppPlatformProto.UNDEFINED
        this.applovinApiKey = it.value.applovinApiKey
      }
    }.let {
      gameIdList {
        games.addAll(it)
      }
    }
  }
}