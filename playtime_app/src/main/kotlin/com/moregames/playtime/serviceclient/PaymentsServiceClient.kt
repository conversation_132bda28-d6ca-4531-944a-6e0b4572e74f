package com.moregames.playtime.serviceclient

import com.google.inject.Inject
import com.google.inject.name.Named
import com.moregames.base.app.BuildVariant
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.config.ServicesRegistry
import com.moregames.base.dto.Payment
import com.moregames.base.dto.Payment.PaymentStatus.*
import com.moregames.base.table.UserCashoutTransactionsTable
import com.moregames.base.util.TimeService
import com.moregames.playtime.app.CoreModule
import com.moregames.playtime.serviceclient.dto.ResendEmailRequestResponse
import com.moregames.playtime.user.cashout.CashoutService
import com.moregames.playtime.user.cashout.dto.CashoutTransactionDto
import io.ktor.client.*
import io.ktor.client.features.*
import io.ktor.client.request.*
import io.ktor.http.*
import java.time.temporal.ChronoUnit
import javax.inject.Singleton

@Singleton
class PaymentsServiceClient @Inject constructor(
  @Named(CoreModule.CROSS_SERVICE_HTTP_CLIENT) private val httpClient: HttpClient,
  private val cashoutService: CashoutService,
  private val timeService: TimeService,
  private val applicationConfig: ApplicationConfig,
  buildVariant: BuildVariant,
) {
  private val paymentServiceLocation = ServicesRegistry.PAYMENT.getPath(buildVariant, applicationConfig.isK8s)

  suspend fun getPayments(userId: String, limit: Int?, page: Int?, filterByType: String?): List<Payment> {
    val payments = httpClient.get<List<Payment>>("$paymentServiceLocation/payments") {
      parameter("userId", userId)
      limit?.let { parameter("limit", limit) }
      page?.let { parameter("page", page) }
      filterByType?.let { parameter("type", filterByType) }
    }
    val paymentIds = payments.map { payment -> payment.cashoutTransactionId }
    val cashouts = if (page == null || page == 0) {
      cashoutService.loadRecentTransactions(userId, timeService.now().minus(1, ChronoUnit.HOURS), CashoutService.TransactionsFilterType.parse(filterByType))
        .filter { it.cashoutTransactionId !in paymentIds }
    } else emptyList()
    return (cashouts.map { cashoutToPayment(it) } + payments)
      .let { list ->
        // TODO we don't use personal data on client side, but it is still needed to backward compatibility
        list.map {
          val s = it.sanitizePersonalData()
          s
        }
      }
  }

  suspend fun getDetailedPayment(cashoutTransactionId: String): Payment {
    return try {
      httpClient.get<Payment>("$paymentServiceLocation/payments/$cashoutTransactionId")
    } catch (e: ClientRequestException) {
      if (e.response.status == HttpStatusCode.NotFound) {
        return cashoutToPayment(cashoutService.loadTransaction(cashoutTransactionId))
      } else {
        throw e
      }
    }
  }

  //should be switched to pub/sub if POC successful
  suspend fun resendEmail(paymentProvider: String, cashoutTransactionId: String): ResendEmailRequestResponse {
    return try {
      httpClient.post(urlString = "$paymentServiceLocation/payments/$paymentProvider/$cashoutTransactionId/resend")
    } catch (e: Exception) {
      throw e
    }
  }

  private fun cashoutToPayment(cashoutTransaction: CashoutTransactionDto) =
    Payment(
      cashoutTransactionId = cashoutTransaction.cashoutTransactionId,
      createdAt = timeService.now(),
      userId = cashoutTransaction.userId,
      provider = cashoutTransaction.provider,
      countryCode = "",
      currencyCode = cashoutTransaction.userCurrency.currencyCode,
      amount = cashoutTransaction.userCurrencyAmount,
      recipientHandle = cashoutTransaction.userHandle,
      encryptedRecipientName = "",
      encryptedRecipientEmail = cashoutTransaction.encryptedEmail,
      status = when (cashoutTransaction.status) {
        UserCashoutTransactionsTable.Status.INITIAL -> INITIATED
        UserCashoutTransactionsTable.Status.REQUESTED -> INITIATED
        UserCashoutTransactionsTable.Status.PENDING -> PENDING
        UserCashoutTransactionsTable.Status.SUCCESSFUL -> COMPLETED
        UserCashoutTransactionsTable.Status.ALREADY_PAYED -> COMPLETED
        UserCashoutTransactionsTable.Status.FAILED -> REJECTED
        UserCashoutTransactionsTable.Status.REJECTED -> REJECTED
      }
    )

  private fun Payment.sanitizePersonalData() =
    this.copy(
      encryptedAddress = "",
      encryptedRecipientName = "",
      encryptedRecipientEmail = "",
    )
}
