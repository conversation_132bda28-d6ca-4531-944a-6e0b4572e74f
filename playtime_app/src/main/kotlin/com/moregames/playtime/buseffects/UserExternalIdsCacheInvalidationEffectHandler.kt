package com.moregames.playtime.buseffects

import com.moregames.base.bus.AsyncEffect
import com.moregames.base.bus.EffectHandler
import com.moregames.playtime.user.UserDataCache
import javax.inject.Inject

class UserExternalIdsCacheInvalidationEffectHandler @Inject constructor(
  private val userDataCache: UserDataCache
) {
  @EffectHandler
  suspend fun handleInvalidateUserExternalIDsCacheEffect(effect: InvalidateUserExternalIDsCacheEffect) {
    userDataCache.invalidateExternalIDsCache(effect.userId)
  }

  @EffectHandler
  suspend fun handleMassInvalidateUserExternalIDsCacheEffect(effect: MassInvalidateUserExternalIDsCacheEffect) {
    userDataCache.invalidateExternalIDsCache(effect.userIds)
  }
}

data class InvalidateUserExternalIDsCacheEffect(val userId: String) : AsyncEffect

data class MassInvalidateUserExternalIDsCacheEffect(val userIds: Set<String>) : AsyncEffect