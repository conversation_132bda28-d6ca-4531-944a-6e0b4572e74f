package com.moregames.playtime.buseffects

import com.justplayapps.service.rewarding.earnings.dto.EarningsCalculationResult
import com.justplayapps.service.rewarding.earnings.dto.simpleEarnings
import com.moregames.base.abtesting.AbGameCoinGoalsService
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.bool
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.Effect
import com.moregames.base.bus.EffectHandler
import com.moregames.base.bus.MessageBus
import com.moregames.base.messaging.dto.EarningsCappedEventDto
import com.moregames.base.util.IoCoroutineScope
import com.moregames.base.util.TimeService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.PaymentProvidersService.Companion.MIN_CASHOUT_AMOUNT_USD
import com.moregames.playtime.user.coingoal.GameCoinGoalsService
import com.moregames.playtime.user.fraudscore.FraudScoreService
import com.moregames.playtime.user.onboarding.progressbar.OnboardingProgressBarService
import kotlinx.coroutines.launch
import java.math.BigDecimal
import javax.inject.Inject
import javax.inject.Provider

class CashoutPeriodEffects @Inject constructor(
  private val abGameCoinGoalsService: AbGameCoinGoalsService,
  private val abTestingService: AbTestingService,
  private val userService: UserService,
  private val fraudScoreService: FraudScoreService,
  private val gameCoinGoalsService: GameCoinGoalsService,
  private val timeService: TimeService,
  private val bigQueryEventPublisher: BigQueryEventPublisher,
  private val coroutineScope: Provider<IoCoroutineScope>,
  private val onboardingProgressBarService: OnboardingProgressBarService,
  private val messageBus: MessageBus,
) {

  @EffectHandler
  suspend fun handleCashoutPeriodEndedEffect(effect: CashoutPeriodEndedEffect) = with(effect) {
    val userAppPlatform = userService.getUser(userId).appPlatform

    if (abGameCoinGoalsService.isGameCoinGoalsParticipant(userId, userAppPlatform).bool()) {
      gameCoinGoalsService.resetBalance(userId)
    }

    messageBus.publish(CreateCashoutOfferSetEffect(userId))

    val userHasCoinsFromGames = (if (abTestingService.isEm2Participant(userId = userId))
      userService.getUserLastGameEm2CoinsDate(userId)
    else
      userService.getUserLastGameCoinsDate(userId)) != null
    // offboarding note - MIN_CASHOUT_AMOUNT here is not applicable for JP and MX.
    if (earnings.simpleEarnings.amount > MIN_CASHOUT_AMOUNT_USD && userHasCoinsFromGames && abTestingService.isUserExperimentParticipant(
        userId = userId,
        experiment = ClientExperiment.ANDROID_ONBOARDING_PROGRESS_BAR
      )
    ) {
      onboardingProgressBarService.activateRouteToCashout(userId)
      onboardingProgressBarService.completePlayFirstGame(userId)
    }
    coroutineScope.get().launch {
      fraudScoreService.checkUserApplovinRevenueFrequencyAndBlockFraudsters(userId)
      fraudScoreService.blockUserOnGameAutomation(userId)
      if (earnings is EarningsCalculationResult.Em2) {
        sendCappedEvents(userId, earnings)
      }
    }
  }

  private suspend fun sendCappedEvents(userId: String, em2EarningsComputationData: EarningsCalculationResult.Em2) =
    listOfNotNull(
      em2EarningsComputationData.toEm2Capped()
        .takeIf { it > BigDecimal.ZERO }
        ?.let { em2Capped ->
          EarningsCappedEventDto(
            metaId = em2EarningsComputationData.simpleCalculationResult.metaId,
            userId = userId,
            cappedAmount = em2Capped,
            createdAt = timeService.now(),
            cappedEventType = EarningsCappedEventDto.CappedEventType.GAME_REAL_REVENUE_CUT
          )
        },
      em2EarningsComputationData.toQuotaCapped()
        .takeIf { it > BigDecimal.ZERO }
        ?.let { quotaCapped ->
          EarningsCappedEventDto(
            metaId = em2EarningsComputationData.simpleCalculationResult.metaId,
            userId = userId,
            cappedAmount = quotaCapped,
            createdAt = timeService.now(),
            cappedEventType = EarningsCappedEventDto.CappedEventType.QUOTA_REVENUE_CUT
          )
        },
    )
      .let { bigQueryEventPublisher.publish(it) }
}

data class CashoutPeriodEndedEffect(
  val userId: String,
  val earnings: EarningsCalculationResult,
) : Effect