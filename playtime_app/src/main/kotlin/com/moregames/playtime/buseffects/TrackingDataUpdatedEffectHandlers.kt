package com.moregames.playtime.buseffects

import com.moregames.base.bus.AsyncEffect
import com.moregames.base.bus.EffectHandler
import com.moregames.base.bus.MessageBus
import com.moregames.base.messaging.dto.UserDeletedEventMessageDto
import com.moregames.playtime.user.ActiveUsersService
import javax.inject.Inject

class TrackingDataUpdatedEffectHandlers @Inject constructor(
  private val messageBus: MessageBus,
  private val activeUsersService: ActiveUsersService,
) {

  @EffectHandler
  suspend fun handleSendUserDeletedEvent(effect: SendUserDeletedEvent) = with(effect) {
    activeUsersService.removeUsers(userIds)
    userIds.forEach { messageBus.publish(UserDeletedEventMessageDto(it)) }
  }
}

data class SendUserDeletedEvent(val userIds: Set<String>) : AsyncEffect