package com.moregames.playtime.buseffects

import com.moregames.base.bus.Effect
import com.moregames.base.bus.EffectHandler
import com.moregames.base.messaging.commands.CommandsClient
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserCommandsNotificationsEffectHandlers @Inject constructor(
  private val commandsClient: CommandsClient,
) {

  @EffectHandler
  suspend fun handleSendCommandBasedBalanceUpdatedNotification(effect: SendCommandBasedBalanceUpdatedNotification) {
    commandsClient.completeCommandSync(effect.commandId, mapOf("balance" to effect.balance.toString()))
  }
}

data class SendCommandBasedBalanceUpdatedNotification(val commandId: String, val balance: Long) : Effect