package com.moregames.playtime.games

import com.google.inject.Inject
import com.google.inject.Provider
import com.google.inject.Singleton
import com.moregames.base.app.BuildVariant
import com.moregames.base.util.ApplicationId.BALL_BOUNCE_APP_ID
import com.moregames.base.util.ApplicationId.BUBBLE_POP_APP_ID
import com.moregames.base.util.ApplicationId.EMOJICLICKERS_APP_ID
import com.moregames.base.util.ApplicationId.FAIRY_TALE_MANSION_APP_ID
import com.moregames.base.util.ApplicationId.MAD_SMASH_APP_ID
import com.moregames.base.util.ApplicationId.MERGE_BLAST_APP_ID
import com.moregames.base.util.ApplicationId.MIX_BLOX_APP_ID
import com.moregames.base.util.ApplicationId.PUZZLE_POP_BLASTER_APP_ID
import com.moregames.base.util.ApplicationId.SOLITAIRE_VERSE_APP_ID
import com.moregames.base.util.ApplicationId.SUGAR_RUSH_APP_ID
import com.moregames.base.util.ApplicationId.TILE_MATCH_PRO_APP_ID
import com.moregames.base.util.ApplicationId.TREASURE_MASTER_APP_ID
import com.moregames.base.util.ApplicationId.WATER_SORTER_APP_ID
import com.moregames.base.util.ApplicationId.WOODEN_PUZZLE_APP_ID
import com.moregames.base.util.ApplicationId.WORD_SEEKER_APP_ID
import com.moregames.base.util.ClientVersionsSupport.IOS_LAST_VERSION_BEFORE_REMOVING_FROM_APP_STORE
import com.moregames.base.util.IoCoroutineScope
import com.moregames.base.util.TimeService
import com.moregames.base.util.buildCache
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.ios.dto.GameApiDto
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.UserService
import kotlinx.coroutines.async
import java.util.*

private const val PLAYED_GAMES_LIST_SIZE = 4

@Singleton
class IosGameService @Inject constructor(
  private val gamePersistenceService: GamePersistenceService,
  private val userService: UserService,
  private val imageService: ImageService,
  private val translationService: UserTranslationService,
  private val coroutineScope: Provider<IoCoroutineScope>,
  buildVariant: BuildVariant,
  private val timeService: TimeService
) {

  companion object {
    private const val PRE_GAME_SCREEN_CACHE_KEY_STUB = "stub"
    private const val PRE_GAME_SCREEN_CACHE_EXPIRATION_PROD = 10L
    private val trendingIcons = mapOf(
      TREASURE_MASTER_APP_ID to "ios_treasuremaster_trending_icon.jpg",
      SOLITAIRE_VERSE_APP_ID to "ios_solitaireverse_trending_icon.jpg",
      SUGAR_RUSH_APP_ID to "ios_sugarmatch_trending_icon.jpg",
      MERGE_BLAST_APP_ID to "ios_mergeblast_trending_icon.jpg",
      BALL_BOUNCE_APP_ID to "ios_ballbounce_trending_icon.jpg",
      WOODEN_PUZZLE_APP_ID to "ios_zentiles_trending_icon.jpg",
      MAD_SMASH_APP_ID to "ios_madsmash_trending_icon.jpg",
      PUZZLE_POP_BLASTER_APP_ID to "ios_puzzlepopblaster_trending_icon.jpg",
      BUBBLE_POP_APP_ID to "ios_bubblepop_trending_icon.jpg",
      FAIRY_TALE_MANSION_APP_ID to "ios_fairytalematch_trending_icon.jpg",
      EMOJICLICKERS_APP_ID to "ios_emojiclickers_trending_icon.jpg",
      MIX_BLOX_APP_ID to "ios_mixblox_trending_icon.jpg",
      TILE_MATCH_PRO_APP_ID to "ios_tilematchpro_trending_icon.jpg",
      WATER_SORTER_APP_ID to "ios_watersorter_trending_icon.jpg",
      WORD_SEEKER_APP_ID to "ios_wordseeker_trending_icon.jpg",
    )

    private val testPlayDescriptions = mapOf(
      TREASURE_MASTER_APP_ID to "\$_ios_treasuremaster_exp_description",
      SOLITAIRE_VERSE_APP_ID to "\$_ios_solitaireverse_exp_description",
      BALL_BOUNCE_APP_ID to "\$_ios_ballbounce_exp_description",
      WOODEN_PUZZLE_APP_ID to "\$_ios_zentiles_exp_description",
      BUBBLE_POP_APP_ID to "\$_ios_bubblepop_exp_description",
      SUGAR_RUSH_APP_ID to "\$_ios_sugarmatch_exp_description",
      MERGE_BLAST_APP_ID to "\$_ios_mergeblast_exp_description",
      MAD_SMASH_APP_ID to "\$_ios_madsmash_exp_description",
      WORD_SEEKER_APP_ID to "\$_ios_wordseeker_exp_description",
      MIX_BLOX_APP_ID to "\$_ios_mixblox_exp_description",
      EMOJICLICKERS_APP_ID to "\$_ios_emojiclickers_exp_description",
      WATER_SORTER_APP_ID to "\$_ios_watersorter_exp_description",
      FAIRY_TALE_MANSION_APP_ID to "\$_ios_fairytalematch_exp_description",
      PUZZLE_POP_BLASTER_APP_ID to "\$_ios_popblast_exp_description",
      TILE_MATCH_PRO_APP_ID to "\$_ios_tilematchpro_exp_description",
    )

    private val promoImages = mapOf(
      TREASURE_MASTER_APP_ID to "ios_promo_2025_04_16/TreasureMaster_badge.png",
      SOLITAIRE_VERSE_APP_ID to "ios_promo_2025_04_16/SolitaireVerse_badge.png",
      BALL_BOUNCE_APP_ID to "ios_promo_2025_04_16/BallBounce_badge.png",
      WOODEN_PUZZLE_APP_ID to "ios_promo_2025_04_16/WoodenPuzzleBliss_badge.png",
      BUBBLE_POP_APP_ID to "ios_promo_2025_04_16/WildBublePopRescue_badge.png",
      SUGAR_RUSH_APP_ID to "ios_promo_2025_04_16/SugarRushAdventure_badge.png",
      MERGE_BLAST_APP_ID to "ios_promo_2025_04_16/MergeBalst_badge.png",
      WORD_SEEKER_APP_ID to "ios_promo_2025_04_16/WordSeeker_badge.png",
      MIX_BLOX_APP_ID to "ios_promo_2025_04_16/MixBlox_badge.png",
      WATER_SORTER_APP_ID to "ios_promo_2025_04_16/WaterSorter_badge.png",
      PUZZLE_POP_BLASTER_APP_ID to "ios_promo_2025_04_16/PuzzlePopBlaster_badge.png",
      TILE_MATCH_PRO_APP_ID to "ios_promo_2025_04_16/TileMatchPro_badge.png"
    )
  }

  private val preGameScreenCache = buildCache(buildVariant, expireAfter = PRE_GAME_SCREEN_CACHE_EXPIRATION_PROD) { _: String ->
    coroutineScope.get().async {
      gamePersistenceService.loadIosPreGameScreens()
    }
  }

  suspend fun loadNewGames(userId: String, locale: Locale, iosDeviceMajorVersion: Int?): List<GameApiDto> {
    val userGameCoins = userService.loadUserGameCoins(userId)

    val games = loadNewGames(
      userId = userId,
      locale = locale,
      iosDeviceMajorVersion = iosDeviceMajorVersion,
      userGameCoins = userGameCoins,
      ifAllGamesPlayedFallback = {
        loadPlayedGamesOrdered(userGameCoins)
          .drop(PLAYED_GAMES_LIST_SIZE)
          .sortedBy { game -> game.orderKey }
      },
      takeLimit = 5
    )//.mapIndexed { idx, game -> if (idx == 0) game.copy(showBadge = true) else game }
    // we want to temporarily show all images with badges (and we need other badge, so we have to include it into the images)
    // https://justplayapps.slack.com/archives/C04C7K6UFQS/p1744814819847689?thread_ts=1744632395.209879&cid=C04C7K6UFQS


    return games
  }

  suspend fun loadPlayedGames(userId: String, locale: Locale) =
    userService.loadUserGameCoins(userId).let { userGameCoins ->
      loadPlayedGamesOrdered(userGameCoins)
        .take(PLAYED_GAMES_LIST_SIZE)
    }
      .let { applyPromoImagesForOldUsers(it, userService.getUser(userId, includingDeleted = true).appVersion) }
      .map { game -> translateGame(game, locale, userId) }
      .map { game -> game2ApiDto(game) }
      .let { applyTrendingIcons(it) }

  suspend fun loadNewsRelatedGameOffers(applicationIds: Set<String>): List<IosGameOffer> =
    gamePersistenceService.loadVisibleIosGamesByApplicationIds(applicationIds)

  private fun game2ApiDto(game: IosGameOffer) =
    with(game) {
      GameApiDto(
        id = id,
        applicationId = applicationId,
        name = name,
        description = description,
        iconFilename = imageService.toUrl(iconFilename),
        imageFilename = imageService.toUrl(imageFilename),
        infoTextInstall = infoTextInstall,
        iosApplicationId = iosApplicationId,
        iosGameUrl = iosGameUrl
      )
    }

  private suspend fun translateGame(game: IosGameOffer, locale: Locale, userId: String): IosGameOffer =
    with(translationService) {
      game.copy(
        description = tryTranslate(game.description, locale, userId),
        infoTextInstall = tryTranslate(game.infoTextInstall, locale, userId)
      )
    }

  private suspend fun loadPlayedGamesOrdered(userGameCoins: Map<Int, UserPersistenceService.GamePlayStatusDto>) =
    gamePersistenceService.loadVisibleIosGamesByIds(userGameCoins.keys)
      .sortedByDescending { game ->
        userGameCoins[game.id]?.lastPlayedAt
      }

  private suspend fun addPreGameScreen(games: List<GameApiDto>, iosDeviceMajorVersion: Int?): List<GameApiDto> {
    if ((iosDeviceMajorVersion ?: 0) < 16) return games

    val preGameScreens = preGameScreenCache.get(PRE_GAME_SCREEN_CACHE_KEY_STUB).await()
    return games.map { game ->
      val preGameScreen = preGameScreens[game.id]
      if (preGameScreen != null) {
        game.copy(preGameScreen = preGameScreen.toApiDto(imageService))
      } else game
    }
  }

  private suspend fun loadNewGames(
    userId: String,
    locale: Locale,
    iosDeviceMajorVersion: Int?,
    userGameCoins: Map<Int, UserPersistenceService.GamePlayStatusDto>,
    ifAllGamesPlayedFallback: suspend () -> List<IosGameOffer>,
    takeLimit: Int,
  ): List<GameApiDto> {
    val user = userService.getUser(userId, includingDeleted = true)
    val boostedGameOrNull = if (user.appVersion <= IOS_LAST_VERSION_BEFORE_REMOVING_FROM_APP_STORE) {
      gamePersistenceService.loadVisibleIosBoostedGame(timeService.today())
    } else null

    val games = gamePersistenceService.loadVisibleIosGamesExcludeIds(excludedGameIds = userGameCoins.keys)
      .ifEmpty { ifAllGamesPlayedFallback() }
      .let { listOfNotNull(boostedGameOrNull) + it }
      .distinctBy { it.id }//keeps order, removes possible duplicate of the boosted game
      .take(takeLimit)
      .map { game -> translateGame(game, locale, userId) }
      .let { applyPromoImagesForOldUsers(it, user.appVersion, boostedGameOrNull) }
      .map { game -> game2ApiDto(game) }
      .let { addPreGameScreen(it, iosDeviceMajorVersion) }
      .let { applyTrendingIcons(it) }
      .map { translateTestPlayDescription(it, locale, userId) }


    return games
  }

  private suspend fun translateTestPlayDescription(dto: GameApiDto, locale: Locale, userId: String): GameApiDto {
    val translated = testPlayDescriptions[dto.applicationId]?.let {
      translationService.tryTranslate(it, locale, userId)
    }
    return if (translated == null) {
      dto
    } else {
      dto.copy(testplayDescription = translated)
    }
  }

  private fun applyTrendingIcons(games: List<GameApiDto>): List<GameApiDto> = games.map { game ->
    game.copy(trendingIcon = trendingIcons[game.applicationId]?.let { icon -> imageService.toUrl(icon) })
  }

  private fun applyPromoImagesForOldUsers(games: List<IosGameOffer>, appVersion: Int, boostedGameOrNull: IosGameOffer? = null): List<IosGameOffer> =
    if (appVersion <= IOS_LAST_VERSION_BEFORE_REMOVING_FROM_APP_STORE) {
      games.map { game ->
        val boostedGameId = boostedGameOrNull?.id ?: -1
        val promoImage = promoImages[game.applicationId]
        if (promoImage != null && game.id != boostedGameId)
          game.copy(imageFilename = promoImage)
        else game
      }
    } else games


}
