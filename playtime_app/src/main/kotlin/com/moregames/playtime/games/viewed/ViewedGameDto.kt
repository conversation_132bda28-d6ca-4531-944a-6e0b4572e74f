package com.moregames.playtime.games.viewed

import com.moregames.base.util.ClientVersionsSupport.ANDROID_MARK_VIEWED_GAMES_UNUSED_APP_VERSION

@Deprecated("Unused from version $ANDROID_MARK_VIEWED_GAMES_UNUSED_APP_VERSION")
data class ViewedGameDto(
  val userId: String,
  val gameId: Int,
) {
  companion object {
    fun fromApiRequest(userId: String, request: ViewedGamesApiDto) =
      request.viewedGamesIds.map { gameId -> ViewedGameDto(userId, gameId.toInt()) }.toSet()
  }
}