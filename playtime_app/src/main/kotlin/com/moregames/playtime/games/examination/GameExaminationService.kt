package com.moregames.playtime.games.examination

import com.google.inject.Inject
import com.google.inject.Singleton
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.util.RandomGenerator
import com.moregames.base.util.TimeService
import com.moregames.base.util.logger
import com.moregames.playtime.checks.ExaminationService
import com.moregames.playtime.checks.IntegrityParserService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.exception.ExaminationThrottlingException
import java.time.temporal.ChronoUnit

@Singleton
class GameExaminationService @Inject constructor(
  private val randomGenerator: RandomGenerator,
  private val gameExaminationPersistenceService: GameExaminationPersistenceService,
  private val timeService: TimeService,
  private val integrityParserService: IntegrityParserService,
  private val examinationService: ExaminationService,
  private val abTestingService: AbTestingService,
  private val userService: UserService,
) {
  suspend fun initiateExaminationGetNonce(userId: String, gameId: Int): String {
    // should never normally happen, but I do not trust games.
    if (!abTestingService.shouldUseGamesPlayIntegrity(userId)) throw ExaminationThrottlingException(userId)

    return randomGenerator.nextUUID()
      .also { nonce ->
        gameExaminationPersistenceService.initiateDeviceExamination(
          userId = userId,
          gameId = gameId,
          nonce = nonce,
          validUntil = timeService.now().plus(5, ChronoUnit.MINUTES)
        )
      }
  }

  suspend fun examinationRequired(userId: String, gameId: Int): Boolean =
    !gameExaminationPersistenceService.successfulExaminationExists(userId, gameId) &&
      abTestingService.shouldUseGamesPlayIntegrity(userId)

  suspend fun examine(userId: String, applicationId: String, signedAttestationStatement: String) {
    val payload = integrityParserService.parseAndVerify(applicationId, signedAttestationStatement)

    logger().debug("User '{}', applicationId '{}', game attestation: '{}'", userId, applicationId, payload)

    // todo: detect and bypass outage of PlayIntegrity https://app.asana.com/0/0/1205916470462274/f

    val payloadNonce = payload.decodedNonce()

    if (!payload.error.isNullOrEmpty()) {
      gameExaminationPersistenceService.saveErroneousAttestationPayload(payloadNonce, payload)
    } else {
      val nonceData = gameExaminationPersistenceService.getNonceData(payloadNonce)

      val osVersion = userService.loadUserDevice(userId)?.osVersion?.toIntOrNull()

      // as in ExaminationService for repeated calls `examinationPassedWell` will return `false` because `examinedAt` will be
      //   already filled.
      //   1. Currently, this has no negative consequences
      //   2. strictly speaking, "same attestation" assertion should take `applicationId` into account here
      val examinationPassedWell =
        examinationService.examinationBasicallyWell(userId, nonceData, payload, expectedApplicationId = applicationId, osVersion = osVersion)
      // todo: validate certificate sha like in ExaminationService https://app.asana.com/0/0/****************/f

      if (!examinationPassedWell) {
        logger().warn("User $userId, applicationId $applicationId device examination failed: nonceData: $nonceData payload: $payload")
      }

      val status = if (examinationPassedWell) GameExaminationStatus.SUCCESS else GameExaminationStatus.FAIL

      gameExaminationPersistenceService.saveAttestationPayload(payload, status)
    }
  }
}