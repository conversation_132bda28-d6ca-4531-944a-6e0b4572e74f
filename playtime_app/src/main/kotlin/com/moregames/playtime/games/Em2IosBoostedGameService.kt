package com.moregames.playtime.games

import com.google.inject.Inject
import com.moregames.base.app.BuildVariant
import com.moregames.base.util.TimeService
import com.moregames.base.util.ex
import com.moregames.base.util.redis.SafeJedisClient
import redis.clients.jedis.params.SetParams
import javax.inject.Singleton
import kotlin.time.Duration.Companion.minutes


@Singleton
class Em2IosBoostedGameService @Inject constructor(
  private val safeJedisClient: SafeJedisClient,
  private val gamePersistenceService: GamePersistenceService,
  private val timeService: TimeService,
  private val buildVariant: BuildVariant
) {

  companion object {
    const val CACHE_KEY = "IosCurrentBoostedGameId2"
    const val TEST_CACHE_KEY = "IosCurrentBoostedGameId2Test"
    val TEN_MINUTES: SetParams = SetParams().ex(10.minutes)
    const val NO_BOOSTED_GAME_ID_STUB = -1

  }

  suspend fun isIosBoostedGame(gameId: Int): Boolean =
    getBoostedGameId() == gameId

  private suspend fun getBoostedGameId(): Int {
    val cachedValue = safeJedisClient.get(getCacheKey())
    if (cachedValue != null) {
      return cachedValue.toInt()
    }
    return (gamePersistenceService.findTodayIosBoostedGameId(timeService.today()) ?: NO_BOOSTED_GAME_ID_STUB)
      .also { safeJedisClient.set(getCacheKey(), it.toString(), TEN_MINUTES) }
  }

  private fun getCacheKey() = CACHE_KEY.takeIf { buildVariant == BuildVariant.PRODUCTION } ?: TEST_CACHE_KEY
}