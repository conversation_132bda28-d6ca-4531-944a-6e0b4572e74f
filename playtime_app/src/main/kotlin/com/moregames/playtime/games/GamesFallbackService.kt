package com.moregames.playtime.games

import com.google.inject.Inject
import com.google.inject.Singleton
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.games.fallback.dto.DefaultTextDto
import com.moregames.playtime.games.fallback.dto.FallbackMessageTextDto
import com.moregames.playtime.games.fallback.dto.android.AndroidFallbackApiDto
import com.moregames.playtime.games.fallback.dto.android.AndroidFallbackGameApiDto
import com.moregames.playtime.games.fallback.dto.ios.IosFallbackApiDto
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.ios.dto.IosFallbackGameApiDto
import com.moregames.playtime.translations.TranslationService
import com.moregames.playtime.user.offer.AndroidGameOffer
import com.moregames.playtime.util.DEFAULT_USER_LOCALE
import java.util.*

@Singleton
class GamesFallbackService @Inject constructor(
  private val gamePersistenceService: GamePersistenceService,
  private val translationService: TranslationService,
  private val imageService: ImageService,
  private val marketService: MarketService,
) {

  suspend fun getIosFallback(): List<IosFallbackApiDto> {
    val iosGames = loadIosFallbackGamesList()
    val result = mutableListOf<IosFallbackApiDto>()
    marketService.getAllMarkets().forEach { market ->
      result.add(
        IosFallbackApiDto(
          market = market,
          games = iosGames,
          statusMessage = createFallbackStatusMessage(),
          playGamesTitle = createFallbackTitle()
        )
      )
    }
    return result
  }

  suspend fun getAndroidFallback(): List<AndroidFallbackApiDto> {
    val androidGames = loadAndroidFallbackGamesList()
    val result = mutableListOf<AndroidFallbackApiDto>()
    marketService.getAllMarkets().forEach { market ->
      result.add(
        AndroidFallbackApiDto(
          market = market,
          items = androidGames,
          statusMessage = createFallbackStatusMessage(),
          playGamesTitle = createFallbackTitle()
        )
      )
    }
    return result
  }

  private suspend fun loadAndroidFallbackGamesList() =
    gamePersistenceService.loadVisibleAndroidGimicaGamesOffer(DEFAULT_USER_LOCALE.language)
      .map { game -> translateGame(game, DEFAULT_USER_LOCALE) }
      .map { game -> game2AndroidFallbackGameApiDto(game) }

  private suspend fun loadIosFallbackGamesList() =
    gamePersistenceService.loadVisibleIosGimicaGamesOffer(DEFAULT_USER_LOCALE.language)
      .map { game -> game2IosFallbackGameApiDto(game) }

  private suspend fun translateGame(game: AndroidGameOffer, locale: Locale) =
    with(translationService) {
      game.copy(
        description = tryTranslate(game.description, locale),
        infoTextInstallTop = tryTranslate(game.infoTextInstallTop, locale),
        infoTextInstallBottom = tryTranslate(game.infoTextInstallBottom, locale)
      )
    }

  private fun game2AndroidFallbackGameApiDto(game: AndroidGameOffer) =
    with(game) {
      AndroidFallbackGameApiDto(
        id = game.id,
        activityName = game.activityName,
        title = DefaultTextDto(game.name),
        subtitle = DefaultTextDto(game.description),
        iconUrl = imageService.toUrl(game.iconFilename),
        applicationId = game.applicationId,
        showInstallImage = true,
        installImageUrl = imageService.toUrl(game.installImageFilename),
        infoTextInstallTop = DefaultTextDto(game.infoTextInstallTop),
        infoTextInstallBottom = DefaultTextDto(game.infoTextInstallBottom),
        installationLink = AndroidGameService.GAME_INSTALLATION_LINK_TEMPLATE.format(this.applicationId)
      )
    }

  private suspend fun game2IosFallbackGameApiDto(game: IosGameOffer): IosFallbackGameApiDto {
    val locales = marketService.getAllowedLanguagesForFallback()
    val fallbackGame = IosFallbackGameApiDto(
      id = game.id,
      applicationId = game.applicationId,
      name = mutableMapOf("default" to game.name),
      description = mutableMapOf("default" to translationService.tryTranslate(game.description, DEFAULT_USER_LOCALE)),
      iconFilename = imageService.toUrl(game.iconFilename),
      imageFilename = imageService.toUrl(game.imageFilename),
      infoTextInstall = mutableMapOf("default" to translationService.tryTranslate(game.infoTextInstall, DEFAULT_USER_LOCALE)),
      iosApplicationId = game.iosApplicationId,
      iosGameUrl = game.iosGameUrl,
    )
    locales.forEach { locale ->
      fallbackGame.name[locale.language] = game.name
      fallbackGame.description[locale.language] = translationService.tryTranslate(game.description, locale)
      fallbackGame.infoTextInstall[locale.language] = translationService.tryTranslate(game.infoTextInstall, locale)
    }
    return fallbackGame
  }

  private fun createFallbackStatusMessage(): FallbackMessageTextDto = FallbackMessageTextDto(
    "Service is temporarily unavailable",
    "Service is temporarily unavailable"
  )

  private fun createFallbackTitle(): FallbackMessageTextDto = FallbackMessageTextDto(
    "You still can play our awesome games. All our scientists are already working to resolve the issue",
    "You still can play our awesome games. All our scientists are already working to resolve the issue"
  )
}