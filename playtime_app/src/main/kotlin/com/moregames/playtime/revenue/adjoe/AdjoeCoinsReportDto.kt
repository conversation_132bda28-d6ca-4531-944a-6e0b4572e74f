package com.moregames.playtime.revenue.adjoe

data class AdjoeCoinsReportDto(
  val id: String,
  val userId: String,
  val coins: Int,
  val signature: String,
  val currencyName: String,
  val sdkAppId: String?,
  val deviceId: String?,
  val appId: String?,
  val appName: String?,
  val rewardLevel: Int?,
  val rewardType: String?,
  val uaNetwork: String?,
  val uaChannel: String?,
  val uaSubPublisherEncrypted: String?,
  val uaSubPublisherClearText: String?,
  val placement: String?,
  val publisherSubId1: String?,
  val publisherSubId2: String?,
  val publisherSubId3: String?,
  val publisherSubId4: String?,
  val publisherSubId5: String?,
)

//https://docs.adjoe.io/adjoe-playtime-supply-documentation/android/playtime-sdk-for-android/server-to-server-payouts
//sid = sha1(concatenate(trans_uuid, user_uuid, currency, coin_amount, device_id, sdk_app_id, s2s_token))
//If sdk_app_id and/or device_id are not present in the endpoint URL they will be excluded from the sid calculation
//sid = sha1(concatenate(trans_uuid, user_uuid, currency, coin_amount, s2s_token))
fun AdjoeCoinsReportDto.signatureText(secret: String) = with(this) {
  "$id$userId$currencyName$coins${deviceId.takeIf { it != null } ?: ""}${sdkAppId.takeIf { it != null } ?: ""}$secret"
}
