package com.moregames.playtime.revenue.fyber.exception

import com.moregames.base.exceptions.BaseException
import com.moregames.base.exceptions.ErrorType
import com.moregames.base.exceptions.PlaytimeErrorCodes

class DuplicateFyberTransactionIdException(transactionId: String) :
  BaseException("Duplicate transactionId '$transactionId' for fyber coins report", "Duplicate transaction ID") {
  override val errorCode = PlaytimeErrorCodes.DUPLICATE_FYBER_TRANSACTION_ID
  override val errorType = ErrorType.INPUT_ERROR
}
