package com.moregames.playtime.revenue.bitlabs

import com.google.inject.Singleton
import com.moregames.base.base.BasePersistenceService
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.insertIgnore
import javax.inject.Inject

@Singleton
class BitlabsPersistenceService @Inject constructor(database: Database) : BasePersistenceService(database) {
  suspend fun saveReport(report: BitlabReport): Boolean = dbQuery {
    BitlabsReportsTable.insertIgnore {
      it[transactionId] = report.transactionId
      it[userId] = report.userId
      it[rewardValue] = report.rewardValue
      it[rewardPaid] = report.rewardPaid
      when (report) {
        is BitlabReport.Survey -> {
          it[stateType] = report.state.name
          it[offerType] = BitlabsOfferType.SURVEY
        }

        is BitlabReport.Receipt -> {
          it[stateType] = report.state.name
          it[offerType] = BitlabsOfferType.RECEIPT
        }
      }
    }.insertedCount == 1
  }
}