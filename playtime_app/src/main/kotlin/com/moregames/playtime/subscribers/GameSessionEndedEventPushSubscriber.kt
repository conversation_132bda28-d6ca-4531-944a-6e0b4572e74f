package com.moregames.playtime.subscribers

import com.google.inject.Inject
import com.google.inject.Provider
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.variations.AndroidOnboardingProgressBarVariation
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.messaging.dto.GameSessionEndedBqEventDto
import com.moregames.base.messaging.dto.GameSessionEndedEventDto
import com.moregames.base.messaging.push.GenericPushSubscriber
import com.moregames.base.util.IoCoroutineScope
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.buseffects.SendGameSessionEndedEventEffect
import com.moregames.playtime.games.GamesPlayingTimeTrackingService
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.GameUnlockedNotification
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.offer.OffersOrderService
import com.moregames.playtime.user.offer.lock.AndroidGameUnlockReminderService
import com.moregames.playtime.user.offer.lock.AndroidLockedGamesService
import com.moregames.playtime.user.onboarding.progressbar.buseffects.OnboardingProgressBarTimePlayedEffect
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.launch
import java.time.temporal.ChronoUnit
import kotlin.math.absoluteValue

class GameSessionEndedEventPushSubscriber @Inject constructor(
  private val messageBus: MessageBus,
  private val userService: UserService,
  private val offersOrderService: OffersOrderService,
  private val androidGameUnlockReminderService: AndroidGameUnlockReminderService,
  private val androidLockedGamesService: AndroidLockedGamesService,
  private val coroutineScope: Provider<IoCoroutineScope>,
  private val gamesPlayingTimeTrackingService: GamesPlayingTimeTrackingService,
  private val bigQueryEventPublisher: BigQueryEventPublisher,
  private val abTestingService: AbTestingService,
) : GenericPushSubscriber<GameSessionEndedEventDto>(GameSessionEndedEventDto::class) {

  override suspend fun handle(message: GameSessionEndedEventDto) {
    if (!userService.userExists(message.userId)) return
    handleAmplitudeEvent(message)
    val notification = coroutineScope.get().launch { handleUnlockNotification(message) }
    val timeSpent = coroutineScope.get().launch { gamesPlayingTimeTrackingService.trackTimeSpent(message.userId, message) }
    listOf(notification, timeSpent).joinAll()
    handleCompleteOnboarding(message)
    bigQueryEventPublisher.publish(
      GameSessionEndedBqEventDto(
        market = message.market,
        userId = message.userId,
        gameId = message.gameId,
        sessionStart = message.sessionStart,
        sessionEnd = message.sessionEnd,
        coins = message.coins,
        createdAt = message.createdAt,
      )
    )
  }

  override val url: String
    get() = "game-session-ended-send-to-amplitude"

  private suspend fun handleAmplitudeEvent(message: GameSessionEndedEventDto) {
    with(message) {
      messageBus.publishAsync(
        SendGameSessionEndedEventEffect(
          userId = userId,
          appPlatform = userService.getUser(userId).appPlatform,
          gameId = gameId,
          sessionStart = sessionStart,
          sessionEnd = sessionEnd,
          coins = coins
        )
      )
    }
  }

  private suspend fun handleUnlockNotification(message: GameSessionEndedEventDto) {
    with(message) {
      if (userService.getUser(message.userId).appPlatform != AppPlatform.ANDROID) return
      // if session start was withing 10 minutes of game's first played at - it's unlocking event. otherwise - not
      val userGameCoins = userService.loadUserGameCoins(userId)
      val firstPlayedAt = userGameCoins[message.gameId]?.firstPlayedAt
      if (firstPlayedAt != null && ChronoUnit.MINUTES.between(firstPlayedAt, message.sessionStart).absoluteValue > 10) return

      val recentUnlockedGame = offersOrderService.loadRecentUnlockedGame(userId, userGameCoins)
      if (recentUnlockedGame != null && androidGameUnlockReminderService.getNotificationSentForGame(userId) != recentUnlockedGame.id) {
        messageBus.publishAsync(
          PushNotificationEffect(
            notification = GameUnlockedNotification(
              userId = userId,
              gameName = recentUnlockedGame.name,
              widgetId = androidLockedGamesService.unlockedAndLockedGamesInfoConfig.unlockedGameWidgetId.toString(),
            )
          )
        )
        androidGameUnlockReminderService.storeNotificationSent(userId, recentUnlockedGame.id)
      }
    }
  }

  private suspend fun handleCompleteOnboarding(message: GameSessionEndedEventDto) {
    with(message) {
      if (abTestingService.assignedVariationValue(userId, ClientExperiment.ANDROID_ONBOARDING_PROGRESS_BAR) != AndroidOnboardingProgressBarVariation.Enhanced) return
      val overallTimeSpent = gamesPlayingTimeTrackingService.getTimeSpent(userId, gameId)
      messageBus.publishAsync(
        OnboardingProgressBarTimePlayedEffect(
          userId = userId,
          gameId = gameId,
          overallTimeSpent = overallTimeSpent,
        )
      )
    }
  }
}
