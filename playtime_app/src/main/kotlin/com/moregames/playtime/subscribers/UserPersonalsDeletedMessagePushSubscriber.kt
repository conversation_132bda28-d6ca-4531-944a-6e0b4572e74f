package com.moregames.playtime.subscribers

import com.google.inject.Inject
import com.moregames.base.messaging.dto.UserPersonalsDeletedEventDto
import com.moregames.base.messaging.push.GenericPushSubscriber
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutService
import com.moregames.playtime.user.fraudscore.FraudScoreService
import com.moregames.playtime.user.verification.VerificationService

class UserPersonalsDeletedMessagePushSubscriber @Inject constructor(
  private val userService: UserService,
  private val fraudScoreService: FraudScoreService,
  private val verificationService: VerificationService,
  private val cashoutService: CashoutService,
) : GenericPushSubscriber<UserPersonalsDeletedEventDto>(UserPersonalsDeletedEventDto::class) {

  override suspend fun handle(message: UserPersonalsDeletedEventDto) {
    userService.markUserAsDeleted(message.userId)

    fraudScoreService.obfuscateFraudScorePersonals(message.userId)
    userService.obfuscateUserGaidTrackingPersonals(message.userId)
    verificationService.disassociateSession(message.userId)

    // should be later, as removes email (used when email passed as argument)
    cashoutService.obfuscateUserCashoutTransactionPersonals(message.userId)
    // should be last, as removes googleAdId for future runs
    userService.obfuscateUserDataPersonals(message.userId)
  }

  override val url: String
    get() = "personals-deleted"

}