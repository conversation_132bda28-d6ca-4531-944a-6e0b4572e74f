package com.moregames.playtime.vpn

import com.google.inject.Inject
import com.moregames.base.base.BasePersistenceService
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.select

class VpnCheckPersistenceService @Inject constructor(database: Database) : BasePersistenceService(database) {

  suspend fun isIpBlacklisted(ip: String) =
    dbQuery {
      IpBlacklistTable.select { IpBlacklistTable.ip eq ip }.count() > 0
    }
}
