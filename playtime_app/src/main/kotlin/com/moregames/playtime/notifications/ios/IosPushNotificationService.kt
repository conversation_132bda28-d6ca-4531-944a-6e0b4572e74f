package com.moregames.playtime.notifications.ios

import com.google.inject.Inject
import com.google.inject.Singleton
import com.moregames.base.messaging.customnotification.CustomNotificationDto
import com.moregames.base.messaging.customnotification.CustomNotificationType.*
import com.moregames.base.messaging.customnotification.OnClickActionApiDto
import com.moregames.base.util.ClientVersionsSupport.IOS_BALANCE_UPDATE_EXPERIENCE_MIN_APP_VERSION
import com.moregames.base.util.RandomGenerator
import com.moregames.base.util.format
import com.moregames.playtime.notifications.NotificationsFacade
import com.moregames.playtime.notifications.PushNotification.CrossPlatformPushNotification.*
import com.moregames.playtime.notifications.PushNotification.IosPushNotification.PlayFirstGameReminder
import com.moregames.playtime.notifications.PushNotification.IosPushNotification.ShareYourExperienceNotification
import com.moregames.playtime.translations.TranslationResource
import com.moregames.playtime.translations.TranslationResource.*
import com.moregames.playtime.translations.UntranslatedStringsStorage.BALANCE_UPDATE_BODY_NO_COINS
import com.moregames.playtime.translations.UntranslatedStringsStorage.BALANCE_UPDATE_BODY_NO_COINS_IOS_EXP
import com.moregames.playtime.translations.UntranslatedStringsStorage.BALANCE_UPDATE_IOS_BOOSTED_GAME
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.util.roundDownToSecondDigit
import java.text.NumberFormat
import java.util.*

@Singleton
class IosPushNotificationService @Inject constructor(
  private val translationService: UserTranslationService,
  private val notificationsFacade: NotificationsFacade,
  private val randomGenerator: RandomGenerator,
) {

  suspend fun sendPlayFirstGameReminder(notification: PlayFirstGameReminder) = with(notification) {
    notificationsFacade.sendMessage(
      userId = userId,
      message = CustomNotificationDto(
        notificationId = randomGenerator.nextUUID(),
        notificationType = IOS_REMIND_TO_PLAY_NOTIFICATION,
        shortDescription = "To collect your first Justplay points try out one of those games",
        label = label
      )
    )
  }

  suspend fun sendBalanceUpdatedNotification(notification: BalanceUpdatedNotification, locale: Locale, appVersion: Int) = with(notification) {

    suspend fun TranslationResource.translate() =
      translationService.translateOrDefault(this, locale, userId).replace("{coins}", NumberFormat.getNumberInstance(locale).format(coins))

    val shortDescription = when {
      hideCoins && appVersion < IOS_BALANCE_UPDATE_EXPERIENCE_MIN_APP_VERSION -> BALANCE_UPDATE_BODY_NO_COINS
      hideCoins -> BALANCE_UPDATE_BODY_NO_COINS_IOS_EXP
      appVersion < IOS_BALANCE_UPDATE_EXPERIENCE_MIN_APP_VERSION -> IOS_NOTIFICATION_BALANCE_UPDATED_DESCRIPTION.translate()
      isIosBoostedGameCoins -> BALANCE_UPDATE_IOS_BOOSTED_GAME.replace("{coins}", NumberFormat.getNumberInstance(locale).format(coins))
      else -> IOS_NOTIFICATION_BALANCE_UPDATED_EXP_DESCRIPTION.translate()
    }

    notificationsFacade.sendMessage(
      userId = userId,
      message = CustomNotificationDto(
        notificationId = randomGenerator.nextUUID(),
        notificationType = IOS_COINS_BALANCE_UPDATED,
        title = "JustPlay",
        shortDescription = shortDescription,
        label = label
      )
    )
  }

  suspend fun sendCashoutProcessedNotification(notification: CashoutProcessedNotification, locale: Locale) =
    notificationsFacade.sendMessage(
      userId = notification.userId,
      message = CustomNotificationDto(
        notificationId = randomGenerator.nextUUID(),
        notificationType = IOS_CASHOUT_PROCESSED,
        title = translationService.translateOrDefault(IOS_NOTIFICATION_CASHOUT_PROCESSED_TITLE, locale, notification.userId),
        shortDescription = translationService.translateOrDefault(IOS_NOTIFICATION_CASHOUT_PROCESSED_DESCRIPTION, locale, notification.userId),
        label = notification.label
      )
    )

  suspend fun sendShareYourExperienceNotification(notification: ShareYourExperienceNotification) = with(notification) {
    notificationsFacade.sendMessage(
      userId = userId,
      message = CustomNotificationDto(
        notificationId = randomGenerator.nextUUID(),
        notificationType = IOS_SHARE_YOUR_EXPERIENCE,
        title = "Share your opinion for a reward",
        shortDescription = "Tell us about your gaming experience and get a \$50 Amazon gift card. Tap to check it out!",
        label = label
      )
    )
  }

  suspend fun sendEarningsAddedNotification(notification: EarningsAddedNotification, locale: Locale) = with(notification) {
    val formattedAmount = earnings.userCurrencyAmount.roundDownToSecondDigit().format(earnings.userCurrency)
    CustomNotificationDto(
      notificationId = randomGenerator.nextUUID(),
      notificationType = IOS_EARNINGS_ADDED,
      title = translationService.translateOrDefault(IOS_NOTIFICATION_EARNING_ADDED_TITLE, locale, userId)
        .replace("{formatted_amount}", "$formattedAmount ${earnings.userCurrency.currencyCode}"),
      shortDescription = translationService.translateOrDefault(IOS_NOTIFICATION_EARNING_ADDED_DESCRIPTION, locale, userId),
      onClickAction = OnClickActionApiDto.routeToCashout(),
      label = label
    ).let {
      notificationsFacade.sendMessage(userId, it)
    }
  }

  suspend fun sendRatingPromptCommand(notification: RatingPromptCommand) = with(notification) {
    notificationsFacade.sendMessage(
      userId = userId,
      message = CustomNotificationDto(
        notificationId = randomGenerator.nextUUID(),
        notificationType = IOS_RATING_PROMPT,
        label = label
      )
    )
  }

  suspend fun sendGameCoinGoalReachedNotification(notification: GameCoinGoalReachedNotification) = with(notification) {
    notificationsFacade.sendMessage(
      userId = userId,
      message = CustomNotificationDto(
        notificationId = randomGenerator.nextUUID(),
        notificationType = IOS_REACH_GAME_COIN_GOAL,
        title = title,
        shortDescription = text,
        label = label
      )
    )
  }

  suspend fun sendUnclaimedEarningNotification(notification: UnclaimedEarningNotification) = with(notification) {
    notificationsFacade.sendMessage(
      userId = userId,
      message = CustomNotificationDto(
        notificationId = randomGenerator.nextUUID(),
        notificationType = IOS_EARNINGS_ADDED,
        title = title,
        shortDescription = text,
        label = label
      )
    )
  }

}

