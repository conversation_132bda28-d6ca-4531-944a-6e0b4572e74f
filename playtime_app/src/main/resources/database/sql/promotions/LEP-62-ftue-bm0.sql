insert into playtime.boosted_mode_preset (id, coins_coefficient, earnings_coefficient, ui_config)
values (
           'ftue_exp_bm0_1p5',
        1.5,
        1,
        '
{
  "mainScreenHintTranslation": "<big><font color=\'#F2C94C\'>Newcomer DOUBLE Payouts!</font></big><br><br><big>Get DOUBLE payouts for every coin you collect for today only!</big>",
  "topLeftCoinsReplacementTranslation": "Boosted Coins",
  "earnPlayingGamesReplacementTranslation": "Boosted Games:",
  "balanceUpdate": {
    "titleTranslation": "$_balance_update_notification_title",
    "descriptionTranslation": "You now have %s Boosted Coins!",
    "descriptionNoCoinsTranslation": "Keep collecting your Boosted Coins!"
  },
  "readyToCashout": {
    "titleTranslation": "Your Boosted Payout is READY!",
  }
}
'
) as v
on duplicate key update coins_coefficient = v.coins_coefficient,
                        earnings_coefficient = v.earnings_coefficient,
                        ui_config = v.ui_config;


insert into playtime.boosted_mode_preset (id, coins_coefficient, earnings_coefficient, ui_config)
    values (
               'ftue_exp_bm0_2p0',
               2,
               1.25,
               '
       {
         "mainScreenHintTranslation": "<big><font color=\'#F2C94C\'>Newcomer DOUBLE Payouts!</font></big><br><br><big>Get DOUBLE payouts for every coin you collect for today only!</big>",
         "topLeftCoinsReplacementTranslation": "Boosted Coins",
         "earnPlayingGamesReplacementTranslation": "Boosted Games:",
         "balanceUpdate": {
           "titleTranslation": "$_balance_update_notification_title",
           "descriptionTranslation": "You now have %s Boosted Coins!",
           "descriptionNoCoinsTranslation": "Keep collecting your Boosted Coins!"
         },
         "readyToCashout": {
           "titleTranslation": "Your Boosted Payout is READY!",
         }
       }
       '
           ) as v
on duplicate key update coins_coefficient = v.coins_coefficient,
                        earnings_coefficient = v.earnings_coefficient,
                        ui_config = v.ui_config;