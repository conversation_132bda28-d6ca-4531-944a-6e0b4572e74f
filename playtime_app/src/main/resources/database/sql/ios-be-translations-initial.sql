-- resources
INSERT INTO playtime.string_resource (resource_name) VALUES
('$_ios_treasuremaster_description'),
('$_ios_ballbounce_description'),
('$_ios_solitaireverse_description'),
('$_ios_zentiles_description'),
('$_ios_bubblepop_description'),
('$_ios_sugarmatch_description'),
('$_ios_mergeblast_description'),
('$_ios_madsmash_description'),
('$_ios_wordseeker_description'),
('$_ios_mixblox_description'),
('$_ios_watersorter_description'),
('$_ios_fairytalematch_description'),
('$_ios_text_install_top'),
('$_ios_notification_balance_updated_description'),
('$_ios_notification_cashout_processed_title'),
('$_ios_notification_cashout_processed_description'),
('$_ios_notification_earnings_added_title'),
('$_ios_notification_earnings_added_description'),
('$_ios_news_1_title'),
('$_ios_news_1_text'),
('$_ios_news_2_title'),
('$_ios_news_2_text'),
('$_ios_news_3_title'),
('$_ios_news_3_text'),
('$_ios_news_4_title'),
('$_ios_news_4_text'),
('$_ios_news_5_title'),
('$_ios_news_5_text'),
('$_ios_news_6_title'),
('$_ios_news_6_text'),
('$_ios_news_7_title'),
('$_ios_news_7_text')
ON DUPLICATE KEY UPDATE resource_name = VALUES(resource_name);

-- en translations
INSERT INTO playtime.string_resource_translation (resource_name, language, translation) VALUES
('$_ios_treasuremaster_description', 'en', 'Defeat monsters for <b>loyalty points!</b>'),
('$_ios_ballbounce_description', 'en', 'Collect pickups for <b>loyalty points!<b>'),
('$_ios_solitaireverse_description', 'en', 'Play to earn <b>loyalty points!</b>'),
('$_ios_zentiles_description', 'en', 'Enter a state of bliss and earn <b>loyalty coins!</b>'),
('$_ios_bubblepop_description', 'en', 'Solve puzzles and rescue the animals for <b>loyalty coins!</b>'),
('$_ios_sugarmatch_description', 'en', 'Match 3 to make sweet treats for <b>loyalty coins!</b>'),
('$_ios_mergeblast_description', 'en', 'Have a blast and merge blocks to survive as long as you can for <b>loyalty coins!</b>'),
('$_ios_madsmash_description', 'en', 'Smash through level after level for <b>loyalty coins!</b>'),
('$_ios_wordseeker_description', 'en', 'Solve word puzzles for <b>loyalty coins!</b>'),
('$_ios_mixblox_description', 'en', 'Make number matches for <b>loyalty coins!</b>'),
('$_ios_watersorter_description', 'en', 'Sort all the colors for <b>loyalty coins!</b>'),
('$_ios_fairytalematch_description', 'en', 'Take on the heat of the Word Kitchen and discover words for <b>loyalty coins!</b>'),
('$_ios_text_install_top', 'en', 'Play'),
('$_ios_notification_balance_updated_description', 'en', 'You now have {coins} loyalty points!'),
('$_ios_notification_cashout_processed_title', 'en', 'Congratulations!'),
('$_ios_notification_cashout_processed_description', 'en', 'Your reward is ready!'),
('$_ios_notification_earnings_added_title', 'en', '{formatted_amount} added to your JustPlay balance'),
('$_ios_notification_earnings_added_description', 'en', 'Congratulations! Now you can withdraw your JustPlay reward!'),
('$_ios_news_1_title', 'en', 'New! Unleash Explosive Power-Ups: Amplify Your Block-Destroying Abilities!'),
('$_ios_news_1_text', 'en', 'Discover explosive power-ups that amplify your block-destroying abilities. Unleash devastating effects such as bomb balls, multi-shot bursts, or laser-guided precision shots to obliterate blocks in spectacular fashion. Prepare for explosive gameplay like never before!'),
('$_ios_news_2_title', 'en', 'Special White Ball Hunt: Seek Hidden Treasures for Extra Firepower!'),
('$_ios_news_2_text', 'en', 'Embark on a quest to find the elusive white balls scattered among the number blocks. Shoot with precision to claim these hidden treasures and witness your firepower grow exponentially. The more white balls you gather, the greater your destructive potential!'),
('$_ios_news_3_title', 'en', 'Daily Brain Training: Exercise Your Mind with Wooden Puzzle Bliss!'),
('$_ios_news_3_text', 'en', 'Boost your cognitive abilities and improve your mental acuity with daily brain training sessions using Wooden Puzzle Bliss. Challenge your logic skills, sharpen your problem-solving abilities, and unlock the full potential of your brain in a relaxing and enjoyable way.'),
('$_ios_news_4_title', 'en', 'New Puzzle Packs Unlocked: Explore Endless Challenging Bliss!'),
('$_ios_news_4_text', 'en', 'Dive into a world of endless puzzle bliss with new puzzle packs! Test your logic skills with a variety of challenging puzzles that will keep you engaged for hours. Can you solve them all and reach new levels of blissful satisfaction?'),
('$_ios_news_5_title', 'en', 'Personalize Your Solitaire Experience: Customizable Cards and Backgrounds!'),
('$_ios_news_5_text', 'en', 'Make Solitaire Verse truly yours with customisable cards and backgrounds. Select your favourite card designs and set the perfect backdrop to enhance your solitaire game. Let your personality shine through as you play!'),
('$_ios_news_6_title', 'en', 'Epic Boss Battles Await: Unleash Your Archery Skills!'),
('$_ios_news_6_text', 'en', 'Engage in adrenaline-pumping boss battles where the precise aim is crucial. Test your archery skills and take down formidable foes with well-placed arrow shots. Can you defeat the toughest bosses and claim legendary rewards?'),
('$_ios_news_7_title', 'en', 'New monsters are added to Treasure Master!'),
('$_ios_news_7_text', 'en', 'New monsters have been added to one of our best games! Welcome “The Mummy”, “The Dragon”, and “The Skeleton”! To enjoy the new monsters, make sure your version of Treasure Master is up to date.')
ON DUPLICATE KEY UPDATE translation = VALUES(translation);

-- fill other languages
INSERT INTO playtime.string_resource_translation (resource_name, language, translation)
    (WITH res AS (SELECT *
                  FROM playtime.string_resource_translation
                  WHERE resource_name like '$\_ios\_%'
                    AND language = 'en'),
          lang AS (SELECT DISTINCT language FROM playtime.string_resource_translation WHERE language != 'en')
     SELECT res.resource_name,
            lang.language,
            res.translation
     FROM res
              LEFT JOIN lang ON 1 = 1);

-- apply to games
UPDATE playtime.games SET description = '$_ios_treasuremaster_description' WHERE platform = 'IOS' and application_id = 'com.gimica.treasuremaster';
UPDATE playtime.games SET description = '$_ios_solitaireverse_description' WHERE platform = 'IOS' and application_id = 'com.gimica.solitaireverse';
UPDATE playtime.games SET description = '$_ios_ballbounce_description' WHERE platform = 'IOS' and application_id = 'com.gimica.ballbounce';
UPDATE playtime.games SET description = '$_ios_zentiles_description' WHERE platform = 'IOS' and application_id = 'com.gimica.zentiles';
UPDATE playtime.games SET description = '$_ios_bubblepop_description' WHERE platform = 'IOS' and application_id = 'com.gimica.bubblepop';
UPDATE playtime.games SET description = '$_ios_sugarmatch_description' WHERE platform = 'IOS' and application_id = 'com.gimica.sugarmatch';
UPDATE playtime.games SET description = '$_ios_mergeblast_description' WHERE platform = 'IOS' and application_id = 'com.gimica.mergeblast';
UPDATE playtime.games SET description = '$_ios_madsmash_description' WHERE platform = 'IOS' and application_id = 'com.gimica.madsmash';
UPDATE playtime.games SET description = '$_ios_wordseeker_description' WHERE platform = 'IOS' and application_id = 'com.gimica.wordseeker';
UPDATE playtime.games SET description = '$_ios_mixblox_description' WHERE platform = 'IOS' and application_id = 'com.gimica.mixblox';
UPDATE playtime.games SET description = '$_ios_watersorter_description' WHERE platform = 'IOS' and application_id = 'com.gimica.watersorter';
UPDATE playtime.games SET description = '$_ios_fairytalematch_description' WHERE platform = 'IOS' and application_id = 'com.gimica.fairytalematch';