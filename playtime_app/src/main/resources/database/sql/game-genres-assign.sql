UPDATE playtime.games
SET genre_id = CASE
                   WHEN application_id = 'com.gimica.hexapuzzlefun'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'MATCHING')
                   WHEN application_id = 'com.gimica.blockslider'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'MATCHING')
                   WHEN application_id = 'com.gimica.bubblepop'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'MATCHING')
                   WHEN application_id = 'com.gimica.colorlogic'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'MATCHING')
                   WHEN application_id = 'com.gimica.mixblox'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'MATCHING')
                   WHEN application_id = 'com.gimica.puzzlepopblaster'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'MATCHING')
                   WHEN application_id = 'com.gimica.sugarmatch'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'MATCHING')
                   WHEN application_id = 'com.gimica.zentiles'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'MATCHING')
                   WHEN application_id = 'com.gimica.hexmatch'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'CASUAL')
                   WHEN application_id = 'com.gimica.maketen'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'CASUAL')
                   WHEN application_id = 'com.gimica.watersorter'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'CASUAL')
                   WHEN application_id = 'com.gimica.wordkitchen'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'CASUAL')
                   WHEN application_id = 'com.gimica.wordseeker'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'CASUAL')
                   WHEN application_id = 'com.gimica.solitaireverse'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'CASUAL')
                   WHEN application_id = 'com.gimica.sudoku'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'CASUAL')
                   WHEN application_id = 'com.gimica.triviamadness'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'CASUAL')
                   WHEN application_id = 'com.gimica.madsmash'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'ACTION')
                   WHEN application_id = 'com.gimica.treasuremaster'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'ACTION')
                   WHEN application_id = 'com.gimica.ballbounce'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'ACTION')
                   WHEN application_id = 'com.gimica.blockholeclash'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'ACTION')
                   WHEN application_id = 'com.gimica.marblemadness'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'ACTION')
                   WHEN application_id = 'com.gimica.mergeblast'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'ACTION')
                   WHEN application_id = 'com.cocomagic.mergecarsdefense'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'IDLE')
                   WHEN application_id = 'com.gimica.crystalcrush'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'IDLE')
                   WHEN application_id = 'com.gimica.emojiclickers'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'IDLE')
                   WHEN application_id = 'com.gimica.idlemergefun'
                       THEN (SELECT id FROM playtime.game_genre WHERE type = 'IDLE')
                   else (SELECT id FROM playtime.game_genre WHERE type = 'NONE') end;
