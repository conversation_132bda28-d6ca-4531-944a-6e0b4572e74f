-- first - prefill existing demo games
INSERT INTO playtime.games_webgl (game_id, webgl_url, demo_for_game_id)
VALUES (200073, 'TBD', 200039),
       (500016, 'TBD', 500001); -- Treasure Master demo

-- second - add new games. only IOS for now, and all are hidden until we determine their show logics
INSERT INTO playtime.games (id, application_id, name, description, icon_filename, image_filename, order_key, applovin_api_key, activity_name, install_image_filename, info_text_install_top, info_text_install_bottom, do_not_show, publisher_id, ios_application_id, ios_game_url, platform)
select 500017                         as id,
       'com.gimica.ballbounce.webgl'  as application_id,
       'Balls Versus Blocks'          as name,
       '$_ios_ballbounce_description' as description,
       'ball_bounce.jpg'              as icon_filename,
       'ball_bounce_preview.jpg'      as image_filename,
       500017                         as order_key,
       'gimica-api-key'               as applovin_api_key,
       ''                             as activity_name,
       ''                             as install_image_filename,
       '$_ios_text_install_top'       as info_text_install_top,
       ''                             as info_text_install_bottom,
       1                              as do_not_show,
       4                              as publisher_id,
       'not applicable'               as ios_application_id,
       'not applicable'               as ios_game_url,
       'IOS'                          as platform;

INSERT INTO playtime.games (id, application_id, name, description, icon_filename, image_filename, order_key, applovin_api_key, activity_name, install_image_filename, info_text_install_top, info_text_install_bottom, do_not_show, publisher_id, ios_application_id, ios_game_url, platform)
select 500018                        as id,
       'com.gimica.bubblepop.webgl'  as application_id,
       'Bubble Pop: Wild Rescue'     as name,
       '$_ios_bubblepop_description' as description,
       'bubble_pop_icon.jpg'         as icon_filename,
       'bubble_pop_bg.jpg'           as image_filename,
       500018                        as order_key,
       'gimica-api-key'              as applovin_api_key,
       ''                            as activity_name,
       ''                            as install_image_filename,
       '$_ios_text_install_top'      as info_text_install_top,
       ''                            as info_text_install_bottom,
       1                             as do_not_show,
       4                             as publisher_id,
       'not applicable'              as ios_application_id,
       'not applicable'              as ios_game_url,
       'IOS'                         as platform;

INSERT INTO playtime.games (id, application_id, name, description, icon_filename, image_filename, order_key, applovin_api_key, activity_name, install_image_filename, info_text_install_top, info_text_install_bottom, do_not_show, publisher_id, ios_application_id, ios_game_url, platform)
select 500019                       as id,
       'com.gimica.madsmash.webgl'  as application_id,
       'Mad Smash'                  as name,
       '$_ios_madsmash_description' as description,
       'mad_smash.jpg'              as icon_filename,
       'mad_smash_preview.jpg'      as image_filename,
       500019                       as order_key,
       'gimica-api-key'             as applovin_api_key,
       ''                           as activity_name,
       ''                           as install_image_filename,
       '$_ios_text_install_top'     as info_text_install_top,
       ''                           as info_text_install_bottom,
       1                            as do_not_show,
       4                            as publisher_id,
       'not applicable'             as ios_application_id,
       'not applicable'             as ios_game_url,
       'IOS'                        as platform;

INSERT INTO playtime.games (id, application_id, name, description, icon_filename, image_filename, order_key, applovin_api_key, activity_name, install_image_filename, info_text_install_top, info_text_install_bottom, do_not_show, publisher_id, ios_application_id, ios_game_url, platform)
select 500020                             as id,
       'com.gimica.solitaireverse.webgl'  as application_id,
       'Solitaire Verse'                  as name,
       '$_ios_solitaireverse_description' as description,
       'solitaire_verse_green.jpg'        as icon_filename,
       'solitaire_verse_preview.jpg'      as image_filename,
       500020                             as order_key,
       'gimica-api-key'                   as applovin_api_key,
       ''                                 as activity_name,
       ''                                 as install_image_filename,
       '$_ios_text_install_top'           as info_text_install_top,
       ''                                 as info_text_install_bottom,
       1                                  as do_not_show,
       4                                  as publisher_id,
       'not applicable'                   as ios_application_id,
       'not applicable'                   as ios_game_url,
       'IOS'                              as platform;

INSERT INTO playtime.games (id, application_id, name, description, icon_filename, image_filename, order_key, applovin_api_key, activity_name, install_image_filename, info_text_install_top, info_text_install_bottom, do_not_show, publisher_id, ios_application_id, ios_game_url, platform)
select 500021                           as id,
       'com.gimica.sugarmatch.webgl'    as application_id,
       'Chef''s Quest: Match Sensation' as name,
       '$_ios_sugarmatch_description'   as description,
       'ios_chefs_quest_icon.jpg'       as icon_filename,
       'ios_chefs_quest_image.jpg'      as image_filename,
       500021                           as order_key,
       'gimica-api-key'                 as applovin_api_key,
       ''                               as activity_name,
       ''                               as install_image_filename,
       '$_ios_text_install_top'         as info_text_install_top,
       ''                               as info_text_install_bottom,
       1                                as do_not_show,
       4                                as publisher_id,
       'not applicable'                 as ios_application_id,
       'not applicable'                 as ios_game_url,
       'IOS'                            as platform;

INSERT INTO playtime.games (id, application_id, name, description, icon_filename, image_filename, order_key, applovin_api_key, activity_name, install_image_filename, info_text_install_top, info_text_install_bottom, do_not_show, publisher_id, ios_application_id, ios_game_url, platform)
select 500022                             as id,
       'com.gimica.treasuremaster.webgl'  as application_id,
       'Treasure Masters'                 as name,
       '$_ios_treasuremaster_description' as description,
       'treasure_master.png'              as icon_filename,
       'treasure_master_preview.jpg'      as image_filename,
       500022                             as order_key,
       'gimica-api-key'                   as applovin_api_key,
       ''                                 as activity_name,
       ''                                 as install_image_filename,
       '$_ios_text_install_top'           as info_text_install_top,
       ''                                 as info_text_install_bottom,
       1                                  as do_not_show,
       4                                  as publisher_id,
       'not applicable'                   as ios_application_id,
       'not applicable'                   as ios_game_url,
       'IOS'                              as platform;

INSERT INTO playtime.games (id, application_id, name, description, icon_filename, image_filename, order_key, applovin_api_key, activity_name, install_image_filename, info_text_install_top, info_text_install_bottom, do_not_show, publisher_id, ios_application_id, ios_game_url, platform)
select 500023                            as id,
       'com.gimica.zentiles.webgl'       as application_id,
       'Wooden Puzzle Bliss'             as name,
       '$_ios_zentiles_description'      as description,
       'wooden_puzzle_bliss_icon.jpg'    as icon_filename,
       'wooden_puzzle_bliss_preview.jpg' as image_filename,
       500023                            as order_key,
       'gimica-api-key'                  as applovin_api_key,
       ''                                as activity_name,
       ''                                as install_image_filename,
       '$_ios_text_install_top'          as info_text_install_top,
       ''                                as info_text_install_bottom,
       1                                 as do_not_show,
       4                                 as publisher_id,
       'not applicable'                  as ios_application_id,
       'not applicable'                  as ios_game_url,
       'IOS'                             as platform;

-- third - add webgl stubs
INSERT INTO playtime.games_webgl (game_id, webgl_url, demo_for_game_id)
VALUES (500017, 'TBD', null), -- Balls Versus Blocks
       (500018, 'TBD', null), -- Bubble Pop: Wild Rescue
       (500019, 'TBD', null), -- Mad Smash
       (500020, 'TBD', null), -- Solitaire Verse
       (500021, 'TBD', null), -- Chef's Quest: Match Sensation
       (500022, 'TBD', null), -- Treasure Masters
       (500023, 'TBD', null); -- Wooden Puzzle Bliss