-- Names

insert into playtime.string_resource (resource_name)
    values
        ('$_challenge_atlantis_bounce_complete') as v
on duplicate key update resource_name = v.resource_name;



-- Values

insert into playtime.string_resource_translation (resource_name, language, translation)
    values

        -- -------------------- $_challenge_atlantis_bounce_complete -----------------------------
        ('$_challenge_atlantis_bounce_complete', 'en', 'Collect %s ($) tokens in Atlantis Bounce. Aim at the ($) tokens to collect!'),
        ('$_challenge_atlantis_bounce_complete', 'de', 'Sammle %s ($)-Token in Atlantis Bounce. Ziele auf die ($)-Token, um sie zu sammeln!'),
        ('$_challenge_atlantis_bounce_complete', 'es', 'Recoge %s ($) fichas en Atlantis Bounce. Apunta a las fichas ($) para recogerlas.'),
        ('$_challenge_atlantis_bounce_complete', 'es-mx', 'Recoge %s ($) fichas en Atlantis Bounce. Apunta a las fichas ($) para recogerlas.'),
        ('$_challenge_atlantis_bounce_complete', 'fr', 'Collecte %s jetons ($) dans Atlantis Bounce. Vise les jetons ($) pour les collecter !'),
        ('$_challenge_atlantis_bounce_complete', 'it', 'Raccogli %s gettoni ($) in Atlantis Bounce. Mira ai gettoni ($) per raccoglierli!'),
        ('$_challenge_atlantis_bounce_complete', 'ja', 'Atlantis Bounceで%s個の($)トークンを集めよう。($)トークンを狙ってゲット！'),
        ('$_challenge_atlantis_bounce_complete', 'ko', 'Atlantis Bounce에서 %s개의 ($) 토큰을 모으세요. ($) 토큰을 조준하여 수집하세요!'),
        ('$_challenge_atlantis_bounce_complete', 'nl', 'Verzamel %s ($)-tokens in Atlantis Bounce. Richt op de ($)-tokens om ze te verzamelen!'),
        ('$_challenge_atlantis_bounce_complete', 'pl', 'Zbierz %s żetonów ($) w Atlantis Bounce. Celuj w żetony ($), aby je zebrać!'),
        ('$_challenge_atlantis_bounce_complete', 'pt', 'Colete %s tokens ($) em Atlantis Bounce. Mire nos tokens ($) para coletá-los!'),
        ('$_challenge_atlantis_bounce_complete', 'pt-br', 'Colete %s tokens ($) em Atlantis Bounce. Mire nos tokens ($) para coletá-los!'),
        ('$_challenge_atlantis_bounce_complete', 'zh-hk', '在 Atlantis Bounce 收集 %s 個 ($) 代幣。瞄準 ($) 代幣來收集！'),
        ('$_challenge_atlantis_bounce_complete', 'zh-tw', '在 Atlantis Bounce 收集 %s 個 ($) 代幣。瞄準 ($) 代幣來收集！')
        as v on duplicate key update translation = v.translation;

-- APP TRANSLATIONS

