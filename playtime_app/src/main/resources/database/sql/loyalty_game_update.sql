UPDATE playtime.games dst
  JOIN playtime.games src ON src.id = dst.id
SET dst.info_text_install_top = concat('Play ', src.name),
    dst.info_text_install = concat('Play ', src.name, '<br><br>', src.description)
WHERE dst.info_text_install_top NOT LIKE '$_%'
  AND dst.info_text_install NOT LIKE '$_%'
  AND dst.application_id != 'com.justplay.app'
  AND dst.platform = 'ANDROID';

UPDATE playtime.games dst
    JOIN playtime.games src ON src.id = dst.id
    JOIN (
          SELECT 'com.gimica.treasuremaster' AS application_id, 'Defeat monsters for loyalty coins' AS description
UNION ALL SELECT 'com.gimica.madsmash' AS application_id, 'Smash through level after level for loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.mixblox' AS application_id, 'Make number matches for loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.ballbounce' AS application_id, 'Destroy the blocks and collect pick ups for loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.wordseeker' AS application_id, 'Solve word puzzles for loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.solitaireverse' AS application_id, 'The more games you win the more loyalty coins you get' AS descr
UNION ALL SELECT 'com.gimica.hexapuzzlefun' AS application_id, 'Reach 2048 to get more loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.puzzlepopblaster' AS application_id, 'Match colored blocks to make loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.idlemergefun' AS application_id, 'Merge your way to improved production and loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.triviamadness' AS application_id, 'Put your general knowledge to the test and earn loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.mergeblast' AS application_id, 'Have a blast and merge blocks to survive as long as you can for loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.emojiclickers' AS application_id, 'Tap and bounce your way to more loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.hexmatch' AS application_id, 'Solve hexagonal puzzles for loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.zentiles' AS application_id, 'Enter a state of bliss and earn loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.sugarmatch' AS application_id, 'Match 3 to make sweet treats for loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.carsmerge' AS application_id, 'Merge and build your team to defeat your enemies for loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.blockholeclash' AS application_id, 'Remove the correct blocks for loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.bubblepop' AS application_id, 'Solve puzzles and rescue the animals for loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.blockslider' AS application_id, 'Survive the wall by making complete rows and earn loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.marblemadness' AS application_id, 'Survive Aztec trials for loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.sudoku' AS application_id, 'Earn loyalty coins and relax with a classic' AS descr
UNION ALL SELECT 'com.gimica.brickdoku' AS application_id, 'Sudoku X Block Puzzle! Complete rows, columns and squares for loyalty coins' AS descr
UNION ALL SELECT 'com.gimica.wordkitchen' AS application_id, 'Take on the heat of the Word Kitchen and discover words for loyalty coins' AS descr
    ) updated_description
    ON updated_description.application_id = src.application_id
SET dst.description = updated_description.description,
    dst.info_text_install = concat('Play ', src.name, '<br><br>', updated_description.description),
    dst.info_text_play = concat('Play ', src.name, '<br><br>', updated_description.description),
    dst.info_text_install_bottom = updated_description.description
WHERE dst.description NOT LIKE '$_%'
  AND dst.info_text_install NOT LIKE '$_%'
  AND dst.info_text_play NOT LIKE '$_%'
  AND dst.info_text_install_bottom NOT LIKE '$_%'
  AND dst.platform = 'ANDROID'
;
