package com.justplayapps.service.reporting.revenue.applovin

import com.google.inject.Inject
import com.justplayapps.service.reporting.revenue.applovin.dto.ApplovinRequest
import com.justplayapps.service.reporting.revenue.applovin.dto.MetaApplovinRevenue
import com.justplayapps.service.reporting.serviceclient.PlaytimeServiceClient
import com.moregames.base.secret.SecretService
import com.moregames.base.util.cronLogger
import java.time.LocalDate
import javax.inject.Singleton

@Singleton
class RevenueImportManager @Inject constructor(
  private val revenuePersistenceService: RevenuePersistenceService,
  private val revenueImportService: RevenueImportService,
  private val playtimeServiceClient: PlaytimeServiceClient,
  private val secretService: SecretService,
) {

  suspend fun importAndPushCsvRevenueToBQ(batchSize: Int) {
    var success = true
    val requests = prepareApplovinRequests()
    for (request in requests) {
      try {
        processRequest(request, batchSize)
      } catch (e: Exception) {
        success = false
        cronLogger().error("Import for $request failed", e)
      }
    }
    if (!success) {
      cronLogger().error("Import failed")
    }
  }

  private suspend fun prepareApplovinRequests() =
    playtimeServiceClient.getAllGameIdsList().filter { !it.applovinApiKey.isNullOrEmpty() }
      .map {
        val apiKey = secretService.secretValue(ApplovinApiKeys.byKey(it.applovinApiKey!!).secret)
        ApplovinRequest(it.applicationId, apiKey, it.platform)
      }

  private suspend fun processRequest(request: ApplovinRequest, batchSize: Int) {
    val importDay = LocalDate.now().minusDays(1)
    val lastImport = revenuePersistenceService.loadLatestMetaApplovinRevenue(request.applicationId, request.platform!!.name)
    if (lastImport == null || lastImport.day < importDay) {
      val processedRecordsNumber = revenueImportService.fetchApplovinRevenues(request, importDay, batchSize)
      if (processedRecordsNumber > 0) {
        revenuePersistenceService.saveMetaApplovinRevenues(
          MetaApplovinRevenue(
            null,
            request.applicationId,
            request.platform.name,
            importDay,
            processedRecordsNumber
          )
        )
        cronLogger().info("Imported ${processedRecordsNumber} Applovin data for ${request.applicationId} on $importDay")
      } else {
        cronLogger().info("No Applovin data found for ${request.applicationId} on $importDay")
      }
    } else {
      cronLogger().info("Applovin data already imported on $importDay for ${request.applicationId}")
    }
  }
}