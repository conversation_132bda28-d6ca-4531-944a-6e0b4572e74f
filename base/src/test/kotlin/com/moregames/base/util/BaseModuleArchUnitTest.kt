package com.moregames.base.util

import com.moregames.base.abtesting.TypedVariation
import com.moregames.base.util.archunit.ArchUnitTestBase
import com.tngtech.archunit.core.domain.JavaClass
import com.tngtech.archunit.lang.ArchCondition
import com.tngtech.archunit.lang.ConditionEvents
import com.tngtech.archunit.lang.SimpleConditionEvent
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.classes
import org.junit.jupiter.api.Test

class BaseModuleArchUnitTest : ArchUnitTestBase("com.moregames.base", excludeJars = false) {

  @Test
  fun `SHOULD check typed variations`() {
    classes().that().implement(TypedVariation::class.java).and().areNotNestedClasses()
      .should(HaveOnlyObjects)
      .check(javaClasses)
  }

  private object HaveOnlyObjects : ArchCondition<JavaClass>("be sealed and have only objects as children") {
    override fun check(item: JavaClass, events: ConditionEvents) {
      val kotlin = item.reflect().kotlin
      if (!kotlin.isSealed) {
        events.add(SimpleConditionEvent.violated(item, "Class ${item.fullName} is not sealed"))
        return
      }
      if (kotlin.sealedSubclasses.size != kotlin.sealedSubclasses.mapNotNull { it.objectInstance }.size) {
        val nonObjectClasses = kotlin.sealedSubclasses.filter { it.objectInstance == null }
        events.add(SimpleConditionEvent.violated(item, "Class ${item.fullName} doesn't have only objects as children: $nonObjectClasses"))
        return
      }
    }
  }
}