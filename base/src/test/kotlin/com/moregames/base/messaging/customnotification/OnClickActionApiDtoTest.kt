package com.moregames.base.messaging.customnotification

import net.javacrumbs.jsonunit.JsonAssert.assertJsonEquals
import org.junit.jupiter.api.Test

class OnClickActionApiDtoTest {
  @Test
  fun `SHOULD generate json ON scrollToOffer`() {
    //language=json
    val expected = """
      {
        "name": "SCROLL_TO_OFFER",
        "parameters": [
          "42"
        ]
      }
    """.trimIndent()

    val actual = OnClickActionApiDto.scrollToOffer(42)

    assertJsonEquals(expected, actual)
  }

  @Test
  fun `SHOULD generate json ON scrollToOfferAndOpenPreGameScreen`() {
    //language=json
    val expected = """
      {
        "name": "SCROLL_TO_OFFER",
        "parameters": [
          "42", "openPreGameScreen"
        ]
      }
    """.trimIndent()

    val actual = OnClickActionApiDto.scrollToOfferAndOpenPreGameScreen(42)

    assertJsonEquals(expected, actual)
  }

  @Test
  fun `SHOULD generate json ON scrollToOfferAndHighlight`() {
    //language=json
    val expected = """
      {
        "name": "SCROLL_TO_OFFER",
        "parameters": [
          "42", "applyHighlight"
        ]
      }
    """.trimIndent()

    val actual = OnClickActionApiDto.scrollToOfferAndHighlight(42)

    assertJsonEquals(expected, actual)
  }
}