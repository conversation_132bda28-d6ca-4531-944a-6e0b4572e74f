package com.moregames.base.bus.impl

import com.moregames.base.db.OptimisticLockException
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import java.util.function.Supplier
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

class ImmediateRetryTest {
  private val callback = mock<Supplier<Unit>>()

  @Test
  fun `SHOULD retry immediately ON immediateRetry WHEN exception is thrown`() = runBlocking {
    immediateRetry(3) {
      callback.get()
    }
    verify(callback, times(1)).get()
  }

  @Test
  fun `SHOULD retry immediately ON immediateRetry WHEN exception is thrown and retry count is 1`() = runBlocking {
    whenever(callback.get()) doThrow OptimisticLockException("123")
    assertFailsWith<OptimisticLockException> {
      immediateRetry(1) {
        callback.get()
      }
    }.also { assertEquals("123", it.message) }
    verify(callback, times(2)).get()
  }

  @Test
  fun `SHOULD run once ON immediateRetry WHEN exception is thrown and retry count is 0`() = runBlocking {
    whenever(callback.get()) doThrow OptimisticLockException("123")
    assertFailsWith<OptimisticLockException> {
      immediateRetry(0) {
        callback.get()
      }
    }.also { assertEquals("123", it.message) }
    verify(callback, times(1)).get()
  }
}