package com.moregames.base.table

import org.jetbrains.exposed.sql.Database
import org.junit.jupiter.api.extension.BeforeAllCallback
import org.junit.jupiter.api.extension.ExtensionContext
import org.junit.jupiter.api.extension.ParameterContext
import org.junit.jupiter.api.extension.ParameterResolver

/**
 * JUnit 5 extension that initializes the database for tests.
 *
 * The database instance can be injected into test methods using the [Database] parameter of method or constructor.
 *
 * The extension can be configured using the following properties, available in junit-platform.properties file:
 * - `docker.specific.port` - the specific port to use for the database container. If this property is used - docker will not try to find a free port.
 */
class DatabasePgExtension : BeforeAllCallback, ParameterResolver {

  override fun beforeAll(context: ExtensionContext) {
    val specificPort = context.getConfigurationParameter("docker.specific.port").orElse(null)?.takeIf { it.isNotBlank() }?.toInt()
    DatabasePgTestBase.initDatabase(specificPort)
  }

  override fun supportsParameter(parameterContext: ParameterContext, extensionContext: ExtensionContext?): <PERSON><PERSON><PERSON> {
    return parameterContext.parameter.type == Database::class.java
  }

  override fun resolveParameter(parameterContext: ParameterContext, extensionContext: ExtensionContext?): Any {
    return DatabasePgTestBase.database
  }
}
