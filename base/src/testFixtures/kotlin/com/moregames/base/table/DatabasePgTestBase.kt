package com.moregames.base.table

import com.github.dockerjava.api.model.ExposedPort
import com.github.dockerjava.api.model.HostConfig
import com.github.dockerjava.api.model.PortBinding
import com.github.dockerjava.api.model.Ports
import liquibase.Liquibase
import liquibase.database.DatabaseFactory
import liquibase.database.jvm.JdbcConnection
import liquibase.resource.ClassLoaderResourceAccessor
import org.jetbrains.exposed.sql.Database
import org.junit.jupiter.api.BeforeAll
import org.testcontainers.containers.PostgreSQLContainer
import java.sql.DriverManager

open class DatabasePgTestBase {
  companion object {
    lateinit var dbUser: String
    lateinit var dbUrl: String

    const val DB_PASSWORD = "user"
    const val SCHEMA = "rewarding"

    lateinit var database: Database

    @BeforeAll
    @JvmStatic
    fun initDatabase() {
      initDatabase(null)
    }

    fun initDatabase(specificPort: Int?) {
      if (::database.isInitialized) return

      dbUser = "root"

      val pgContainer =
        PostgreSQLContainer<Nothing>("postgres:16-alpine").apply {
          withTmpFs(mapOf("/var/lib/postgres" to "rw,noexec,nosuid,size=1024m"))
          withUsername(dbUser)
          withPassword(DB_PASSWORD)
          withInitScript("testcontainers/init_script_pg.sql")
          specificPort?.also {
            withCreateContainerCmdModifier { cmd ->
              cmd.withHostConfig(
                HostConfig().withPortBindings(PortBinding(Ports.Binding.bindPort(it), ExposedPort(3306)))
              )
            }
          }
        }

      pgContainer.start()
      dbUrl = "***************************:${pgContainer.getMappedPort(5432)}/$SCHEMA"

      DriverManager.getConnection(dbUrl, dbUser, DB_PASSWORD).use { connection ->
        val database = DatabaseFactory.getInstance().findCorrectDatabaseImplementation(JdbcConnection(connection))
        val liquibase = Liquibase("/database/liquibase-migration.xml", ClassLoaderResourceAccessor(), database)
        liquibase.update("unit-test")
      }

      database = Database.connect(
        url = dbUrl,
        user = dbUser,
        password = DB_PASSWORD
      )
    }
  }
}
