package com.moregames.base.util

import com.google.protobuf.Descriptors
import com.google.protobuf.Message
import com.google.protobuf.TypeRegistry
import com.moregames.base.bus.serialization.ProtoTypeRegistryProvider

@Suppress("unused")
object EmptyProtoTypeRegistryProvider : ProtoTypeRegistryProvider {
  override val protoTypeRegistry: TypeRegistry = TypeRegistry.getEmptyTypeRegistry()

  override fun findDefaultInstance(descriptor: Descriptors.Descriptor): Message {
    throw IllegalStateException("No default instance found for descriptor: ${descriptor.fullName}")
  }
}