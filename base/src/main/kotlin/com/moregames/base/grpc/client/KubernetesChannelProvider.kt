package com.moregames.base.grpc.client

import com.moregames.base.app.BuildVariant
import com.moregames.base.config.ServicesRegistry
import io.grpc.Channel
import io.grpc.ManagedChannelBuilder
import io.grpc.opentelemetry.GrpcOpenTelemetry
import javax.inject.Inject
import javax.inject.Provider

class KubernetesChannelProvider @Inject constructor(
  private val service: ServicesRegistry,
  private val clientConfig: GrpcClientConfig,
  private val buildVariant: BuildVariant,
  private val grpcOpenTelemetry: GrpcOpenTelemetry,
  private val kubernetesChannelProviderCache: KubernetesChannelProviderCache,
) : Provider<Channel> {
  override fun get(): Channel {
    val uri = "kubernetes://${buildVariant.key}/${service.key}-${buildVariant.key}-grpc/${clientConfig.port}"
    return kubernetesChannelProviderCache.getOrCreate(uri) {
      ManagedChannelBuilder.forTarget(uri)
        .defaultLoadBalancingPolicy("round_robin")
        .usePlaintext()
        .also {
          grpcOpenTelemetry.configureChannelBuilder(it)
        }
        .build()
    }
  }
}