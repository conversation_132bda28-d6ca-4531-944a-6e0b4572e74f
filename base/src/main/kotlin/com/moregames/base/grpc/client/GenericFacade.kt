package com.moregames.base.grpc.client

import com.google.inject.Singleton
import com.moregames.base.util.logger
import io.grpc.stub.StreamObserver
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

@Singleton
class GenericFacade {
  private val logger = logger()

  suspend fun <Req, Res> get(
    grpcCall: (Req, StreamObserver<Res>) -> Unit,
    request: Req,
  ): Res {
    val initialCallStack = RuntimeException()
    return suspendCancellableCoroutine { continuation ->
      grpcCall(request, object : StreamObserver<Res> {
        override fun onNext(value: Res) {
          continuation.resume(value)
        }

        override fun onError(t: Throwable) {
          val exception = initialCallStack.also {
            it.initCause(t)
          }
          if (!continuation.isActive) {
            logger.error("Received onError call, but continuation is already completed", exception)
            return
          }
          continuation.resumeWithException(exception)
        }

        override fun onCompleted() {
        }
      })
    }
  }

  /**
   * Returns **cold** Flow of responses.
   */
  fun <Req, Res> getFlow(
    grpcCall: (Req, StreamObserver<Res>) -> Unit,
    request: Req,
  ): Flow<Res> {
    val initialCallStack = RuntimeException()
    return callbackFlow {
      grpcCall(request, object : StreamObserver<Res> {
        override fun onNext(value: Res) {
          trySend(value)
        }

        override fun onError(t: Throwable) {
          close(initialCallStack.also {
            it.initCause(t)
          })
        }

        override fun onCompleted() {
          close()
        }
      })
    }
  }
}