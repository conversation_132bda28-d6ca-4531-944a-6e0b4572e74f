package com.moregames.base.user.dto

import com.moregames.base.dto.AppVersionDto
import kotlinx.serialization.Serializable

@Serializable
data class CreateUserData(
  val appVersion: AppVersionDto,
  val userRequestMetadata: UserRequestMetadata,
  val userRequestDto: CreateUserRequestDto?,
  val userRequestDtoSignature: String?,
  @Deprecated("We turned off all depends on client changes experiments and all new versions of the app do not provide definedExperimentVariations query param")
  val definedExperimentVariations: Map<String, Set<String>>?,
  val isReviewer: Boolean? = null,
)