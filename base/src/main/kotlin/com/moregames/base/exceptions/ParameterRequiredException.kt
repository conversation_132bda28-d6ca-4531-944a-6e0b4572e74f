package com.moregames.base.exceptions

class ParameterRequiredException(parameter: String, uri: String? = null, userId: String? = null) :
  BaseException(
    internalMessage = "Required parameter '$parameter' not found${uri?.let { " for request $it" } ?: ""}${userId?.let { ", userId: $it" } ?: ""}"
  ) {
  override val errorCode = BaseErrorCodes.PARAMETER_REQUIRED
  override val errorType = ErrorType.INPUT_ERROR
}
