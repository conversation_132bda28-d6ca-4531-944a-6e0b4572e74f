package com.moregames.base.app

import com.google.cloud.tasks.v2.CloudTasksClient
import com.google.inject.*
import com.google.inject.multibindings.MapBinder
import com.google.inject.name.Named
import com.launchdarkly.sdk.server.LDClient
import com.launchdarkly.sdk.server.LDConfig
import com.moregames.base.abtesting.AbTestingExperimentHook
import com.moregames.base.abtesting.ExperimentBase
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.ktor.HttpClientTracing
import com.moregames.base.messaging.PublisherCreator
import com.moregames.base.secret.BaseSecrets
import com.moregames.base.secret.GcpApiSecretService
import com.moregames.base.secret.Secret
import com.moregames.base.secret.SecretService
import com.moregames.base.util.DefaultCoroutineScope
import com.moregames.base.util.IoCoroutineScope
import com.moregames.base.util.logger
import com.sendgrid.SendGrid
import com.zaxxer.hikari.HikariDataSource
import io.grpc.opentelemetry.GrpcOpenTelemetry
import io.ktor.application.*
import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.features.*
import io.ktor.client.features.json.*
import io.ktor.client.features.json.serializer.*
import io.opentelemetry.api.GlobalOpenTelemetry
import io.opentelemetry.api.OpenTelemetry
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import java.util.concurrent.TimeUnit
import kotlin.reflect.KClass

abstract class BaseModule(
  private val application: Application,
  private val buildVariant: BuildVariant,
  private val dataSourceProvider: DataSourceProvider
) : AbstractModule() {
  companion object {
    const val CERTS_HTTP_CLIENT = "certsHttpClient"
  }

  override fun configure() {
    if (buildVariant == BuildVariant.TEST && !applicationConfig().gaeServiceVersion.startsWith("test")) {
      throw RuntimeException("Trying to deploy 'TEST' environment with not 'TEST' version")
    }
    if (buildVariant == BuildVariant.PRODUCTION && applicationConfig().gaeServiceVersion.startsWith("test")) {
      throw RuntimeException("Trying to deploy 'PRODUCTION' environment with 'TEST' version")
    }
    bind(Application::class.java).toInstance(application)
    bind(OpenTelemetry::class.java).toInstance(provideOpenTelemetry())
    bind(GrpcOpenTelemetry::class.java).toInstance(provideGrpcOpenTelemetry())
    bind(CloudTasksClient::class.java).toProvider { provideCloudTasksClient() }.`in`(Scopes.SINGLETON)
    bind(PublisherCreator::class.java).toInstance(providePublisherCreator())

    bind(IoCoroutineScope::class.java).toProvider { IoCoroutineScope() }
    bind(DefaultCoroutineScope::class.java).toProvider { DefaultCoroutineScope() }
    MapBinder.newMapBinder(binder(), ExperimentBase::class.java, AbTestingExperimentHook::class.java).also {
      provideAbTestingHooks().forEach { key, value ->
        it.addBinding(key).to(value.java).`in`(Scopes.SINGLETON)
      }
    }
    bind(SecretService::class.java).toProvider(provideSecretService()).`in`(Scopes.SINGLETON)
  }

  open fun provideAbTestingHooks(): Map<ExperimentBase, KClass<out AbTestingExperimentHook>> = emptyMap()

  open fun provideOpenTelemetry(): OpenTelemetry = GlobalOpenTelemetry.get()

  open fun provideGrpcOpenTelemetry(): GrpcOpenTelemetry = GrpcOpenTelemetry.newBuilder()
    .sdk(GlobalOpenTelemetry.get())
    .build()

  open fun provideCloudTasksClient(): CloudTasksClient = CloudTasksClient.create()

  open fun providePublisherCreator(): PublisherCreator = PublisherCreator()

  open fun provideSecretService(): Provider<SecretService> = Provider { GcpApiSecretService() }

  @Provides
  fun buildVariant() = buildVariant

  @Provides
  fun applicationConfig() = ApplicationConfig()

  @Provides
  @Singleton
  @Named(CERTS_HTTP_CLIENT)
  fun certsHttpClient() = HttpClient(CIO) {
    install(JsonFeature) {
      val json = kotlinx.serialization.json.Json { ignoreUnknownKeys = true }
      serializer = KotlinxSerializer(json)
    }
    install(HttpTimeout) {
      requestTimeoutMillis = 15 * 1000
    }
    install(HttpClientTracing) {
      spanName = "certs"
    }
  }

  @Provides
  @Singleton
  fun sendGrid(secretService: SecretService) = runBlocking { SendGrid(secretService.secretValue(BaseSecrets.SEND_GRID_API_KEY)) }

  @Provides
  @Singleton
  fun ldClient(secretService: SecretService): LDClient {
    val apiKeySecret = when (buildVariant) {
      BuildVariant.LOCAL -> BaseSecrets.LAUNCH_DARKLY_SDK_KEY_TEST
      BuildVariant.TEST -> BaseSecrets.LAUNCH_DARKLY_SDK_KEY_TEST
      BuildVariant.PRODUCTION -> BaseSecrets.LAUNCH_DARKLY_SDK_KEY
    }
    return LDClient(
      runBlocking { secretService.secretValue(apiKeySecret) },
      LDConfig.Builder().build()
    )
  }

  protected fun connectToDatabase(dbConfig: DbConfig, secretService: SecretService, alias: String? = null): Database {
    return Database.connect(getDataSource(dbConfig, secretService, alias ?: ""))
  }

  protected fun getLiquibaseDataSource(
    defaultSchema: String,
    username: String,
    password: Secret,
    secretService: SecretService,
    alias: String? = null,
  ): HikariDataSource =
    dataSourceProvider.getDataSource(
      username = username,
      password = password,
      maximumPoolSize = 1,
      secretService = secretService,
      defaultSchema = defaultSchema,
      alias = alias,
      this.buildVariant
    )

  private fun getDataSource(dbConfig: DbConfig, secretService: SecretService, alias: String): HikariDataSource =
    dataSourceProvider.getDataSource(
      username = dbConfig.username,
      password = dbConfig.password,
      maximumPoolSize = dbConfig.maximumPoolSize,
      secretService = secretService,
      alias = alias,
      buildVariant = this.buildVariant
    )

  interface DbConfig {
    val username: String
    val password: Secret
    val maximumPoolSize: Int
  }

  fun onShutdown(injector: Injector) {
    try {
      val cloudTasksClient = injector.getInstance(CloudTasksClient::class.java)
      cloudTasksClient.shutdown()
      cloudTasksClient.awaitTermination(3, TimeUnit.SECONDS)
    } catch (e: Exception) {
      logger().warn("Shutdown failure: ${e.message}", e)
    }
  }
}
