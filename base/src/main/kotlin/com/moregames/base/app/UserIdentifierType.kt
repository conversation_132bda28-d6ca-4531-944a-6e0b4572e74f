package com.moregames.base.app

import com.moregames.base.exceptions.InvalidEnumKey
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
enum class UserIdentifierType(val key: String) {

  @SerialName("email")
  EMAIL("email"),

  @SerialName("user_handle")
  USER_HANDLE("user_handle"),

  @SerialName("phone_number")
  PHONE_NUMBER("phone_number")
  ;

  companion object {
    private val valuesMap = values().associateBy { it.key }

    fun from<PERSON>ey(key: String) = valuesMap[key] ?: throw InvalidEnumKey(key, UserIdentifierType::class)
  }

}