package com.moregames.base.bus.config

import com.moregames.base.bus.Message
import kotlin.reflect.KClass
import kotlin.reflect.full.isSubclassOf

@RequiresOptIn("ALARM! ALARM! ALARM! You're using wrong `to` method.", level = RequiresOptIn.Level.ERROR)
annotation class WrongToMethod

data class Destination(val topicName: String, val queueName: String?)

@BusConfigurationDsl1
class MessageRoutingConfiguration {
  internal val typeToDestinationName: MutableMap<KClass<*>, Destination> = mutableMapOf()
  internal val subTypeToDestinationName: MutableMap<KClass<*>, Destination> = mutableMapOf()

  @BusConfigurationDsl1
  inline fun <reified T> route(): RoutingBuilder {
    return RoutingBuilder(T::class)
  }

  inline fun <reified T> routeSubTypeOf(): SubtypeRoutingBuilder {
    return SubtypeRoutingBuilder(T::class)
  }

  inner class RoutingBuilder(private val clazz: KClass<*>) {
    @BusConfigurationDsl1
    infix fun to(destination: Destination) {
      map(clazz, destination)
    }

    @WrongToMethod
    infix fun to(any: Any) { // overrides kotlin's `to` method
      error("You should use 'to' method with Destination parameter")
    }
  }

  inner class SubtypeRoutingBuilder(private val superClazz: KClass<*>) {
    infix fun to(destination: Destination) {
      mapSubTypeOf(superClazz, destination)
    }
  }

  private fun map(clazz: KClass<*>, destination: Destination) {
    if (clazz.isAbstract) {
      throw IllegalArgumentException("Could not make direct mapping of interface or abstract class. Use mapSubTypeOf for interfaces or abstract classes")
    }

    if (!(clazz.isSubclassOf(Message::class) || clazz.isSubclassOf(com.google.protobuf.Message::class))) {
      throw IllegalArgumentException("Could not map $clazz because it is not message type")
    }

    if (typeToDestinationName[clazz] != null) {
      throw IllegalArgumentException("Routing configuration already contains mapping for $clazz")
    }

    typeToDestinationName[clazz] = destination
  }

  private fun mapSubTypeOf(clazz: KClass<*>, destination: Destination) {
    if (subTypeToDestinationName[clazz] != null) {
      throw IllegalArgumentException("Routing configuration already contains subType mapping for $clazz")
    }

    subTypeToDestinationName[clazz] = destination
  }
}