package com.moregames.base.bus.impl

import org.slf4j.LoggerFactory

private val logger = LoggerFactory.getLogger("com.moregames.base.bus.impl.ImmediateRetry")

suspend fun immediateRetry(maxNumberOfImmediateRetries: Int, callback: suspend () -> Unit) {
  repeat(maxNumberOfImmediateRetries) {
    try {
      callback()
      return
    } catch (e: Exception) {
      when {
        e is ImmediateRetryException -> {
          logger.warn("Invocation failed with ImmediateRetryException: ${e.message}. Immediate retry ${it + 1} out of $maxNumberOfImmediateRetries...", e)
        }

        else -> throw e
      }
    }
  }

  // last (or only) attempt
  callback()
}

abstract class ImmediateRetryException(message: String?, cause: Throwable?) : RuntimeException(message, cause)