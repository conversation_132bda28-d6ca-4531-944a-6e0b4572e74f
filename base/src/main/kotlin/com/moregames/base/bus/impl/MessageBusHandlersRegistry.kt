package com.moregames.base.bus.impl

import com.google.inject.Injector
import com.google.inject.Provider
import com.moregames.base.bus.ImmediateRetries
import javax.inject.Inject
import kotlin.reflect.KClass
import kotlin.reflect.KFunction
import kotlin.reflect.full.callSuspend
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.full.instanceParameter
import kotlin.reflect.full.valueParameters
import kotlin.reflect.jvm.jvmErasure

data class MessageBusHandlerProxy(
  val handlerInstance: Provider<out Any>,
  val handlerMethod: KFunction<*>,
  val immediateRetries: Int = 0,
) {
  suspend fun invokeHandler(effect: Any): Any? {
    return handlerMethod.callSuspend(handlerInstance.get(), effect)
  }
}

class MessageBusHandlersRegistry @Inject constructor(
  private val injector: Injector,
  messageBusTypeRegistry: MessageBusTypeRegistry,
) {
  private val messageBusHandlers = buildEffectHandlersMap(messageBusTypeRegistry)

  fun findHandlers(effectType: KClass<*>): List<MessageBusHandlerProxy> {
    return messageBusHandlers[effectType] ?: emptyList()
  }

  fun getMessageClasses(): Set<KClass<*>> = messageBusHandlers.keys

  private fun buildEffectHandlersMap(messageBusTypeRegistry: MessageBusTypeRegistry): Map<KClass<*>, List<MessageBusHandlerProxy>> {
    return messageBusTypeRegistry.handlerMethods.groupBy({
      it.valueParameters[0].type.jvmErasure
    }, { handlerMethod ->
      val immediateRetries = handlerMethod.findAnnotation<ImmediateRetries>()?.value ?: 1

      MessageBusHandlerProxy(
        injector.getProvider(handlerMethod.instanceParameter!!.type.jvmErasure.java),
        handlerMethod,
        immediateRetries,
      )
    })
  }
}