package com.moregames.base.bus.impl

import com.moregames.base.bus.AsyncEffect
import com.moregames.base.bus.Effect
import com.moregames.base.util.IoCoroutineScope
import com.moregames.base.util.getJpMeter
import io.opentelemetry.api.GlobalOpenTelemetry
import io.opentelemetry.api.common.AttributeKey.stringKey
import io.opentelemetry.api.common.Attributes
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Provider
import kotlin.reflect.full.isSubclassOf

class BusMessagesProcessor @Inject constructor(
  private val messageBusHandlersRegistry: MessageBusHandlersRegistry,
  private val coroutineScope: Provider<IoCoroutineScope>,
) {

  private val messageCounter = GlobalOpenTelemetry.get().getJpMeter().counterBuilder("bus_messages_counter").setDescription("Count of bus messages").build()
  private val effectsCounter = GlobalOpenTelemetry.get().getJpMeter().counterBuilder("bus_effects_counter").setDescription("Count of bus effects").build()

  suspend fun processMessage(message: Any) {
    messageBusHandlersRegistry.findHandlers(message::class).also {
      if (it.isEmpty()) {
        throw IllegalStateException("Could not find handlers for ${message::class}")
      }
    }.forEach { proxy ->
      immediateRetry(proxy.immediateRetries) {
        proxy.invokeHandler(message)
      }
      if (message::class.isSubclassOf(Effect::class)) {
        effectsCounter.add(
          1, message::class.simpleName
            ?.let { name -> Attributes.of(stringKey("effect_class"), name, stringKey("effect_type"), "sync") }
            ?: Attributes.empty()
        )
      } else {
        messageCounter.add(
          1, message::class.simpleName
            ?.let { name -> Attributes.of(stringKey("message_class"), name) }
            ?: Attributes.empty()
        )
      }
    }
  }

  fun processAsyncEffect(effect: AsyncEffect) {
    messageBusHandlersRegistry.findHandlers(effect::class).also {
      if (it.isEmpty()) {
        throw IllegalStateException("Could not find handlers for ${effect::class}")
      }
    }.forEach { proxy ->
      coroutineScope.get().launch {
        immediateRetry(proxy.immediateRetries) {
          proxy.invokeHandler(effect)
        }
        effectsCounter.add(
          1, effect::class.simpleName
            ?.let { name -> Attributes.of(stringKey("effect_class"), name, stringKey("effect_type"), "async") }
            ?: Attributes.empty()
        )
      }
    }
  }
}