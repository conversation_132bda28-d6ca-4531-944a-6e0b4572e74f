package com.moregames.base.messaging.commands

import kotlinx.serialization.Serializable


object StatusCommandResponse {
  fun ok(vararg payload: Pair<String, String>) = CommandResponse(CommandResult.OK, payload.toMap())
  fun tooEarly(vararg payload: Pair<String, String>) = CommandResponse(CommandResult.TOO_EARLY, payload.toMap())
  fun internalServerError(vararg payload: Pair<String, String>) = CommandResponse(CommandResult.INTERNAL_SERVER_ERROR, payload.toMap())

  fun ok(map: Map<String, String>) = CommandResponse(CommandResult.OK, map)
}

private fun CommandResponse(status: CommandResult, payload: Map<String, String>) = buildMap {
  put("status", status.name)
  putAll(payload)
}

@Serializable
enum class CommandResult {
  OK, TOO_EARLY, INTERNAL_SERVER_ERROR
}