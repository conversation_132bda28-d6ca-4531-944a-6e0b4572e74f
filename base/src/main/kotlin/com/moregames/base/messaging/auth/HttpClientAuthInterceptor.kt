package com.moregames.base.messaging.auth

import com.moregames.base.util.alert
import com.moregames.base.util.bearerToken
import com.moregames.base.util.logger
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.request.*
import io.ktor.response.*
import io.ktor.routing.*
import javax.inject.Inject

class HttpClientAuthInterceptor @Inject constructor(
  private val authService: AuthService,
) {
  fun Route.installInterceptor() {
    intercept(ApplicationCallPipeline.Call) {

      val token = bearerToken()
      if (token.isNullOrEmpty()) {
        logger().alert("Received cross-service request without token. URL: ${call.request.uri}")
      }

      val isTokenValid = authService.isTokenValid(token)

      if (!isTokenValid) {
        call.respondText("Unauthorized request", ContentType.Text.Plain, HttpStatusCode.Unauthorized)
        return@intercept finish()
      }
    }
  }
}