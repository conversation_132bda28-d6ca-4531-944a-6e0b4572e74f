package com.moregames.base.messaging.dto

import com.moregames.base.bigquery.BqEvent
import com.moregames.base.util.BigDecimalAsString
import com.moregames.base.util.InstantAsString
import kotlinx.serialization.Serializable


@Serializable
data class CoinsCappedEventDto(
  val userId: String,
  val cappedEventType: CappedEventType,
  val cappedAmount: BigDecimalAsString, // amount of coins that were not delivered to the user
  val createdAt: InstantAsString,
) : BqEvent {
  override val topicName: String = "coins-capped"

  @Serializable
  enum class CappedEventType {
    // limited in BQ with 50 chars
    REVENUE_RETENTION_CAP;
  }
}
