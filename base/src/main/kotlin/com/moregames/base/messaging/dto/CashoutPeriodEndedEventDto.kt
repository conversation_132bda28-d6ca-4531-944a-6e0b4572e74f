package com.moregames.base.messaging.dto

import com.moregames.base.util.InstantAsString
import kotlinx.serialization.Serializable

@Serializable
data class CashoutPeriodEndedEventDto(
  val userId: String,
  val periodEnd: InstantAsString,
  val periodStart: InstantAsString,
) : MessageDto {
  override fun defaultPubsubTopicName(): String = TOPIC_NAME
  override fun defaultCloudTaskQueueName(): String = QUEUE_NAME

  companion object {
    const val TOPIC_NAME = "cashout-period-ended"
    const val QUEUE_NAME = "cashout-period-ended-tf"
  }
}