package com.moregames.base.messaging.dto

import com.fasterxml.jackson.annotation.JsonTypeInfo
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "@class")
sealed class UserChallengeProgressDto : MessageDto {
  abstract val userId: String
  abstract val applicationId: String

  override fun defaultPubsubTopicName(): String = TOPIC_NAME

  companion object {
    const val TOPIC_NAME = "user-challenge-progress"
  }

  @Serializable
  @SerialName("CHALLENGE_PROGRESS.SCORE")
  data class ScoreProgressDto(
    override val userId: String,
    override val applicationId: String,
    val score: Int,
  ) : UserChallengeProgressDto()

  @Serializable
  @SerialName("CHALLENGE_PROGRESS.MILESTONE")
  data class MilestoneProgressDto(
    override val userId: String,
    override val applicationId: String,
    val milestone: Int
  ) : UserChallengeProgressDto()

  @Serializable
  @SerialName("CHALLENGE_PROGRESS.AMOUNT_MILESTONE")
  data class AmountMilestoneProgressDto(
    override val userId: String,
    override val applicationId: String,
    val milestone: Int,
    val amount: Int,
  ) : UserChallengeProgressDto()

  @Serializable
  @SerialName("CHALLENGE_PROGRESS.TREASURE_MASTER")
  @Deprecated("Replaced with more precise LevelId")
  data class TmProgressDto(
    override val userId: String,
    override val applicationId: String,
    val score: Int,
    val isBoss: Boolean,
    val level: Int? = null,
  ) : UserChallengeProgressDto()

  @Serializable
  @SerialName("CHALLENGE_PROGRESS.GAME_COMPLETED_SCORE")
  data class ScoreCompletedProgressDto(
    override val userId: String,
    override val applicationId: String,
    val score: Int,
    val isHighScore: Boolean,
  ) : UserChallengeProgressDto()

  @Serializable
  @SerialName("CHALLENGE_PROGRESS.LEVEL_ID")
  data class LevelIdProgressDto(
    override val userId: String,
    override val applicationId: String,
    val levelId: String,
  ) : UserChallengeProgressDto()
}