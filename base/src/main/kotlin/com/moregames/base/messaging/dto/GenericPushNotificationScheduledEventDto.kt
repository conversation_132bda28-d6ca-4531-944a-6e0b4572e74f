package com.moregames.base.messaging.dto

import com.moregames.base.messaging.dto.PushNotificationDto.PushNotificationType
import com.moregames.base.messaging.dto.PushNotificationDto.PushNotificationType.DEFAULT
import kotlinx.serialization.Serializable

@Serializable
data class GenericPushNotificationScheduledEventDto(
  val userId: String,
  val title: String? = null,
  val notificationText: String,
  val notificationType: PushNotificationType? = DEFAULT,
  val label: String? = null,
) : PushNotificationDto {
  override fun userId(): String = userId

  override fun notificationType() = notificationType ?: DEFAULT

  override fun notificationTitle(): String? = title

  override fun notificationText(): String = notificationText

  override fun label(): String = label ?: ""
}

