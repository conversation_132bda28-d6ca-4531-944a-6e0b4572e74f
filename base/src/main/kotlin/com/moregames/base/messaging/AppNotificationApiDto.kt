package com.moregames.base.messaging

import com.moregames.base.messaging.dto.MessageDto
import kotlinx.serialization.Serializable

@Serializable
data class AppNotificationApiDto(
  val data: String?,
  val notificationType: String,
  override val userId: String,
  override val label: String = "",
) : MessageDto, BaseAppNotificationApiDto {
  override fun defaultPubsubTopicName(): String = TOPIC_NAME

  companion object {
    const val TOPIC_NAME = "app-notification"
  }
}