package com.moregames.base.messaging.customnotification

enum class Button<PERSON><PERSON><PERSON>ame {
  ROUTE_TO_MAIN,
  ROUTE_TO_CASHOUT,
  ROUTE_TO_REWARDS,
  CONTACT_US,
  OPEN_LINK_IN_BROWSER,
  O<PERSON><PERSON>_LINK_IN_POPUP,
  SCROLL_TO_OFFER,
  SCROLL_TO_UNLOCKED_GAME_AND_HIDE_WIDGET,
  OPEN_UNLOCKED_GAME_INSTALL_AND_HIDE_WIDGET,
  DISCARD_NOTIFICATION,
  DISCARD_POPUP,
  OPEN_FIRST_FOUND_INSTALLED_GAME,
  CONTINUE_INCOMPLETE_CASHOUT,

  //PLAT-2849: used in promotion config
  O<PERSON><PERSON>_CHALLENGES,
  OPEN_FIRST_FOUND_OFFERWALL,
}
