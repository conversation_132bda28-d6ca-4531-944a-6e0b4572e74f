package com.moregames.base.table

import org.jetbrains.exposed.sql.Table

/**
 * List of games that are offered to users for playing
 *
 * - id - unique identifier
 * - application_id - google/apple application package name
 * - activity_name - used to determine games installation status in justplay mobile app
 * - name - user-friendly name of game
 * - description - detailed description of game
 * - icon_filename - file name of small game icon
 * - image_filename - file name of game picture
 * - order_key - default order of game on JustPlay app screen
 * - info_text_install - text that is displayed if game is not yet installed
 * - info_text_play - text that is displayed if game is already installed
 * - order_key_alt_x - alternative game orders that are used for experiments
 * - applovin_api_key - name of secret (in Google secret service) that contains key is used for accessing applovin API
 * - install_image_filename - URL for image on install panel
 * - video_preview_filename - URL for video preview
 * - info_text_install_top - Text on install panel
 * - info_text_install_bottom - - Text on install panel
 * - exp_image_filename - Another image (for experiment)
 * - back_ground_color - UI
 * - is_disabled - don't show on UI and don't process game activity
 * - do_not_show - hide from UI, but still process game activity (for already installed games)
 * - show_for_lat - should game be shown for Limited Ad Tracking user or not
 * - publisher_id - identifier of publisher (SoftBakedApps / Gimica / etc)
 * - platform - either ANDROID or IOS
 * - ios_application_id - identifier of iOS app in appstore
 * - ios_game_url - URL-suffix of game for iOS store
 * - genre_id - [genre of game][com.moregames.playtime.games.genres.GameGenreTable.id]
 */
object GamesTable : Table("playtime.games") {
  val id = integer("id").autoIncrement()
  val applicationId = varchar("application_id", 100)
  val activityName = varchar("activity_name", 200)
  val name = varchar("name", 60)
  val description = varchar("description", 200)
  val iconFilename = varchar("icon_filename", 1000)
  val imageFilename = varchar("image_filename", 1000)
  val orderKey = integer("order_key")
  val applovinApiKey = varchar("applovin_api_key", 45)
  val installImageFilename = varchar("install_image_filename", 1000)
  val videoPreviewFilename = varchar("video_preview_filename", 1000).nullable()
  val infoTextInstallTop = varchar("info_text_install_top", 200)
  val infoTextInstallBottom = varchar("info_text_install_bottom", 200)
  val expImageFilename = varchar("exp_image_filename", 1000).nullable()
  val backGroundColor = varchar("back_ground_color", 20).nullable()
  val isDisabled = bool("is_disabled").clientDefault { false }
  val doNotShow = bool("do_not_show").clientDefault { false }
  val showForLat = bool("show_for_lat").nullable()
  val publisherId = integer("publisher_id")
  val platform = varchar("platform", 36)
  val iosApplicationId = varchar("ios_application_id", 36).nullable()
  val iosGameUrl = varchar("ios_game_url", 128).nullable()

  override val primaryKey: PrimaryKey = PrimaryKey(id)
}
