package com.moregames.base.table

import org.jetbrains.exposed.dao.id.IntIdTable

object UserMultiSimDataTable : IntIdTable("playtime.user_multi_sim_data") {
  val userId = varchar("user_id", 36)
  val networkCountry = varchar("network_country", 2)
  val networkOperatorName = varchar("network_operator_name", 100)
  val simCountry = varchar("sim_country", 2)
  val simOperatorName = varchar("sim_operator_name", 100)
  val simSlotIndex = integer("sim_slot_index")
}