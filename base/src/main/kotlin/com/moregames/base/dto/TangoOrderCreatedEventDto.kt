package com.moregames.base.dto

import com.moregames.base.messaging.dto.MessageDto
import kotlinx.serialization.Serializable

@Serializable
data class TangoOrderCreatedEventDto(
  val cashoutTransactionId: String,
  val referenceId: String,
  val status: String

) : MessageDto {
  override fun defaultPubsubTopicName(): String = TOPIC_NAME

  companion object {
    const val TOPIC_NAME = "tango-order-created"
  }
}