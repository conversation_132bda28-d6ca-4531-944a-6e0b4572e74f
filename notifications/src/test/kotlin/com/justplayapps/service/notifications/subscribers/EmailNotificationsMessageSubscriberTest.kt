package com.justplayapps.service.notifications.subscribers

import com.justplayapps.service.notifications.EmailMessagingService
import com.moregames.base.messaging.dto.EmailNotificationEventDto
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking

class EmailNotificationsMessageSubscriberTest {
  private val emailMessagingService: EmailMessagingService = mock()

  private val service = EmailNotificationsMessageSubscriber(emailMessagingService)

  companion object {
    val event = EmailNotificationEventDto(
      userId = "u1",
      encryptedEmail = "encryptedEmail",
      encryptedName = "encryptedName",
      sendGridTemplateId = "tmpl1"
    )
  }

  @Test
  fun `SHOULD call messaging service ON new event`() {
    runBlocking { service.handle(event) }

    verifyBlocking(emailMessagingService) { sendEmail(event) }
  }
}