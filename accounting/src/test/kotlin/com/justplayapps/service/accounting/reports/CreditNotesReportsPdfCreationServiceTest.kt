package com.justplayapps.service.accounting.reports

import com.codeborne.pdftest.PDF
import com.justplayapps.proxybase.PaymentAccounts.*
import com.justplayapps.service.accounting.invoices.InvoiceType
import com.justplayapps.service.accounting.invoices.InvoicesTotal
import com.justplayapps.service.accounting.invoices.ProviderType
import com.justplayapps.service.accounting.reports.pdf.CreditNotesReportsPdfCreationService
import com.moregames.base.app.BuildVariant
import com.moregames.base.util.mock
import org.junit.jupiter.api.Test
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.eq
import java.io.File
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.assertEquals

class CreditNotesReportsPdfCreationServiceTest {

  private val cloudStorageService: CloudStorageService = org.mockito.kotlin.mock()
  private val underTest = CreditNotesReportsPdfCreationService(
    BuildVariant.TEST,
    cloudStorageService
  )

  @Test
  fun `SHOULD create correct pdf file ON createPdf with PayPal provider`() {
    val argumentCaptor = argumentCaptor<ByteArray>()
    cloudStorageService.mock(
      { saveReportAndReturnUrl(argumentCaptor.capture(), eq("test/2022/report-42.pdf")) },
      "https://storage.cloud.google.com/justplay-reports/test/2022/report-42.pdf"
    )
    underTest.createPdf(
      42,
      LocalDateTime.parse("2022-12-01T11:51:00"),
      ProviderType.PayPal,
      listOf(
        InvoicesTotal(InvoiceType.Payment, "USD", PAYPAL_JP, BigDecimal("1673.12"), 14),
        InvoicesTotal(InvoiceType.Payment, "CAD", PAYPAL_JP, BigDecimal("6227.13"), 34),
        InvoicesTotal(InvoiceType.Payment, "EUR", PAYPAL_JP, BigDecimal("3783.53"), 67),
        InvoicesTotal(InvoiceType.Reversal, "AUD", PAYPAL_JP, BigDecimal("1333.12"), 11),
        InvoicesTotal(InvoiceType.Reversal, "PLN", PAYPAL_JP, BigDecimal("6447.13"), 89),
        InvoicesTotal(InvoiceType.Reversal, "GBP", PAYPAL_JP, BigDecimal("3673.53"), 5),
      ),
      "2022-11-01T00:00:02+0000#6WAZ844N#T0001" to "2022-11-30T23:59:58+0000#FM1551212C#T0001"
    )

    val expected = PDF(File("src/test/resources/pdf/expectedCreditNotesReport.pdf"))
    val actual = PDF(argumentCaptor.firstValue)
    //assertion without first row with timestamps
    assertEquals(expected.text.substringAfter("\n"), actual.text.substringAfter("\n"))
  }

  @Test
  fun `SHOULD create correct pdf file ON createPdf with PayPal provider WHEN we have data for both accounts`() {
    val argumentCaptor = argumentCaptor<ByteArray>()
    cloudStorageService.mock(
      { saveReportAndReturnUrl(argumentCaptor.capture(), eq("test/2022/report-42.pdf")) },
      "https://storage.cloud.google.com/justplay-reports/test/2022/report-42.pdf"
    )
    underTest.createPdf(
      42,
      LocalDateTime.parse("2022-12-01T11:51:00"),
      ProviderType.PayPal,
      listOf(
        InvoicesTotal(InvoiceType.Payment, "USD", PAYPAL_JP, BigDecimal("444.12"), 65),
        InvoicesTotal(InvoiceType.Payment, "CAD", PAYPAL_JP, BigDecimal("333.13"), 34),
        InvoicesTotal(InvoiceType.Payment, "EUR", PAYPAL_JP, BigDecimal("222.53"), 15),
        InvoicesTotal(InvoiceType.Reversal, "AUD", PAYPAL_JP, BigDecimal("44.12"), 5),
        InvoicesTotal(InvoiceType.Reversal, "PLN", PAYPAL_JP, BigDecimal("33.13"), 4),
        InvoicesTotal(InvoiceType.Reversal, "GBP", PAYPAL_JP, BigDecimal("22.53"), 2),
        InvoicesTotal(InvoiceType.Payment, "EUR", PAYPAL_PS, BigDecimal("3783.53"), 67),
        InvoicesTotal(InvoiceType.Reversal, "PLN", PAYPAL_PS, BigDecimal("6447.13"), 89),
        InvoicesTotal(InvoiceType.Payment, "CAD", PAYPAL_PS, BigDecimal("6227.13"), 34),
        InvoicesTotal(InvoiceType.Payment, "USD", PAYPAL_PS, BigDecimal("1673.12"), 14),
        InvoicesTotal(InvoiceType.Payment, "SGD", PAYPAL_PS, BigDecimal("200.12"), 14),
        InvoicesTotal(InvoiceType.Reversal, "AUD", PAYPAL_PS, BigDecimal("1333.12"), 11),
        InvoicesTotal(InvoiceType.Reversal, "GBP", PAYPAL_PS, BigDecimal("3673.53"), 5),
      ),
      "2022-11-01T00:00:02+0000#6WAZ844N#T0001" to "2022-11-30T23:59:58+0000#FM1551212C#T0001"
    )

    val expected = PDF(File("src/test/resources/pdf/expectedCreditNotesReport2.pdf"))
    val actual = PDF(argumentCaptor.firstValue)
    //assertion without first row with timestamps
    assertEquals(expected.text.substringAfter("\n"), actual.text.substringAfter("\n"))
  }

  @Test
  fun `SHOULD create correct pdf file ON createPdf with TangoCard provider WHEN we have data for both accounts`() {
    val argumentCaptor = argumentCaptor<ByteArray>()
    cloudStorageService.mock(
      { saveReportAndReturnUrl(argumentCaptor.capture(), eq("test/2022/report-42.pdf")) },
      "https://storage.cloud.google.com/justplay-reports/test/2022/report-42.pdf"
    )
    underTest.createPdf(
      42,
      LocalDateTime.parse("2022-12-01T11:51:00"),
      ProviderType.PayPal,
      listOf(
        InvoicesTotal(InvoiceType.Payment, "USD", TANGO_JP, BigDecimal("444.12"), 65),
        InvoicesTotal(InvoiceType.Payment, "EUR", TANGO_JP, BigDecimal("222.53"), 15),
        InvoicesTotal(InvoiceType.Payment, "CAD", TANGO_JP, BigDecimal("333.13"), 34),
        InvoicesTotal(InvoiceType.Reversal, "PLN", TANGO_JP, BigDecimal("33.13"), 4),
        InvoicesTotal(InvoiceType.Reversal, "AUD", TANGO_JP, BigDecimal("44.12"), 5),
        InvoicesTotal(InvoiceType.Reversal, "GBP", TANGO_JP, BigDecimal("22.53"), 2),
      ),
      "2022-11-01T00:00:02+0000#6WAZ844N#T0001" to "2022-11-30T23:59:58+0000#FM1551212C#T0001"
    )

    val expected = PDF(File("src/test/resources/pdf/expectedCreditNotesReport3.pdf"))
    val actual = PDF(argumentCaptor.firstValue)

    //assertion without first row with timestamps
    assertEquals(expected.text.substringAfter("\n"), actual.text.substringAfter("\n"))
  }
}
//PDF can be saved by
//File.createTempFile("___", "", File("C:\\WorkDir")).writeBytes(actual.content)