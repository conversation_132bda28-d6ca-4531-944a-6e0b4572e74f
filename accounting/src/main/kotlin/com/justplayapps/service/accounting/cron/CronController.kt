package com.justplayapps.service.accounting.cron

import com.google.inject.Inject
import com.justplayapps.proxybase.PaymentAccounts
import com.justplayapps.proxybase.PaymentAccounts.*
import com.justplayapps.service.accounting.invoices.InvoicesService
import com.justplayapps.service.accounting.invoices.ProviderType
import com.justplayapps.service.accounting.paypal.PayPalApiService
import com.justplayapps.service.accounting.reports.CreditNotesReportsService
import com.justplayapps.service.accounting.reports.donation.DonationsReportService
import com.justplayapps.service.accounting.tango.TangoCardApiService
import com.moregames.base.util.cronLogger
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.response.*
import io.ktor.routing.*
import java.time.YearMonth

class CronController @Inject constructor(
  private val payPalApiService: PayPalApiService,
  private val tangoCardApiService: TangoCardApiService,
  private val invoicesService: InvoicesService,
  private val creditNotesReportsService: CreditNotesReportsService,
  private val donationsReportService: DonationsReportService,
) {

  companion object {
    const val LOAD_PAYPAL_TRANSACTIONS_CRON_JOB = "loadPayPalTransactions"
    const val LOAD_PAYPAL_TRANSACTIONS_NEW_ACCOUNT_CRON_JOB = "loadPayPalTransactionsNewAccount"
    const val LOAD_PAYPAL_TRANSACTIONS_USD_ACCOUNT_CRON_JOB = "loadPayPalTransactionsUsdAccount"
    const val LOAD_PAYPAL_TRANSACTIONS_GMC_ACCOUNT_CRON_JOB = "loadPayPalTransactionsGmcAccount"
    const val LOAD_TANGO_CARD_TRANSACTIONS_CRON_JOB = "loadTangoCardTransactions"
    const val CREATE_PAY_PAL_INVOICES_CRON_JOB = "createPayPalInvoices"
    const val CREATE_TANGO_CARD_INVOICES_CRON_JOB = "createTangoCardInvoices"
    const val CREATE_TREMENDOUS_INVOICES_CRON_JOB = "createTremendousInvoices"
  }

  fun startRouting(route: Route) {
    route.get("/getLastBalancesAmounts") {
      val accounts = (call.parameters["accounts"] ?: "PAYPAL_PS,PAYPAL_JP").split(",").toList()
      accounts.forEach { account ->
        cronLogger().info("Starting getLastBalancesAmounts $account")
        payPalApiService.getLastBalancesAmounts(PaymentAccounts.valueOf(account.trim()))
          .also { cronLogger().info("Finished getLastBalancesAmounts $account. Rows inserted: $it") }
      }
      call.respond(HttpStatusCode.OK)
    }
    route.get("/creditNotesReports") {
      cronLogger().info("Starting creditNotesReports")
      ProviderType.entries.forEach {
        creditNotesReportsService.createReport(YearMonth.now().minusMonths(1), it)
      }
      donationsReportService.generateMonthlyDonationsPdfReport()
      cronLogger().info("Finished creditNotesReports")
      call.respond(HttpStatusCode.OK)
    }

    route.get("/aggregatedDonationsReportMail") {
      cronLogger().info("Starting aggregatedDonationsReportMail")
      donationsReportService.sendMonthlyDonationsReport(YearMonth.now().minusMonths(1))
      call.respond(HttpStatusCode.OK)
      cronLogger().info("Finished aggregatedDonationsReportMail")
    }

    route.get("/$LOAD_PAYPAL_TRANSACTIONS_NEW_ACCOUNT_CRON_JOB") {
      val minutes = call.parameters["minutes"]!!.toLong()
      cronLogger().info("Starting $LOAD_PAYPAL_TRANSACTIONS_NEW_ACCOUNT_CRON_JOB")
      payPalApiService.loadBatchOfPayPalTransactions(
        minutes = minutes,
        cronJobName = LOAD_PAYPAL_TRANSACTIONS_NEW_ACCOUNT_CRON_JOB,
        account = PAYPAL_JP
      )
        .let { cronLogger().info("Finished $LOAD_PAYPAL_TRANSACTIONS_NEW_ACCOUNT_CRON_JOB. Rows inserted $it") }
      call.respond(HttpStatusCode.OK)
    }

    route.get("/$LOAD_PAYPAL_TRANSACTIONS_CRON_JOB") {
      val minutes = call.parameters["minutes"]!!.toLong()
      cronLogger().info("Starting $LOAD_PAYPAL_TRANSACTIONS_CRON_JOB")
      payPalApiService.loadBatchOfPayPalTransactions(
        minutes = minutes,
        cronJobName = LOAD_PAYPAL_TRANSACTIONS_CRON_JOB,
        account = PAYPAL_PS
      )
        .let { cronLogger().info("Finished $LOAD_PAYPAL_TRANSACTIONS_CRON_JOB. Rows inserted $it") }
      call.respond(HttpStatusCode.OK)
    }

    route.get("/$LOAD_PAYPAL_TRANSACTIONS_USD_ACCOUNT_CRON_JOB") {
      val minutes = call.parameters["minutes"]!!.toLong()
      cronLogger().info("Starting $LOAD_PAYPAL_TRANSACTIONS_USD_ACCOUNT_CRON_JOB")
      payPalApiService.loadBatchOfPayPalTransactions(
        minutes = minutes,
        cronJobName = LOAD_PAYPAL_TRANSACTIONS_USD_ACCOUNT_CRON_JOB,
        account = PAYPAL_USD
      )
        .let { cronLogger().info("Finished $LOAD_PAYPAL_TRANSACTIONS_USD_ACCOUNT_CRON_JOB. Rows inserted $it") }
      call.respond(HttpStatusCode.OK)
    }

    route.get("/$LOAD_PAYPAL_TRANSACTIONS_GMC_ACCOUNT_CRON_JOB") {
      val minutes = call.parameters["minutes"]!!.toLong()
      cronLogger().info("Starting $LOAD_PAYPAL_TRANSACTIONS_GMC_ACCOUNT_CRON_JOB")
      payPalApiService.loadBatchOfPayPalTransactions(
        minutes = minutes,
        cronJobName = LOAD_PAYPAL_TRANSACTIONS_GMC_ACCOUNT_CRON_JOB,
        account = PAYPAL_GMC
      )
        .let { cronLogger().info("Finished $LOAD_PAYPAL_TRANSACTIONS_GMC_ACCOUNT_CRON_JOB. Rows inserted $it") }
      call.respond(HttpStatusCode.OK)
    }

    mapOf(
      LOAD_TANGO_CARD_TRANSACTIONS_CRON_JOB to tangoCardApiService::loadBatchOfTangoCardTransactions,
      CREATE_PAY_PAL_INVOICES_CRON_JOB to invoicesService::createBatchOfPayPalInvoices,
      CREATE_TANGO_CARD_INVOICES_CRON_JOB to invoicesService::createBatchOfTangoCardInvoices,
      CREATE_TREMENDOUS_INVOICES_CRON_JOB to invoicesService::createBatchOfTremendousInvoices
    ).map { (cronJobName, method) ->
      route.get("/$cronJobName") {
        val minutes = call.parameters["minutes"]!!.toLong()
        cronLogger().info("Starting $cronJobName")
        method
          .invoke(minutes, cronJobName)
          .let { cronLogger().info("Finished $cronJobName. Rows inserted $it") }
        call.respond(HttpStatusCode.OK)
      }
    }
  }
}
