package com.justplayapps.service.accounting.tables

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.`java-time`.timestamp

object TremendousTransactionsDataTable : Table("accounting.tremendous_transaction_data") {
  val id = varchar("id", 100)
  val externalId = varchar("external_id", 100)
  val status = varchar("status", 15)
  val subtotal = decimal("subtotal", 15, 4)
  val total = decimal("total", 15, 4)
  val fees = decimal("fees", 15, 4)
  val createdAt = timestamp("created_at")
}
