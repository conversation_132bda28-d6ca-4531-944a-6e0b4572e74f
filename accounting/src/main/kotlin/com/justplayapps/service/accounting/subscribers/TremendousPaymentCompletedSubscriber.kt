package com.justplayapps.service.accounting.subscribers

import com.moregames.base.messaging.dto.TremendousPaymentCompletedEventDto
import com.moregames.base.messaging.push.GenericPushSubscriber
import javax.inject.Inject

class TremendousPaymentCompletedSubscriber @Inject constructor(
  private val paymentPersistenceService: PaymentPersistenceService,
) : GenericPushSubscriber<TremendousPaymentCompletedEventDto>(TremendousPaymentCompletedEventDto::class) {
  override suspend fun handle(message: TremendousPaymentCompletedEventDto) {
    paymentPersistenceService.saveTremendousTransaction(message)
  }

  override val url: String = "tremendous-payment-completed"
}
