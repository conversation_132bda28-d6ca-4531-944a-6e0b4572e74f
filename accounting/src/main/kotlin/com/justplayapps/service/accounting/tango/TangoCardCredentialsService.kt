package com.justplayapps.service.accounting.tango

import com.justplayapps.service.accounting.Secrets
import com.moregames.base.secret.Secret
import com.moregames.base.secret.SecretService
import com.tangocard.raas.Configuration
import com.tangocard.raas.Environments
import com.tangocard.raas.RaasClient

enum class TangoCardCredentialsService(
  private val platformName: String,
  private val platformKey: Secret,
  private val environment: Environments,
  val accountIdentifier: String,
  val customerIdentifier: String
) {
  PRODUCTION("SoftBakedAppsGmbH-920", Secrets.TANGO_CARD_PLATFORM_KEY_LIVE, Environments.PRODUCTION, "A60855040", "G43915119"),
  SANDBOX("SoftBakedAppsTest", Secrets.TANGO_CARD_PLATFORM_KEY_SANDBOX, Environments.SANDBOX, "BackedSoft", "BackedSoft");

  suspend fun client(secretService: SecretService): RaasClient {
    Configuration.environment = environment
    return RaasClient(platformName, secretService.secretValue(platformKey))
  }
}
