package com.moregames.commands.api

import com.moregames.base.app.BuildVariant
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.ktor.defaultApiStatusPage
import com.moregames.base.util.installLogging
import com.moregames.base.util.logger
import io.ktor.application.*
import io.ktor.features.*
import io.ktor.http.*
import io.ktor.response.*
import io.ktor.routing.*
import javax.inject.Inject
import javax.inject.Provider

class ApiManager @Inject constructor(
  private val buildVariant: Provider<BuildVariant>,
  private val featureFlagsFacade: FeatureFlagsFacade,
  private val commandsController: CommandsController,
  private val application: Application
) {

  private val logger = logger()

  fun initApi() = with(application) {
    installLogging(buildVariant.get(), featureFlagsFacade)
    install(StatusPages) {
      defaultApiStatusPage(buildVariant.get(), logger)
    }

    routing {
      initWarmup()
      commandsController.startRouting(this)
    }
  }

  private fun Routing.initWarmup() {
    get("/_ah/warmup") {
      call.respond(HttpStatusCode.OK)
    }
  }
}