package com.moregames.commands.api

import com.moregames.base.app.BuildVariant
import com.moregames.base.ktor.cors
import com.moregames.base.messaging.auth.HttpClientAuthInterceptor
import com.moregames.base.util.RandomGenerator
import com.moregames.commands.service.CommandsService
import io.ktor.application.*
import io.ktor.features.*
import io.ktor.http.*
import io.ktor.request.*
import io.ktor.response.*
import io.ktor.routing.*
import io.ktor.util.*
import kotlinx.serialization.Serializable
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CommandsController @Inject constructor(
  private val commandsService: CommandsService,
  private val randomGenerator: RandomGenerator,
  private val interceptor: HttpClientAuthInterceptor,
  private val buildVariant: BuildVariant
) {
  fun startRouting(routing: Route) = with(routing) {
    interService()
    route("/commands/{commandId}") {
      install(CORS, cors(buildVariant))
      get {
        val commandId = call.parameters.getOrFail("commandId")
        val command = commandsService.getResult(commandId)
        if (command == null) {
          call.respond(HttpStatusCode.NotFound, "command not found")
        } else if (!command.isFinished) {
          call.respond(HttpStatusCode(425, "Too early"), "Too early")
        } else {
          call.respond(command.payload)
        }
      }
    }
  }

  private fun Route.interService() {
    route("/inter/commands") {
      with(interceptor) { installInterceptor() }
      post("/create") {
        val req = call.receive<CreateCommandRequest>()
        val commandId = req.commandId ?: randomGenerator.nextUUID()
        commandsService.saveCommand(commandId)
        call.respond(CreateCommandResponse(commandId))
      }
      post("/{commandId}/complete") {
        val commandId = call.parameters.getOrFail("commandId")
        val payload = call.receive<Map<String, String>>()
        commandsService.saveResult(commandId, payload)
        call.respond(HttpStatusCode.OK)
      }
    }
  }

  @Serializable
  data class CreateCommandRequest(
    val commandId: String? = null,
  )

  @Serializable
  data class CreateCommandResponse(
    val commandId: String,
  )
}