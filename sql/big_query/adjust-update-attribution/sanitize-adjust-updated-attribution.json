{"dataSourceId": "scheduled_query", "datasetRegion": "us-central1", "destinationDatasetId": "analytics_sanitized", "displayName": "sanitize-adjust-updated-attribution", "emailPreferences": {}, "name": "<generated on creation>", "nextRunTime": "<generated on creation>", "ownerInfo": {"email": "<generated on creation>"}, "params": {"destination_table_name_template": "adjust-updated-attribution", "query": "select campaignName, trackerId, trackerName, adNetwork, googleAdId, userId, adjustId, ip, countryCode, installedAt, packageName, osVersion, device, userAgent, limitAdTracking, isOrganic, adgroupName, creativeName, googleStoreReferrer, appSetId, outdatedTracker, outdatedTrackerName, attributionUpdatedAt, activityKind, createdAt from `playspot-server-dev`.analytics.`adjust-updated-attribution` WHERE DATE(createdAt) = DATE_SUB(@run_date, INTERVAL 1 DAY) GROUP BY campaignName, trackerId, trackerName, adNetwork, googleAdId, userId, adjustId, ip, countryCode, installedAt, packageName, osVersion, device, userAgent, limitAdTracking, isOrganic, adgroupName, creativeName, googleStoreReferrer, appSetId, outdatedTracker, outdatedTrackerName, attributionUpdatedAt, activityKind, createdAt;", "write_disposition": "WRITE_APPEND"}, "schedule": "every day 01:30", "scheduleOptions": {"startTime": "2023-12-07T01:30:00Z"}, "state": "<generated on creation>", "updateTime": "<generated on creation>", "userId": "<generated on creation>"}