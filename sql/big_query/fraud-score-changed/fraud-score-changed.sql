CREATE TABLE `playspot-server-dev.analytics_test.fraud-score-changed` (
  userId                 STRING(36) NOT NULL,
  amount                 INTEGER  NOT NULL,
  reasonType             STRING(50) NOT NULL,
  reasonUniqueId         STRING(50) NOT NULL,
  description            STRING(200) NOT NULL,
  createdAt              TIMESTAMP NOT NULL
)
PARTITION BY DATE(createdAt)
OPTIONS(
    require_partition_filter=true
);

CREATE TABLE `playspot-server-dev.analytics.fraud-score-changed` (
  userId                 STRING(36) NOT NULL,
  amount                 INTEGER  NOT NULL,
  reasonType             STRING(50) NOT NULL,
  reasonUniqueId         STRING(50) NOT NULL,
  description            STRING(200) NOT NULL,
  createdAt              TIMESTAMP NOT NULL
)
PARTITION BY DATE(createdAt)
OPTIONS(
    require_partition_filter=true
);