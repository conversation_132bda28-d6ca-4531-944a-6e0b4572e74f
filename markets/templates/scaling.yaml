{{- define "scaling_block"}}
{{ .scaling }}:
{{- if .instances }}
  instances: {{ .instances }}
{{- end }}
{{- if .target_throughput_utilization }}
  target_throughput_utilization: {{ .target_throughput_utilization }}
{{- end }}
{{- if .max_concurrent_requests }}
  max_concurrent_requests: {{ .max_concurrent_requests }}
{{- end }}
{{- if .target_cpu_utilization }}
  target_cpu_utilization: {{ .target_cpu_utilization }}
{{- end }}
{{- if .max_instances }}
  max_instances: {{ .max_instances }}
{{- end }}
{{- if .min_instances }}
  min_instances: {{ .min_instances }}
{{- end }}
{{- if .min_pending_latency }}
  min_pending_latency: {{ .min_pending_latency }}
{{- end }}
{{- if .min_idle_instances }}
  min_idle_instances: {{ .min_idle_instances }}
{{- end }}
{{- if .max_idle_instances }}
  max_idle_instances: {{ .max_idle_instances }}
{{- end }}
{{- end }}