runtime: java21
instance_class: B4
service: router
manual_scaling:
  instances: 1
inbound_services:
  - warmup
vpc_access_connector:
  name: projects/playspot-server-dev/locations/us-central1/connectors/justplay-us-vpc
entrypoint: "java
      -javaagent:opentelemetry-javaagent.jar
      -Dotel.traces.exporter=google_cloud_trace
      -Dotel.propagators=oneway-gcp
      -Dotel.traces.sampler=traceidratio
      -Dotel.traces.sampler.arg=0.01
      -Dotel.javaagent.extensions=exporter-auto-0.27.0-alpha-shaded.jar
      -Dotel.javaagent.experimental.extensions=propagators-gcp-0.27.0-alpha.jar
      -Dotel.java.global-autoconfigure.enabled=true
      -Dlogback.configurationFile=logback-gcp-test.xml
      --add-opens=java.base/java.lang=ALL-UNNAMED -jar router-all.jar"
env_variables:
  NODE_ENV: test
  GCLOUD_REGION: us-central1
  JUSTPLAY_MARKET: none
  redis_host: 'standard.redis.playspot-server-dev.justplay.internal'
  redis_port: '6379'
  OTEL_METRICS_EXPORTER: none
  OTEL_LOGS_EXPORTER: none
  OTEL_SERVICE_NAME: router