package com.justplayapps.service.router.ip

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.ipregistry.IpRegistryResponseDto
import com.moregames.base.user.dto.UserRequestMetadata
import com.moregames.base.util.mock
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import kotlin.test.assertFailsWith

class IpServiceTest {
  private val ipRegistryServiceClient: IpRegistryServiceClient = mock()
  private val ipsPersistenceService: IpsPersistenceService = mock()

  private val service = IpService(ipRegistryServiceClient, ipsPersistenceService)

  private companion object {
    const val countryCode = "US"
    const val ip = "ip"
    var securityDto = IpRegistryResponseDto.SecurityDto(
      isAbuser = false,
      isAnonymous = false,
      isAttacker = false,
      isBogon = false,
      isCloudProvider = false,
      isProxy = false,
      isRelay = false,
      isThreat = false,
      isTor = false,
      isTorExit = false,
      isVpn = false
    )
  }

  @Test
  fun `SHOULD throw error ON extractIpData WHEN Forwarded-For is not set`() {
    runBlocking {
      assertFailsWith<IllegalStateException> {
        service.extractIpCountryCode(
          UserRequestMetadata(
            forwardedIp = null,
            ip = "123",
            countryCode = "en",
            loadBalancerCountryData = "en",
            forwardedIpRaw = "123"
          )
        )
      }.let {
        assertThat(it.message, "Can't determine user ip")
      }
    }
  }

  @Test
  fun `SHOULD return ip and country ON extractIpData WHEN Forwarded-For contains one IP`() {
    val userRequestMetadata = UserRequestMetadata(
      countryCode = countryCode,
      ip = ip,
      forwardedIp = ip,
      loadBalancerCountryData = countryCode,
      forwardedIpRaw = ip
    )

    val actual = runBlocking { service.extractIpCountryCode(userRequestMetadata) }

    assertThat(actual).isEqualTo(IpCountryCode(ip = ip, countryCode = countryCode, ipRegistryCall = false))
    verifyNoInteractions(ipsPersistenceService)
    verifyNoInteractions(ipRegistryServiceClient)
  }

  @Test
  fun `SHOULD return data from db ON extractIpData WHEN Forwarded-For ip does not match AND result is not cached but stored in db`() {
    val userRequestMetadata = UserRequestMetadata(
      countryCode = "proxyCountryCode",
      ip = "proxyIp",
      forwardedIp = ip,
      loadBalancerCountryData = countryCode,
      forwardedIpRaw = ip
    )

    ipsPersistenceService.mock({ getByIp(ip) }, countryCode)

    val actual = runBlocking {
      service.extractIpCountryCode(userRequestMetadata)
    }

    assertThat(actual).isEqualTo(IpCountryCode(ip = ip, countryCode = countryCode, true))
    verifyNoInteractions(ipRegistryServiceClient)
  }

  @Test
  fun `SHOULD return data from ipregistry client ON extractIpData WHEN Forwarded-For ip does not match AND no data cached or stored`() {
    val userRequestMetadata = UserRequestMetadata(
      countryCode = "proxyCountryCode",
      ip = "proxyIp",
      forwardedIp = ip,
      loadBalancerCountryData = countryCode,
      forwardedIpRaw = ip
    )
    val location = IpRegistryResponseDto.LocationDto(
      country = IpRegistryResponseDto.LocationDto.CountryDto(code = countryCode, name = "country name"),
      region = IpRegistryResponseDto.LocationDto.RegionDto("code", "region"),
      city = "city",
    )

    ipRegistryServiceClient.mock(
      { getIpRegistryResult(ip) },
      IpRegistryResponseDto(ip = ip, location, securityDto, timeZone = IpRegistryResponseDto.TimeZoneDto("timeZone"))
    )

    val actual = runBlocking { service.extractIpCountryCode(userRequestMetadata) }

    assertThat(actual).isEqualTo(IpCountryCode(ip = ip, countryCode = countryCode, ipRegistryCall = true))
    verifyBlocking(ipsPersistenceService) {
      save(ip, countryCode)
    }
  }

  @Test
  fun `SHOULD return ZZ country ON extractIpData WHEN Forwarded-For ip does not match and country not detected by ipregistry`() {
    val userRequestMetadata = UserRequestMetadata(
      countryCode = "proxyCountryCode",
      ip = "proxyIp",
      forwardedIp = ip,
      loadBalancerCountryData = countryCode,
      forwardedIpRaw = ip
    )

    ipRegistryServiceClient.mock({ getIpRegistryResult(ip) }, null)

    val actual = runBlocking { service.extractIpCountryCode(userRequestMetadata) }

    assertThat(actual).isEqualTo(IpCountryCode(ip = ip, countryCode = "ZZ", ipRegistryCall = true))
  }
}