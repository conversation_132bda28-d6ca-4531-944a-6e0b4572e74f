<configuration>
    <appender name="CLOUD" class="com.google.cloud.logging.logback.LoggingAppender">
        <log>application.log</log>
        <resourceType>gae_app</resourceType>
        <loggingEventEnhancer>com.moregames.base.util.LogEnhancer</loggingEventEnhancer>
    </appender>

    <appender name="CRON" class="ch.qos.logback.core.FileAppender">
        <file>/var/log/router_cron.log</file>
        <append>true</append>
        <!-- set immediateFlush to false for much higher logging throughput -->
        <immediateFlush>true</immediateFlush>
        <!-- encoders are assigned the type
             ch.qos.logback.classic.encoder.PatternLayoutEncoder by default -->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <root level="DEBUG">
        <appender-ref ref="CLOUD"/>
    </root>
    <logger name="io.grpc.netty" level="info"/>
    <logger name="io.netty" level="warn"/>
    <logger name="sun.net" level="info"/>
    <logger name="com.zaxxer.hikari" level="info"/>
    <logger name="cron" additivity="false" level="debug">
        <appender-ref ref="CRON"/>
    </logger>
</configuration>
