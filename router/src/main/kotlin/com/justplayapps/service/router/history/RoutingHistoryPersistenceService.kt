package com.justplayapps.service.router.history

import com.google.inject.Inject
import com.justplayapps.service.router.routing.Market
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.dto.AppVersionDto
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.insert
import javax.inject.Singleton

@Singleton
class RoutingHistoryPersistenceService @Inject constructor(database: Database) :
  BasePersistenceService(database) {

  suspend fun save(
    ip: String,
    countryCode: String,
    appVersion: AppVersionDto,
    market: Market?,
    userId: String?,
    error: String?
  ) {
    dbQuery {
      RoutingHistoryTable.insert {
        it[RoutingHistoryTable.ip] = ip
        it[RoutingHistoryTable.countryCode] = countryCode
        it[RoutingHistoryTable.appPlatform] = appVersion.platform.name
        it[RoutingHistoryTable.appVersion] = appVersion.version
        it[RoutingHistoryTable.market] = market?.name
        it[RoutingHistoryTable.userId] = userId
        it[RoutingHistoryTable.error] = error
      }
    }
  }
}