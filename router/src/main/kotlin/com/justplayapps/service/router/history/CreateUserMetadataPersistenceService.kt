package com.justplayapps.service.router.history

import com.google.inject.Inject
import com.moregames.base.base.BasePersistenceService
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.insert
import javax.inject.Singleton

@Singleton
class CreateUserMetadataPersistenceService @Inject constructor(database: Database) :
  BasePersistenceService(database) {

  suspend fun save(
    xForwardedFor: String?,
    xForwardedForRaw: String?,
    xAppengineUserIp: String?,
    xAppengineCountry: String?,
    xAppengineRegion: String?,
    xAppengineCity: String?,
    xClientGeoLocation: String?,
    ipRegistryCountryCode: String?,
    countryCodeOverride: Boolean,
    countryCode: String,
  ) {
    dbQuery {
      CreateUserMetadataTable.insert {
        it[CreateUserMetadataTable.xForwardedFor] = xForwardedFor
        it[CreateUserMetadataTable.xForwardedForRaw] = xForwardedForRaw
        it[CreateUserMetadataTable.xAppengineUserIp] = xAppengineUserIp
        it[CreateUserMetadataTable.xAppengineCountry] = xAppengineCountry
        it[CreateUserMetadataTable.xAppengineRegion] = xAppengineRegion
        it[CreateUserMetadataTable.xAppengineCity] = xAppengineCity
        it[CreateUserMetadataTable.xClientGeoLocation] = xClientGeoLocation
        it[CreateUserMetadataTable.ipRegistryCountryCode] = ipRegistryCountryCode
        it[CreateUserMetadataTable.countryCodeOverride] = countryCodeOverride
        it[CreateUserMetadataTable.countryCode] = countryCode
      }
    }
  }
}