package com.justplayapps.service.router.util

import com.google.api.client.http.HttpTransport
import com.google.api.client.http.javanet.NetHttpTransport
import com.google.api.client.json.jackson2.JacksonFactory
import com.google.api.services.playintegrity.v1.PlayIntegrity
import com.google.api.services.playintegrity.v1.PlayIntegrityRequestInitializer
import com.google.api.services.playintegrity.v1.PlayIntegrityScopes
import com.google.auth.Credentials
import com.google.auth.http.HttpCredentialsAdapter
import com.google.auth.oauth2.GoogleCredentials
import com.google.common.annotations.VisibleForTesting
import com.moregames.base.secret.Secret
import com.moregames.base.secret.SecretService
import javax.inject.Inject
import javax.inject.Singleton

typealias CredentialsProvider = suspend (Secret) -> Credentials

@Singleton
class PlayIntegrityClientProvider @Inject constructor(
  private val secretService: SecretService
) {

  private var transport: HttpTransport = NetHttpTransport()
  private var credentialsProvider: CredentialsProvider = {
    GoogleCredentials
      .fromStream(secretService.secretValue(it).byteInputStream())
      .createScoped(PlayIntegrityScopes.PLAYINTEGRITY)
  }

  @VisibleForTesting
  constructor(secretService: SecretService, transport: HttpTransport, credentialsProvider: CredentialsProvider) : this(secretService) {
    this.transport = transport
    this.credentialsProvider = credentialsProvider
  }

  suspend fun buildClient(applicationId: String, secret: Secret): PlayIntegrity {
    val credentials = HttpCredentialsAdapter(credentialsProvider(secret))

    return PlayIntegrity.Builder(transport, JacksonFactory(), credentials)
      .setApplicationName(applicationId)
      .setGoogleClientRequestInitializer(PlayIntegrityRequestInitializer())
      .build()
  }
}
