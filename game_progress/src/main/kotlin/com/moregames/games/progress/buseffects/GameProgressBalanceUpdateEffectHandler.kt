package com.moregames.games.progress.buseffects

import com.google.inject.Inject
import com.google.inject.Singleton
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.AsyncEffect
import com.moregames.base.bus.Effect
import com.moregames.base.bus.EffectHandler
import com.moregames.base.bus.MessageBus
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.dto.AppPlatform
import com.moregames.base.messaging.DelayedMessagePublisher
import com.moregames.base.messaging.UserBalanceUpdateDto
import com.moregames.base.messaging.dto.FirstGamePlayedEventDto
import com.moregames.base.messaging.dto.GameProgressBqEventDto
import com.moregames.base.messaging.dto.NotifyAfterFirstCoinsForGameTaskDto
import com.moregames.base.util.Constants.JP_USER_FOUND_BY
import com.moregames.base.util.RandomGenerator
import com.moregames.base.util.TimeService
import com.moregames.games.coingoal.CoinGoalReachedService
import com.moregames.games.progress.UserBalanceUpdate
import com.moregames.games.progress.UserGameProgressEvent
import com.moregames.games.sessions.GameSessionsService
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit.HOURS

@Singleton
class GameProgressBalanceUpdateEffectHandler @Inject constructor(
  private val bigQueryEventPublisher: BigQueryEventPublisher,
  private val delayedMessagePublisher: DelayedMessagePublisher,
  private val messageBus: MessageBus,
  private val randomGenerator: RandomGenerator,
  private val applicationConfig: ApplicationConfig,
  private val timeService: TimeService,
  private val gameSessionsService: GameSessionsService,
  private val coinGoalReachedService: CoinGoalReachedService,
) {

  @EffectHandler
  suspend fun handleSendGameProgressBqEffect(effect: SendGameProgressBqEffect) {
    with(effect) {
      bigQueryEventPublisher.publish(
        GameProgressBqEventDto(
          eventId = randomGenerator.nextUUID(),
          userId = gameProgressEvent.userId,
          gameId = gameId,
          coins = balanceUpdate.coins,
          method = balanceUpdate.method.name,
          userIdParam = gameProgressEvent.parameters["user_id"],
          idfaParam = gameProgressEvent.parameters["gaid"],
          idfvParam = gameProgressEvent.parameters["idfv"],
          applicationIdParam = gameProgressEvent.applicationId,
          gameVersionParam = gameProgressEvent.parameters["gameVersion"],
          scoreParam = gameProgressEvent.score?.toString(),
          amountParam = gameProgressEvent.amount?.toString(),
          isBossParam = gameProgressEvent.parameters["is_boss"]?.toBoolean(),
          isNewRecordParam = gameProgressEvent.parameters["is_new_record"]?.toBoolean(),
          userFoundBy = gameProgressEvent.parameters[JP_USER_FOUND_BY],
          market = applicationConfig.justplayMarket,
          platform = gameProgressEvent.platform.name,
          createdAt = timeService.now()
        )
      )
    }
  }

  @EffectHandler
  suspend fun handleTrackNewGameSessionEffect(effect: TrackNewGameSessionEffect) {
    with(effect) {
      gameSessionsService.trackNewGameSession(
        userId = userId,
        gameId = gameId,
        lastPlayedAt = lastPlayedAt,
        initialCoinBalance = initialCoinsBalance
      )
    }
  }

  @EffectHandler
  suspend fun handleNotifyPlaytimeOnBalanceUpdateEffect(effect: NotifyPlaytimeOnBalanceUpdateEffect) {
    with(effect) {
      messageBus.publish(
        UserBalanceUpdateDto(
          userId = userId,
          coinsEarned = coinsEarned,
          coinsEarnedEm2 = coinsEarnedEm2,
          commandId = commandId,
          forceCommandNotification = forceCommandNotification,
          gameId = gameId,
        )
      )
    }
  }

  @EffectHandler
  suspend fun handleFirstCoinsForGameNotificationEffect(effect: FirstCoinsForGameNotificationEffect) {
    val notificationDelay = 2L
    with(effect) {
      delayedMessagePublisher.publish(
        message = NotifyAfterFirstCoinsForGameTaskDto(userId, gameId),
        delayUntil = timeService.now().plus(notificationDelay, HOURS)
      )
    }
  }

  @EffectHandler
  suspend fun handleFirstGamePlayedEventEffect(effect: FirstGamePlayedEventEffect) {
    with(effect) {
      messageBus.publish(
        FirstGamePlayedEventDto(
          userId = userId,
          applicationId = applicationId,
          platform = platform
        )
      )
    }
  }

  @EffectHandler
  suspend fun handleCheckIfGameCoinGoalReachedEffect(effect: CheckIfGameCoinGoalReachedEffect) {
    coinGoalReachedService.emitCoinGoalReachedEventIfGoalIsReached(effect.userId)
  }
}

data class SendGameProgressBqEffect(
  val gameId: Int,
  val gameProgressEvent: UserGameProgressEvent,
  val balanceUpdate: UserBalanceUpdate
) : AsyncEffect

data class CheckIfGameCoinGoalReachedEffect(
  val userId: String,
) : AsyncEffect

data class TrackNewGameSessionEffect(
  val userId: String,
  val gameId: Int,
  val lastPlayedAt: Instant?,
  val initialCoinsBalance: BigDecimal
) : Effect

data class NotifyPlaytimeOnBalanceUpdateEffect(
  val userId: String,
  val coinsEarned: Int,
  val coinsEarnedEm2: BigDecimal? = null,
  val gameId: Int,
  val commandId: String? = null,
  val forceCommandNotification: Boolean = false,
) : Effect

data class FirstCoinsForGameNotificationEffect(val userId: String, val gameId: Int) : AsyncEffect

data class FirstGamePlayedEventEffect(val userId: String, val applicationId: String, val platform: AppPlatform) : AsyncEffect
