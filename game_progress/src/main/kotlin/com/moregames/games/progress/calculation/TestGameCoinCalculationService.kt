package com.moregames.games.progress.calculation

import com.moregames.games.progress.Method
import com.moregames.games.progress.UserBalanceUpdate
import com.moregames.games.progress.UserGameProgressEvent

class TestGameCoinCalculationService(
  private val prodGameCoinsCalculationService: GameCoinsCalculationService
) : GameCoinsCalculationService {

  private val overrideUserIds: MutableSet<String> = mutableSetOf()

  override suspend fun calculateCoins(userGameProgressEvent: UserGameProgressEvent): UserBalanceUpdate {
    if (isInTestMode(userGameProgressEvent.userId)) {
      return UserBalanceUpdate(userGameProgressEvent.amount!!, Method.ADD)
    }
    return prodGameCoinsCalculationService.calculateCoins(userGameProgressEvent)
  }

  fun setTestForUser(userId: String) = overrideUserIds.add(userId)
  fun removeTestMode(userId: String) = overrideUserIds.remove(userId)
  fun isInTestMode(userId: String) = userId in overrideUserIds
}