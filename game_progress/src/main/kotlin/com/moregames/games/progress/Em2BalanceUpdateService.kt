package com.moregames.games.progress

import com.google.inject.Inject
import com.google.protobuf.StringValue
import com.justplayapps.service.rewarding.earnings.proto.AddUserCoinsMessageKt.addGameCoinsMessage
import com.justplayapps.service.rewarding.earnings.proto.addUserCoinsMessage
import com.moregames.base.bus.MessageBus
import com.moregames.base.coins.UserQualityService
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.messaging.commands.commandId
import com.moregames.base.util.ApplicationId
import com.moregames.base.util.ApplicationId.SOLITAIRE_VERSE_APP_ID
import com.moregames.base.util.ApplicationId.WOODEN_PUZZLE_APP_ID
import com.moregames.base.util.TimeService
import com.moregames.base.util.toBeginningOf5MinInterval
import com.moregames.base.util.toProto
import com.moregames.games.serviceclient.RewardingFacade
import com.moregames.games.serviceclient.RewardingFacade.Companion.isGameAllowedForEm2Participants
import java.math.BigDecimal
import javax.inject.Singleton
import kotlin.coroutines.coroutineContext
import kotlin.math.roundToInt
import kotlin.math.sqrt

@Singleton
class Em2BalanceUpdateService @Inject constructor(
  private val userGameBalancePersistenceService: UserGameBalancePersistenceService,
  private val timeService: TimeService,
  private val gamesEqualizingService: GamesEqualizingService,
  private val coinsExchangeRateService: CoinsExchangeRateService,
  private val userQualityService: UserQualityService,
  private val progressSettingsService: ProgressSettingsService,
  private val em2CoinsLimitationsService: Em2CoinsLimitationsService,
  private val em2IncreaseCoinsInTimeCoefficientsService: EM2IncreaseCoinsInTimeCoeffsService,
  private val em2IncreaseCoinsRandomlyService: EM2IncreaseCoinsRandomlyService,
  private val em2CoinsRetentionService: Em2CoinsRetentionService,
  private val gameCoinGoalProgressService: GameCoinGoalProgressService,
  private val balanceUpdateValidationService: BalanceUpdateValidationService,
  private val rewardingFacade: RewardingFacade,
  private val em2IncreaseCoinsOnNewGameService: Em2IncreaseCoinsOnNewGameService,
  private val em2TargetCorrectionService: Em2TargetCorrectionService,
  private val messageBus: MessageBus,
  private val em2IosBoostedGameService: Em2IosBoostedGameService
) : BalanceUpdateService {

  companion object {
    const val TM_COINS_FLATTENING_MULTIPLIER: Double = 4.44
    val EM2_COINS_EXCHANGE_BOOSTER_SOL_ZEN_2P0 = BigDecimal("2.0")
  }

  override suspend fun update(
    balanceUpdate: UserBalanceUpdate,
    userId: String,
    gameId: Int,
    applicationId: String,
    appPlatform: AppPlatform,
    forceCommandNotification: Boolean,
  ): UserBalanceUpdateResultDto? {
    if (!isGameAllowedForEm2Participants(applicationId))
      return null

    val existingCoinsData = userGameBalancePersistenceService.getGameFractionalCoinsDataByUser(
      userId = userId,
      gameId = gameId,
      beginningOf5MinInterval = timeService.now().toBeginningOf5MinInterval()
    )
    val isFirstCoinsForGame = (existingCoinsData.coins == BigDecimal.ZERO)
    val isFirstCoinsEver = isFirstCoinsForGame && !userGameBalancePersistenceService.hasCoinsForAnyGameEm2(userId)

    val calculatedCoinsEarned = balanceUpdate.applyReplaceMethodEm2(existingCoinsData)

    if (!balanceUpdateValidationService.isEarnedCoinsValid(userId, gameId, calculatedCoinsEarned, balanceUpdate, existingCoinsData.calculatedCoins))
      return null

    val (validCoins, blockedCoins) = em2CoinsLimitationsService.applyGameCoinsSpeedLimitations(
      gameId = gameId,
      calculatedCoinsEarned = applyTmCoinsFlattening(applicationId, calculatedCoinsEarned),
      existingCoinsData = existingCoinsData
    )
    // equalization needed, as coins cost calculated on equalled overall coins (and different games give different revenue)
    val gamesEqualledCoins = if (appPlatform == IOS) {
      gamesEqualizingService.iosEqualizedGameCoins(gameId, validCoins)
    } else {
      gamesEqualizingService.equalizedGameCoins(gameId, validCoins)
    }
      .let { coins ->
        gameCoinsMultiplier(applicationId)
          ?.let { it * coins }
          ?: coins
      }
    val estimatedAvgRevenue = gamesEqualledCoins * coinsExchangeRateService.getExchangeRate()
    val coinsAlignedWithOfferwall = estimatedAvgRevenue * progressSettingsService.getCoinsToUsdConversionRatio()

    // default == 1 still estimated
    val userPersonalCoefficients = userQualityService.getUserPersonalCoeff(userId)
    val personalizedCoins = coinsAlignedWithOfferwall
      .multiply(userPersonalCoefficients)
      .multiply(em2IncreaseCoinsInTimeCoefficientsService.getCoeff(userId))
      .multiply(em2TargetCorrectionService.getUserTargetCorrection(userId))
      .multiply(em2IncreaseCoinsRandomlyService.getCoeff())
      .multiply(em2IncreaseCoinsOnNewGameService.getCoeff(userId, gameId, existingCoinsData))
      .multiply(em2IosBoostedGameService.getCoeff(userId, gameId))
      .let { coins -> em2CoinsRetentionService.recalculateAndTrack(userId, coins) }
      .let { coins -> coins + rewardingFacade.seizeSomeCoinsFromStash(userId) }

    userGameBalancePersistenceService.addFractionalGameCoinsToUser(
      userId = userId,
      gameId = gameId,
      coinsEarned = personalizedCoins,
      calculatedCoins = calculatedCoinsEarned,
      blockedCoins = blockedCoins,
    )
    gameCoinGoalProgressService.incrementBalance(userId, gameId, personalizedCoins)

    messageBus.publish(
      addUserCoinsMessage {
        this.userId = userId
        this.platform = appPlatform.toProto()
        this.gameCoins = addGameCoinsMessage {
          this.coinsEarned = personalizedCoins.toProto()
          this.forceCommandNotification = forceCommandNotification
          this.gameId = gameId
          coroutineContext.commandId()?.let { this.commandId = StringValue.of(it) }
        }
      }
    )

    return UserBalanceUpdateResultDto(
      userId = userId,
      gameId = gameId,
      lastPlayedAt = existingCoinsData.lastPlayedAt,
      initialCoinsBalance = existingCoinsData.coins,
      em2CoinsEarned = personalizedCoins,
      isFirstCoinsForGame = isFirstCoinsForGame,
      isFirstCoinsEver = isFirstCoinsEver,
      applicationId = applicationId,
      appPlatform = appPlatform,
    )
  }

  private fun applyTmCoinsFlattening(applicationId: String?, calculatedCoinsEarned: Int) =
    calculatedCoinsEarned.takeIf { applicationId != ApplicationId.TREASURE_MASTER_APP_ID }
      ?: calculateTmFlatteningCoins(calculatedCoinsEarned)

  fun calculateTmFlatteningCoins(coins: Int): Int =
    sqrt(coins.toDouble())
      .times(TM_COINS_FLATTENING_MULTIPLIER)
      .roundToInt()

  fun gameCoinsMultiplier(applicationId: String?): BigDecimal? =
    if (
      applicationId in listOf(SOLITAIRE_VERSE_APP_ID, WOODEN_PUZZLE_APP_ID)
    ) EM2_COINS_EXCHANGE_BOOSTER_SOL_ZEN_2P0
    else null
}
