package com.moregames.games.serviceclient

import com.google.inject.Inject
import com.google.inject.Provider
import com.justplayapps.service.rewarding.earnings.proto.EmApiGrpc
import com.justplayapps.service.rewarding.earnings.proto.getApplovinInterRevenueRequest
import com.justplayapps.service.rewarding.earnings.proto.getUninflatedGoalCoinsRequest
import com.justplayapps.service.rewarding.earnings.proto.seizeSomeCoinsFromStashRequest
import com.moregames.base.grpc.client.GenericFacade
import com.moregames.base.util.ApplicationId.BUBBLE_CHIEF_APP_ID
import com.moregames.base.util.ApplicationId.EMOJICLICKERS_APP_ID
import com.moregames.base.util.ApplicationId.PIN_MASTER_APP_ID
import com.moregames.base.util.FOREVERGREEN_PREFIX
import com.moregames.base.util.GIMICA_PREFIX
import com.moregames.base.util.fromProto
import com.moregames.base.util.toProto
import java.math.BigDecimal
import java.time.Instant

class RewardingFacade @Inject constructor(
  private val emApi: Provider<EmApiGrpc.EmApiStub>,
  private val genericFacade: GenericFacade,
) {

  companion object {
    fun isGameAllowedForEm2Participants(applicationId: String) = when {
      applicationId == EMOJICLICKERS_APP_ID -> false
      applicationId.startsWith(GIMICA_PREFIX) -> true
      applicationId.startsWith(FOREVERGREEN_PREFIX) -> true
      applicationId == PIN_MASTER_APP_ID -> true
      applicationId == BUBBLE_CHIEF_APP_ID -> true
      else -> false
    }
  }

  suspend fun seizeSomeCoinsFromStash(userId: String): BigDecimal {
    return genericFacade.get(
      emApi.get()::seizeSomeCoinsFromStash,
      seizeSomeCoinsFromStashRequest {
        this.userId = userId
      }
    ).amount.fromProto()
  }

  suspend fun getApplovinInterRevenue(userId: String, after: Instant): BigDecimal {
    return genericFacade.get(
      emApi.get()::getApplovinInterRevenue,
      getApplovinInterRevenueRequest {
        this.userId = userId
        this.after = after.toProto()
      }
    ).amount.fromProto()
  }

  suspend fun loadUninflatedGoalCoins(userId: String): Int {
    return genericFacade.get(
      emApi.get()::getUninflatedGoalCoins,
      getUninflatedGoalCoinsRequest {
        this.userId = userId
      }
    ).goalCoins
  }
}