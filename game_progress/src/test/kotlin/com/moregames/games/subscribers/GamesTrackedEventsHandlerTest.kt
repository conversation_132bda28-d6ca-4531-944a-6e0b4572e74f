package com.moregames.games.subscribers

import com.justplayapps.games.events.*
import com.justplayapps.orchestrator.events.userLocatedGameEvent
import com.moregames.base.junit.MockExtension
import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.games.progress.GameChallengeProgressService
import com.moregames.games.progress.GameOverallProgressService
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.verify

@ExtendWith(MockExtension::class)
class GamesTrackedEventsHandlerTest(
  private val gameChallengeProgressService: GameChallengeProgressService,
  private val gamesProgressEventsService: GameOverallProgressService,
) {

  private val underTest = GamesTrackedEventsHandler(
    gameChallengeProgressService = gameChallengeProgressService,
    gamesProgressEventsService = gamesProgressEventsService,
  )

  @Test
  fun `SHOULD publish ScoreCompletedProgressDto ON game win`() = runBlocking {
    val event = userLocatedGameEvent {
      userId = "userId"
      gameEvent = gameEvent {
        packageId = "packageId"
        gameWin = gameWin {
          score = 100
          isHighscore = true
          totalGamesCompleted = 10
        }
      }
    }

    underTest.handleGameEvent(event)

    verify(gameChallengeProgressService).handleChallengeProgress(
      UserChallengeProgressDto.ScoreCompletedProgressDto(
        userId = "userId",
        applicationId = "packageId",
        score = 100,
        isHighScore = true
      )
    )
    verify(gamesProgressEventsService).handleGameWin(
      userId = "userId",
      applicationId = "packageId",
      totalGamesCompleted = 10,
    )
  }

  @Test
  fun `SHOULD publish ScoreCompletedProgressDto ON game fail`() = runBlocking {
    val event = userLocatedGameEvent {
      userId = "userId"
      gameEvent = gameEvent {
        packageId = "packageId"
        gameFail = gameFail {
          score = 100
          isHighscore = true
        }
      }
    }

    underTest.handleGameEvent(event)

    verify(gameChallengeProgressService).handleChallengeProgress(
      UserChallengeProgressDto.ScoreCompletedProgressDto(
        userId = "userId",
        applicationId = "packageId",
        score = 100,
        isHighScore = true
      )
    )
  }

  @Test
  fun `SHOULD publish ScoreProgressDto ON score update`() = runBlocking {
    val event = userLocatedGameEvent {
      userId = "userId"
      gameEvent = gameEvent {
        packageId = "packageId"
        scoreUpdate = scoreUpdate {
          score = 100
        }
      }
    }

    underTest.handleGameEvent(event)

    verify(gameChallengeProgressService).handleChallengeProgress(
      UserChallengeProgressDto.ScoreProgressDto(
        userId = "userId",
        applicationId = "packageId",
        score = 100
      )
    )
  }

  @Test
  fun `SHOULD publish LevelIdProgressDto ON level completed`() = runBlocking {
    val event = userLocatedGameEvent {
      userId = "userId"
      gameEvent = gameEvent {
        packageId = "packageId"
        levelComplete = levelComplete {
          index = 1
        }
      }
    }

    underTest.handleGameEvent(event)

    verify(gameChallengeProgressService).handleChallengeProgress(
      UserChallengeProgressDto.LevelIdProgressDto(
        userId = "userId",
        applicationId = "packageId",
        levelId = "1"
      )
    )
    verify(gamesProgressEventsService).handleLevelComplete(
      userId = "userId",
      applicationId = "packageId",
      level = 1,
    )
  }
}