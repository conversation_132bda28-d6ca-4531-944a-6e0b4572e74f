package com.moregames.games.progress

import assertk.assertThat
import assertk.assertions.isEqualByComparingTo
import com.moregames.base.app.BuildVariant
import com.moregames.base.util.mock
import com.moregames.base.util.testIoScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verifyBlocking
import java.math.BigDecimal

@OptIn(ExperimentalCoroutinesApi::class)
class GameEqualizerCoeffsServiceTest {

  private val gameEqualizerCoeffsPersistenceService: GameEqualizerCoeffsPersistenceService = mock()
  private val testScope = TestScope()

  private val service = GameEqualizerCoeffsService(
    buildVariant = BuildVariant.PRODUCTION,
    coroutineScope = { testIoScope(testScope) },
    gameEqualizerCoeffsPersistenceService = gameEqualizerCoeffsPersistenceService,
  )

  init {
    gameEqualizerCoeffsPersistenceService.mock(
      { getGameEqualizerCoeffs() },
      mapOf(
        "app-1" to BigDecimal("0.05"),
        "app-2" to BigDecimal("42"),
      )
    )
  }

  @Test
  fun `SHOULD return game coefficient ON getGameApplicationIdToEqualizerCoeff`() = testScope.runTest {
    service.getGameApplicationIdToEqualizerCoeff("app-2") // cache check
    val actual = service.getGameApplicationIdToEqualizerCoeff("app-1")

    assertThat(actual).isEqualByComparingTo(BigDecimal("0.05"))

    verifyBlocking(gameEqualizerCoeffsPersistenceService, times(1)) { getGameEqualizerCoeffs() }
  }

  @Test
  fun `SHOULD return default coefficient ON getGameApplicationIdToEqualizerCoeff WHEN no entry`() = testScope.runTest {
    val actual = service.getGameApplicationIdToEqualizerCoeff("app-3")// cache check
    assertThat(actual).isEqualByComparingTo(BigDecimal.ONE)
  }
}