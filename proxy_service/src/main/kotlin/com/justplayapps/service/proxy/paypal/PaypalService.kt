package com.justplayapps.service.proxy.paypal

import com.google.gson.GsonBuilder
import com.google.inject.Inject
import com.google.inject.Provider
import com.google.inject.name.Named
import com.justplayapps.proxybase.PaymentAccounts
import com.justplayapps.proxybase.paypal.dto.PaypalRequestDto
import com.justplayapps.proxybase.paypal.dto.PaypalResponseDto
import com.justplayapps.proxybase.utils.ClassTypeAdapter
import com.justplayapps.service.proxy.CoreModule.Companion.PAYPAL_HTTP_CLIENT
import com.justplayapps.service.proxy.CoreModule.Companion.PAYPAL_HTTP_CLIENT_GMC
import com.justplayapps.service.proxy.CoreModule.Companion.PAYPAL_HTTP_CLIENT_JUSTPLAY
import com.justplayapps.service.proxy.CoreModule.Companion.PAYPAL_HTTP_CLIENT_USD
import com.justplayapps.service.proxy.paypal.override.AuthorizationProvider
import com.justplayapps.service.proxy.paypal.override.JustPlayPayPalHttpClient
import com.justplayapps.service.proxy.util.ApiResponse
import com.justplayapps.service.proxy.util.IoCoroutineScope
import com.justplayapps.service.proxy.util.getRequestFromBodyOrNull
import com.justplayapps.service.proxy.util.logger
import com.paypal.http.HttpRequest
import com.paypal.http.exceptions.HttpException
import io.ktor.application.*
import io.ktor.util.pipeline.*
import kotlinx.coroutines.withContext
import org.json.simple.JSONObject

class PaypalService @Inject constructor(
  @Named(PAYPAL_HTTP_CLIENT) private val payPalHttpClient: JustPlayPayPalHttpClient,
  @Named(PAYPAL_HTTP_CLIENT_JUSTPLAY) private val payPalHttpClientJustPlay: JustPlayPayPalHttpClient,
  @Named(PAYPAL_HTTP_CLIENT_USD) private val payPalHttpClientUsd: JustPlayPayPalHttpClient,
  @Named(PAYPAL_HTTP_CLIENT_GMC) private val payPalHttpClientGmc: JustPlayPayPalHttpClient,
  private val coroutineScope: Provider<IoCoroutineScope>,
) {

  private companion object {
    val gson = GsonBuilder()
      .registerTypeAdapter(Class::class.java, ClassTypeAdapter())
      .create()
  }

  private fun getPayPalHttpClient(account: PaymentAccounts) = when (account) {
    PaymentAccounts.PAYPAL_JP -> payPalHttpClientJustPlay
    PaymentAccounts.PAYPAL_USD -> payPalHttpClientUsd
    PaymentAccounts.PAYPAL_GMC -> payPalHttpClientGmc
    PaymentAccounts.PAYPAL_PS -> payPalHttpClient
    else -> throw RuntimeException("Invalid payment account $account.")
  }

  suspend fun prepareRequestData(pipelineContext: PipelineContext<Unit, ApplicationCall>): PaypalRequestDto? {
    with(pipelineContext) {
      return getRequestFromBodyOrNull()
    }
  }

  suspend fun redirectCall(paypalRequestDto: PaypalRequestDto?): ApiResponse<PaypalResponseDto, HttpException> {
    if (paypalRequestDto == null) {
      logger().error("Error parsing paypal request body.")
      return ApiResponse.Error(RuntimeException("Error parsing paypal request body."))
    }
    val httpRequest: HttpRequest<Any> = try {
      gson.fromJson(
        paypalRequestDto.request,
        HttpRequest::class.java
      ) as HttpRequest<Any>
    } catch (e: Exception) {
      logger().error("Unknown parsing exception", e)
      return ApiResponse.Error(e)
    }
    logger().info("Url: ${httpRequest.path()}")
    logger().info("Verb: ${httpRequest.verb()}")
    logger().info("Body: ${httpRequest.requestBody()}")
    if (paypalRequestDto.bodyClass != null) {
      val bodyClass = gson.fromJson(paypalRequestDto.bodyClass, Class::class.java)
      httpRequest.requestBody(
        gson.fromJson((JSONObject((httpRequest.requestBody() as Map<*, *>)).toString()), bodyClass)
      )
    }
    return withContext(coroutineScope.get().coroutineContext) {
      getPayPalHttpClient(paypalRequestDto.paymentAccounts).request(httpRequest)
    }
  }

  private fun JustPlayPayPalHttpClient.request(
    httpRequest: HttpRequest<Any>
  ): ApiResponse<PaypalResponseDto, HttpException> =
    try {
      val response = execute(httpRequest)
      ApiResponse.Success(
        PaypalResponseDto(
          response = gson.toJson(response),
          httpError = null
        )
      )
    } catch (exception: HttpException) {
      logger().error("HttpException", exception)
      if (exception.message?.contains("Access Token not found in cache") == true) {
        AuthorizationProvider.sharedInstance.clearCache()
      }
      ApiResponse.Success(
        PaypalResponseDto(
          response = null,
          httpError = gson.toJson(exception)
        )
      )
    } catch (e: Exception) {
      logger().error("Unknown Exception", e)
      ApiResponse.Error(e)
    }
}