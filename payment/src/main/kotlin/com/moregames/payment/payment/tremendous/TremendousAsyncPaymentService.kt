package com.moregames.payment.payment.tremendous

import com.google.inject.Inject
import com.google.inject.Provider
import com.justplayapps.proxybase.PaymentAccounts
import com.moregames.base.app.BuildVariant
import com.moregames.base.dto.Payment
import com.moregames.base.messaging.GenericMessagePublisher
import com.moregames.base.messaging.dto.TremendousPaymentCompletedEventDto
import com.moregames.base.util.IoCoroutineScope
import com.moregames.base.util.buildCache
import com.moregames.base.util.logger
import com.moregames.payment.payment.AsyncPaymentProviderService
import com.moregames.payment.payment.AsyncPaymentProviderService.PaymentStatusCheckResult
import com.moregames.payment.payment.tremendous.TremendousApiService.Companion.FUNDING_SOURCE_TYPE
import com.moregames.payment.payment.tremendous.dto.CreateOrderRequestDto.RewardDto.RewardDeliveryDto
import com.moregames.payment.payment.tremendous.dto.TremendousRewardDto
import com.paypal.payouts.Currency
import kotlinx.coroutines.async
import java.math.BigDecimal
import javax.inject.Singleton

@Singleton
class TremendousAsyncPaymentService @Inject constructor(
  private val tremendousApiService: TremendousApiService,
  private val tremendousPersistenceService: TremendousPersistenceService,
  private val coroutineScope: Provider<IoCoroutineScope>,
  private val genericMessagePublisher: GenericMessagePublisher,
  buildVariant: BuildVariant,
  private val tremendousPaymentProvidersService: TremendousPaymentProvidersService
) : AsyncPaymentProviderService {

  private companion object {
    const val CAMPAIGN_ID_BY_NAME_CACHE_MINUTES = 10L
    const val FUNDING_SOURCE_ID_CACHE_MINUTES = 10L
    const val STATUS_EXECUTED = "EXECUTED"
  }

  private val campaignIdByName = buildCache(buildVariant, CAMPAIGN_ID_BY_NAME_CACHE_MINUTES) { campaignName: String ->
    coroutineScope.get().async { tremendousApiService.getCampaignIdByName(campaignName = campaignName) }
  }
  private val fundingId = buildCache(buildVariant, FUNDING_SOURCE_ID_CACHE_MINUTES) { _: String ->
    coroutineScope.get().async { tremendousApiService.getFundingSource().id }
  }

  override fun providerName(): String = "Tremendous"

  override suspend fun orderPayment(payment: Payment): PaymentAccounts? {
    val productId = tremendousPaymentProvidersService.getTremendousProviderProductIdOrNull(payment)
    createOneProductOrMultiProductsOrder(productId, payment)
      .let { createOrderResult ->
        val order = createOrderResult.order
        val reward = createOrderResult.order.rewards.first()
        if (order.status != STATUS_EXECUTED) {
          throw IllegalStateException("Status of payment '${payment.cashoutTransactionId}' order '${order.id}' is ${order.status}. But 'EXECUTED' status is expected.")
        }
        if (reward.delivery.link == null) {
          logger().warn("Tremendous link for payment '${payment.cashoutTransactionId}' is empty")
        }
        tremendousPersistenceService.saveTremendousReward(
          TremendousRewardDto(
            cashoutTransactionId = payment.cashoutTransactionId,
            tremendousRewardId = reward.id,
            fee = BigDecimal.valueOf(order.payment.fees).setScale(2),
            link = reward.delivery.link
          )
        )
        genericMessagePublisher.publish(
          TremendousPaymentCompletedEventDto(
            id = reward.id,
            externalId = payment.cashoutTransactionId,
            status = order.status,
            subtotal = order.payment.subtotal.toBigDecimal(),
            total = order.payment.total.toBigDecimal(),
            fees = order.payment.fees.toBigDecimal(),
            createdAt = order.createdAt
          )
        )
      }
    return payment.account
  }

  private suspend fun createOneProductOrMultiProductsOrder(productId: String?, payment: Payment) =
    if (productId == null)
      tremendousApiService.createOrder(
        payment = payment,
        tremendousFundingSourceId = fundingId.get(FUNDING_SOURCE_TYPE).await(),
        tremendousCampaignId = campaignIdByName.get(payment.provider.name).await(),
        tremendousProductId = null,
        rewardDeliveryMethod = RewardDeliveryDto.email()
      )
    else
      tremendousApiService.createOrder(
        payment = payment,
        tremendousFundingSourceId = fundingId.get(FUNDING_SOURCE_TYPE).await(),
        tremendousCampaignId = null,
        tremendousProductId = productId,
        rewardDeliveryMethod = RewardDeliveryDto.email()
      )


  override suspend fun checkPaymentStatus(payment: Payment): PaymentStatusCheckResult =
    runCatching {
      tremendousPersistenceService.getTremendousRewardByCashoutTransactionId(payment.cashoutTransactionId).let { reward ->
        PaymentStatusCheckResult(
          paymentStatus = Payment.PaymentStatus.COMPLETED,
          paymentExternalTransactionId = reward.tremendousRewardId,
          claim = reward.link ?: tremendousApiService.generateRewardLink(reward.tremendousRewardId).also {
            tremendousPersistenceService.saveTremendousRewardLink(payment.cashoutTransactionId, it)
          },
          fee = Currency().currency("USD")
            .value(reward.fee.toPlainString()) // Fees are always in USD: https://developers.tremendous.com/reference/obj-schema-orders
        )
      }
    }.getOrElse { ex ->
      throw IllegalStateException("Failed to retrieve payment for cashoutTransactionId: ${payment.cashoutTransactionId}", ex)
    }


}