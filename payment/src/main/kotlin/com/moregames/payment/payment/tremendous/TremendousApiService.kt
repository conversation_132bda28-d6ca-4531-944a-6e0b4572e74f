package com.moregames.payment.payment.tremendous

import com.google.inject.Inject
import com.google.inject.Provider
import com.google.inject.name.Named
import com.moregames.base.app.BuildVariant
import com.moregames.base.dto.Payment
import com.moregames.base.encryption.EncryptionService
import com.moregames.base.externalip.ExternalIpService
import com.moregames.base.secret.SecretService
import com.moregames.base.util.logger
import com.moregames.payment.app.CoreModule.Companion.TREMENDOUS_HTTP_CLIENT
import com.moregames.payment.app.CoreModule.Companion.TREMENDOUS_RETRY_CONFIG
import com.moregames.payment.app.PaymentSecrets
import com.moregames.payment.payment.tremendous.dto.*
import com.moregames.payment.payment.tremendous.dto.CreateOrderRequestDto.PaymentDto
import com.moregames.payment.payment.tremendous.dto.CreateOrderRequestDto.RewardDto
import com.moregames.payment.payment.tremendous.dto.CreateOrderRequestDto.RewardDto.*
import com.moregames.payment.payment.tremendous.dto.ListProductsResponseDto.ProductDto
import io.github.resilience4j.kotlin.retry.executeSuspendFunction
import io.ktor.client.*
import io.ktor.client.request.*
import io.ktor.http.*

class TremendousApiService @Inject constructor(
  private val secretService: SecretService,
  @Named(TREMENDOUS_HTTP_CLIENT) private val httpClient: HttpClient,
  private val buildVariantProvider: Provider<BuildVariant>,
  private val tremendousPersistenceService: TremendousPersistenceService,
  private val encryptionService: EncryptionService,
  private val externalIpService: ExternalIpService,
  @Named(TREMENDOUS_RETRY_CONFIG) private val retry: TremendousRetry
) {

  companion object {
    const val SANDBOX_API_ROOT_URL = "https://testflight.tremendous.com/api/v2"
    const val PROD_API_ROOT_URL = "https://www.tremendous.com/api/v2"
    const val FUNDING_SOURCE_TYPE = "balance"
  }

  suspend fun createOrder(
    payment: Payment,
    tremendousFundingSourceId: String,
    tremendousCampaignId: String?,
    tremendousProductId: String? = null,
    rewardDeliveryMethod: RewardDeliveryDto
  ): CreateOrderResponseDto {
    val name = encryptionService.decryptOrEmpty(payment.encryptedRecipientName)
    val email = encryptionService.decryptOrEmpty(payment.encryptedRecipientEmail)
    return retry.executeSuspendFunction {
      kotlin.runCatching {
        httpClient.post<CreateOrderResponseDto>(
          "${getApiRoot()}/orders"
        ) {
          header(HttpHeaders.ContentType, "application/json")
          addDefaultHeaders()
          body = CreateOrderRequestDto(
            external_id = payment.cashoutTransactionId,
            payment = PaymentDto(funding_source_id = tremendousFundingSourceId),
            reward = RewardDto(
              campaign_id = tremendousCampaignId,
              value = RewardValueDto(
                denomination = payment.amount,
                currency_code = payment.currencyCode
              ),
              recipient = RewardRecipientDto(
                name = name,
                email = email
              ),
              delivery = rewardDeliveryMethod,
              products = tremendousProductId?.let { listOf(it) }
            )
          )
        }
      }.onFailure { e ->
        logger().error("[External IP: ${externalIpService.getExternalIp()}] Unexpected error occurred when trying to create order", e)
      }.getOrThrow()
    }
  }

  suspend fun generateRewardLink(rewardId: String): String =
    kotlin.runCatching {
      httpClient.post<GetRewardLinkResponseDto>(
        "${getApiRoot()}/rewards/$rewardId/generate_link"
      ) {
        addDefaultHeaders()
      }.reward.link
    }.onFailure { e ->
      logger().error("[External IP: ${externalIpService.getExternalIp()}] Unexpected error occurred when trying to get reward ling by ID: $rewardId", e)
    }.getOrThrow()

  suspend fun getProductCatalog(): List<ProductDto> =
    retry.executeSuspendFunction {
      kotlin.runCatching {
        httpClient.get<ListProductsResponseDto>(urlString = "${getApiRoot()}/products") {
          addDefaultHeaders()
        }.products
      }.onFailure { e ->
        logger().error("[External IP: ${externalIpService.getExternalIp()}] Unexpected error occurred when trying to get product catalog", e)
      }.getOrThrow()
    }

  suspend fun getFundingSource(): ListFundingSourcesResponseDto.FundingSourceDto =
    runCatching {
      httpClient.get<ListFundingSourcesResponseDto>(
        "${getApiRoot()}/funding_sources"
      ) {
        addDefaultHeaders()
      }.funding_sources.firstOrNull { it.method == FUNDING_SOURCE_TYPE }
        ?: throw IllegalStateException("Can't find funding source with type '$FUNDING_SOURCE_TYPE'")
    }.onFailure { e ->
      logger().error("[External IP: ${externalIpService.getExternalIp()}] Unexpected error occurred when trying to get funding source", e)
    }.getOrThrow()

  suspend fun getCampaignIdByName(campaignName: String): String =
    runCatching {
      httpClient.get<ListCampaignsResponseDto>(
        "${getApiRoot()}/campaigns"
      ) {
        addDefaultHeaders()
      }.campaigns.firstOrNull { it.name == campaignName }?.id
        ?: throw IllegalStateException("Can't find campaign with name '$campaignName'")
    }.onFailure { e ->
      logger().error("[External IP: ${externalIpService.getExternalIp()}] Unexpected error occurred when trying to get campaign by name: $campaignName", e)
    }.getOrThrow()


  suspend fun resendReward(cashoutTransactionId: String): Boolean {
    val rewardId = tremendousPersistenceService.getTremendousRewardByCashoutTransactionId(cashoutTransactionId).tremendousRewardId
    if (rewardId.isEmpty()) {
      logger().warn("Could not find rewardId for cashoutCashoutTransactionId: $cashoutTransactionId")
      return false
    }
    val result = runCatching {
      httpClient.post<ResendRewardResponseDto>("${getApiRoot()}/rewards/$rewardId/resend") {
        addDefaultHeaders()
      }
    }.onFailure { e ->
      logger().error("[External IP: ${externalIpService.getExternalIp()}] Unexpected error occurred when trying to resend reward with ID: $rewardId", e)
      return false
    }.getOrNull()

    result?.errors?.let {
      throw IllegalStateException("Can't resend reward with ID $rewardId: ${it.message}")
    }

    return true
  }


  private suspend fun HttpRequestBuilder.addDefaultHeaders() {
    header(HttpHeaders.Accept, "application/json")
    header(HttpHeaders.Authorization, "Bearer ${secretService.secretValue(apiKeySecret())}")
  }

  private fun apiKeySecret() =
    if (buildVariantProvider.get() == BuildVariant.PRODUCTION) PaymentSecrets.TREMENDOUS_API_KEY_LIVE
    else PaymentSecrets.TREMENDOUS_API_KEY_SANDBOX

  private fun getApiRoot() =
    if (buildVariantProvider.get() == BuildVariant.PRODUCTION) PROD_API_ROOT_URL
    else SANDBOX_API_ROOT_URL
}