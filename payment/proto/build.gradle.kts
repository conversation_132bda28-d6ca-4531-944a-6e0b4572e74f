import com.google.protobuf.gradle.id

plugins {
  kotlin("jvm")
  `java-library`
  alias(libs.plugins.protobuf)
  idea
}

idea {
  module {
    generatedSourceDirs.add(file("build/generated/source/proto/main"))
  }
}

repositories {
  mavenCentral()
}

dependencies {
  api(project(":base:proto"))
}

protobuf {
  protoc {
    artifact = libs.protobuf.protoc.get().toString()
  }
  plugins {
    id("grpc") {
      artifact = libs.grpc.gen.java.get().toString()
    }
    id("grpckt") {
      artifact = libs.grpc.gen.kotlin.get().toString() + ":jdk8@jar"
    }
  }
  generateProtoTasks {
    all().forEach { task ->
      task.plugins {
        create("grpc")
        create("grpckt")
      }
      task.builtins {
        create("kotlin")
      }
    }
  }
}